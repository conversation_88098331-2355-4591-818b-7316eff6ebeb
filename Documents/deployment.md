# Deployment Diagram

The following diagram shows the VideoPlatform components deployed on the QA subscription on July 2022, along with the shared components in use in the NBA Shared subscription.

## Notes

- The reason to have more than one resource group (`vidplat` and `vdopen` domains) is to differentiate resources that need a open security policy to overcome a limitation that prevents azure functions to be triggered by event grid. The 'open' resource group is exempt from that security policy
- The reason to deploy the playout service on its own resource group is to avoid confusion with the permanent resources in the videplatform solution. The playout service creates and destroys container instances permanently
- The diagram doesn't include the complete list of shared resources, the ones mentioned in the diagram (at high level) are only the currently used by the system

![deployment](./media/deployment.drawio.png)
