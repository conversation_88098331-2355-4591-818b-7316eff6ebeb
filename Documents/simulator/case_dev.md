# Live Video Simulator - <PERSON><PERSON><PERSON><PERSON><PERSON> use case

As a developer, I want to be able to trigger workflows in the Orchestrator, so that I can test that the orchestration and its related actors/microservices are working properly.

In order to do that, the simulator will expose two endpoints:

- **[GET] /api/infrastructure/channel/{video-platform-channel-id}/**: retrieves the VideoPlatformChannel by its id.
- **[POST] /api/infrastructure/channel/{video-platform-channel-id}/simulation/start**: Starts the simulation of a workflow using a VideoPlatformChannelId, and it returns **SimulationId** (for now, this value is not useful at all)
    - Payload: 
```json
    {
        "WorkflowId": "workflowId", //See NbaWorkflowIds (just after this JSON)
        "ChannelId": "VideoPlatformChannelId"
    }
```

You can check all available workflows here: [NbaWorkflowIds.cs](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FCommon%2FNbaWorkflowIds.cs)




####Simulator remarks
- This two endpoints must be fronted by APIM as every other simulator endpoint.
- Not all the channels should be available for simulation. For this very first version, we've defined the **ISimulatorChannelsRepository interface** in order to get the channel ids availables for simulation. We've implemented a repository which returns a list of ids that can be defined as app settings (again, very first version :)). The configuration should look as following:
```json
"SimulationChannelsOptions": {
    "ChannelsForSimulation": [
      "VideoPlatformChannelId1",
      "VideoPlatformChannelId2",
    ]
  }
```

####Powershell scripts
We will create PS Scripts in order to hit those endpoints, nothing very much different as we did for the Simulator Backend scaffolding.


####Orchestration remarks
The orchestrator receives messages in the WorkflowRequestQueue. The payload of that message contains the following:


- **ScheduleId**: schedule that triggers this change. On the real world, this is supposed to come from the Scheduler, which is the microservice that requests for workflow execution given games and events schedules. **Orchestrator is not using this value for anything, so for now we are assigning ScheduleI. Non-blocking question for them: is the orchestrator going to use this value to make decisions, or just for traceability?**

- **WorkflowIntent**: Description of the workflow to be executed. It contains:
    - **WorkflowId**: Id of the workflow to be executed. So far Start and Stop (see [NbaWorkflowIds](https://dev.azure.com/nbadev/DTC/_git/VideoPlatform?path=%2Fsrc%2FShared%2FNBA.NextGen.VideoPlatform.Shared.Domain%2FCommon%2FNbaWorkflowIds.cs&version=GBmain&_a=contents))
    - **ChannelId**: Id of the VideoPlatformChannel, we get this from the InfrastructureStore (VideoPlatformChannelRepository)
    - **ActorSpecificDetails**: List of details to be provided to specific actors, they are not using it so far. It contains a list of objects as following:
        - **ActorId**: the actor id to receive de details
        - **Data**: object with data to be provided to that specific actor
    - **LiveEventTime**: not needed so far 