{"info": {"_postman_id": "af578637-bef9-4a52-9ed4-d8b3b8aa0f4f", "name": "NextGen Simulator", "description": "NextGen Simulator Web Api\n\nContact Support:\n Name: Placeholder Name\n Email: <EMAIL>", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "api/Simulation", "item": [{"name": "channel/{channel Id}", "item": [{"name": "Gets the channel asynchronous.", "request": {"method": "GET", "header": [{"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/api/Simulation/channel/:channelId", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "channel", ":channelId"], "variable": [{"key": "channelId", "value": "aute officia <PERSON>", "description": "(Required) The VideoPlatformChannel identifier."}]}}, "response": [{"name": "The VideoPlatformChannel.", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Simulation/channel/:channelId", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "channel", ":channelId"], "variable": [{"key": "channelId", "value": "aute officia <PERSON>", "description": "(Required) The VideoPlatformChannel identifier."}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"primaryFeed\": false,\n \"concurrencyToken\": \"aute sunt ut sed\",\n \"id\": \"qui eu nostrud\",\n \"type\": \"commodo\",\n \"name\": \"aliquip ex aliqua\",\n \"liveEventId\": \"aliqua incididunt\",\n \"properties\": {},\n \"operationalState\": \"non sint ea aliqua\",\n \"videoPlatformActors\": [\n  {\n   \"id\": \"dolor occaecat\",\n   \"name\": \"laboris cupidatat ullamco\",\n   \"actorInfrastructureId\": \"anim\",\n   \"actorInfrastructureName\": \"Lorem\"\n  },\n  {\n   \"id\": \"do\",\n   \"name\": \"sit voluptate\",\n   \"actorInfrastructureId\": \"sed sint culpa pariatur\",\n   \"actorInfrastructureName\": \"non dolore\"\n  }\n ]\n}"}, {"name": "Untitled Example", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/Simulation/channel/:channelId", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "channel", ":channelId"], "variable": [{"key": "channelId", "value": "aute officia <PERSON>", "description": "(Required) The VideoPlatformChannel identifier."}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Gets the channel asynchronous.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"continueOnError\": false,\n    \"channelId\": \"Lorem aute ea nulla dolore\",\n    \"workFlowId\": \"et in\",\n    \"actorSpecificDetails\": [\n        {\n            \"actorId\": \"consectetur eu laboris\",\n            \"data\": {}\n        },\n        {\n            \"actorId\": \"ea quis in officia minim\",\n            \"data\": {}\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/channel/:channelId/simulation/start", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "channel", ":channelId", "simulation", "start"], "variable": [{"key": "channelId", "value": "aute officia <PERSON>", "description": "(Required) The channel identifier."}]}}, "response": [{"name": "The simulation id.", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"continueOnError\": false,\n    \"channelId\": \"aute do\",\n    \"workFlowId\": \"enim\",\n    \"actorSpecificDetails\": [\n        {\n            \"actorId\": \"in non\",\n            \"data\": {}\n        },\n        {\n            \"actorId\": \"dolore in dolore ut\",\n            \"data\": {}\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/channel/:channelId/simulation/start", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "channel", ":channelId", "simulation", "start"], "variable": [{"key": "channelId", "value": "aute officia <PERSON>", "description": "(Required) The channel identifier."}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "\"aute officia Lorem\""}, {"name": "Untitled Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"continueOnError\": false,\n    \"channelId\": \"aute do\",\n    \"workFlowId\": \"enim\",\n    \"actorSpecificDetails\": [\n        {\n            \"actorId\": \"in non\",\n            \"data\": {}\n        },\n        {\n            \"actorId\": \"dolore in dolore ut\",\n            \"data\": {}\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/channel/:channelId/simulation/start", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "channel", ":channelId", "simulation", "start"], "variable": [{"key": "channelId", "value": "aute officia <PERSON>", "description": "(Required) The channel identifier."}]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}, {"name": "Ingests a GMS game into the orchestration.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"dateTime\": \"1975-07-19T05:58:57.485Z\",\n    \"season\": 92728889,\n    \"awayTeam\": {\n        \"id\": 52073381,\n        \"allStar\": false,\n        \"leagueTeam\": false,\n        \"code\": \"in ullamco amet\",\n        \"name\": \"nostrud Excepteur\",\n        \"city\": \"dolor\",\n        \"abbr\": \"ipsum\",\n        \"conference\": \"dolore\",\n        \"division\": \"ut Lorem fugiat\",\n        \"defaultLocationId\": 34431381.156873286\n    },\n    \"homeTeam\": {\n        \"id\": 65927982,\n        \"allStar\": false,\n        \"leagueTeam\": true,\n        \"code\": \"velit\",\n        \"name\": \"voluptate\",\n        \"city\": \"eu proident\",\n        \"abbr\": \"dolore ipsum\",\n        \"conference\": \"sit proident pariatur do irure\",\n        \"division\": \"sunt laborum\",\n        \"defaultLocationId\": 24054630.39561835\n    },\n    \"locationConfiguration\": {\n        \"active\": false,\n        \"capacity\": -72830255.0636292,\n        \"city\": \"quis mollit cillum Duis\",\n        \"countryCode\": \"Duis consequat magna \",\n        \"locationId\": \"eiusmod voluptate\",\n        \"locationType\": \"ullamco veniam aliquip est dolore\",\n        \"name\": \"culpa voluptate\",\n        \"postal\": \"Lorem quis non amet cillum\",\n        \"stateOrProvince\": \"sit quis occaecat eiusmod\",\n        \"street\": \"ipsum deserunt\",\n        \"timeZone\": \"cupidatat\"\n    },\n    \"mediaConfigurations\": [\n        {\n            \"id\": 9378862,\n            \"distributionName\": \"exercitation\",\n            \"encoder\": \"amet consectetur in tem\",\n            \"language\": \"cupidatat reprehenderit\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"magna proident\",\n                    \"value\": \"cupidatat ullamco exercitation\"\n                },\n                {\n                    \"key\": \"sed id\",\n                    \"value\": \"aute do velit\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": -18589119,\n                \"name\": \"in\"\n            },\n            \"name\": \"dolore magna cillum fugiat\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"velit dolor adipisicing proident in\",\n                    \"value\": \"consequat deserunt veniam\"\n                },\n                {\n                    \"key\": \"ut in\",\n                    \"value\": \"incididunt pariatur dolore\"\n                }\n            ],\n            \"region\": {\n                \"id\": -89788038,\n                \"name\": \"irure qui sunt\"\n            },\n            \"resolution\": \"ut aliquip\",\n            \"teamContext\": \"commodo exercitation labore\"\n        },\n        {\n            \"id\": 55754336,\n            \"distributionName\": \"anim pariatur\",\n            \"encoder\": \"sit mollit commodo\",\n            \"language\": \"aliquip deserunt dolore\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"sunt fugiat in Lorem culpa\",\n                    \"value\": \"officia esse\"\n                },\n                {\n                    \"key\": \"culpa\",\n                    \"value\": \"dolor consectetur qui in\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": -4315251,\n                \"name\": \"in adipi\"\n            },\n            \"name\": \"non ut consectetur\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"commodo amet et Lorem mollit\",\n                    \"value\": \"eiusmod exercitation \"\n                },\n                {\n                    \"key\": \"et consectetur\",\n                    \"value\": \"proident\"\n                }\n            ],\n            \"region\": {\n                \"id\": 77207618,\n                \"name\": \"irure culpa sit ut\"\n            },\n            \"resolution\": \"pariatur ipsum\",\n            \"teamContext\": \"nulla\"\n        }\n    ],\n    \"scheduleCode\": \"labore tempor nisi adi\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/GmsGame/ingest", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsGame", "ingest"]}}, "response": [{"name": "The GMS game ingested.", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"dateTime\": \"2021-10-30T02:08:35.827Z\",\n    \"season\": 91223832,\n    \"awayTeam\": {\n        \"id\": 55301467,\n        \"allStar\": true,\n        \"leagueTeam\": false,\n        \"code\": \"ex tempor ullamco\",\n        \"name\": \"in cul\",\n        \"city\": \"nulla D\",\n        \"abbr\": \"aliqua adipisicing dolore ad cupidatat\",\n        \"conference\": \"velit culpa pariatur elit\",\n        \"division\": \"eu amet cillum ut\",\n        \"defaultLocationId\": 95359042.87401423\n    },\n    \"homeTeam\": {\n        \"id\": 30744485,\n        \"allStar\": false,\n        \"leagueTeam\": true,\n        \"code\": \"esse qui velit occaecat cupidatat\",\n        \"name\": \"tempor quis nulla exercitation\",\n        \"city\": \"aliquip amet in pariatur\",\n        \"abbr\": \"ullamco ut in proident\",\n        \"conference\": \"u\",\n        \"division\": \"Excepteur ut exercitation pariat\",\n        \"defaultLocationId\": 55187435.78405574\n    },\n    \"locationConfiguration\": {\n        \"active\": true,\n        \"capacity\": 3212355.884725675,\n        \"city\": \"incididunt et dolore cupidatat\",\n        \"countryCode\": \"ut eiusmod\",\n        \"locationId\": \"laboris consectetur\",\n        \"locationType\": \"ea id adipisicing laborum reprehenderit\",\n        \"name\": \"aliqua ut do Duis minim\",\n        \"postal\": \"ullamco eu deserunt\",\n        \"stateOrProvince\": \"consequat dolor enim culpa\",\n        \"street\": \"Duis tempor voluptate anim\",\n        \"timeZone\": \"non cillum ut consequat\"\n    },\n    \"mediaConfigurations\": [\n        {\n            \"id\": -9626904,\n            \"distributionName\": \"aute sit\",\n            \"encoder\": \"anim id\",\n            \"language\": \"est consequat\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"culpa ea exercitation esse\",\n                    \"value\": \"dolore elit in\"\n                },\n                {\n                    \"key\": \"sit dolor magna enim\",\n                    \"value\": \"aliquip elit commodo sed\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": -4409421,\n                \"name\": \"anim\"\n            },\n            \"name\": \"commodo minim proident\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"ut sed ea\",\n                    \"value\": \"labore occaecat anim ea velit\"\n                },\n                {\n                    \"key\": \"consequat\",\n                    \"value\": \"minim mollit labore non\"\n                }\n            ],\n            \"region\": {\n                \"id\": -88651170,\n                \"name\": \"magna cupidatat\"\n            },\n            \"resolution\": \"do\",\n            \"teamContext\": \"est voluptate reprehenderit dolor aute\"\n        },\n        {\n            \"id\": -59819875,\n            \"distributionName\": \"sed\",\n            \"encoder\": \"ut\",\n            \"language\": \"quis Excepteur occaecat in\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"cupidatat qui nisi\",\n                    \"value\": \"cupidatat consectetur ut sint\"\n                },\n                {\n                    \"key\": \"adipisicing ea ipsum\",\n                    \"value\": \"aute irure elit laboris\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": 3455563,\n                \"name\": \"dolore sed dolor sunt exercitation\"\n            },\n            \"name\": \"laborum mollit\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"amet ex Ut\",\n                    \"value\": \"culpa dese\"\n                },\n                {\n                    \"key\": \"eiusmod fugiat\",\n                    \"value\": \"nulla laborum ut aliqua\"\n                }\n            ],\n            \"region\": {\n                \"id\": -17552451,\n                \"name\": \"consectetur\"\n            },\n            \"resolution\": \"sint Duis\",\n            \"teamContext\": \"ex magna\"\n        }\n    ],\n    \"scheduleCode\": \"dolore sint\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/GmsGame/ingest", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsGame", "ingest"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}, {"name": "Untitled Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"dateTime\": \"2021-10-30T02:08:35.827Z\",\n    \"season\": 91223832,\n    \"awayTeam\": {\n        \"id\": 55301467,\n        \"allStar\": true,\n        \"leagueTeam\": false,\n        \"code\": \"ex tempor ullamco\",\n        \"name\": \"in cul\",\n        \"city\": \"nulla D\",\n        \"abbr\": \"aliqua adipisicing dolore ad cupidatat\",\n        \"conference\": \"velit culpa pariatur elit\",\n        \"division\": \"eu amet cillum ut\",\n        \"defaultLocationId\": 95359042.87401423\n    },\n    \"homeTeam\": {\n        \"id\": 30744485,\n        \"allStar\": false,\n        \"leagueTeam\": true,\n        \"code\": \"esse qui velit occaecat cupidatat\",\n        \"name\": \"tempor quis nulla exercitation\",\n        \"city\": \"aliquip amet in pariatur\",\n        \"abbr\": \"ullamco ut in proident\",\n        \"conference\": \"u\",\n        \"division\": \"Excepteur ut exercitation pariat\",\n        \"defaultLocationId\": 55187435.78405574\n    },\n    \"locationConfiguration\": {\n        \"active\": true,\n        \"capacity\": 3212355.884725675,\n        \"city\": \"incididunt et dolore cupidatat\",\n        \"countryCode\": \"ut eiusmod\",\n        \"locationId\": \"laboris consectetur\",\n        \"locationType\": \"ea id adipisicing laborum reprehenderit\",\n        \"name\": \"aliqua ut do Duis minim\",\n        \"postal\": \"ullamco eu deserunt\",\n        \"stateOrProvince\": \"consequat dolor enim culpa\",\n        \"street\": \"Duis tempor voluptate anim\",\n        \"timeZone\": \"non cillum ut consequat\"\n    },\n    \"mediaConfigurations\": [\n        {\n            \"id\": -9626904,\n            \"distributionName\": \"aute sit\",\n            \"encoder\": \"anim id\",\n            \"language\": \"est consequat\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"culpa ea exercitation esse\",\n                    \"value\": \"dolore elit in\"\n                },\n                {\n                    \"key\": \"sit dolor magna enim\",\n                    \"value\": \"aliquip elit commodo sed\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": -4409421,\n                \"name\": \"anim\"\n            },\n            \"name\": \"commodo minim proident\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"ut sed ea\",\n                    \"value\": \"labore occaecat anim ea velit\"\n                },\n                {\n                    \"key\": \"consequat\",\n                    \"value\": \"minim mollit labore non\"\n                }\n            ],\n            \"region\": {\n                \"id\": -88651170,\n                \"name\": \"magna cupidatat\"\n            },\n            \"resolution\": \"do\",\n            \"teamContext\": \"est voluptate reprehenderit dolor aute\"\n        },\n        {\n            \"id\": -59819875,\n            \"distributionName\": \"sed\",\n            \"encoder\": \"ut\",\n            \"language\": \"quis Excepteur occaecat in\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"cupidatat qui nisi\",\n                    \"value\": \"cupidatat consectetur ut sint\"\n                },\n                {\n                    \"key\": \"adipisicing ea ipsum\",\n                    \"value\": \"aute irure elit laboris\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": 3455563,\n                \"name\": \"dolore sed dolor sunt exercitation\"\n            },\n            \"name\": \"laborum mollit\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"amet ex Ut\",\n                    \"value\": \"culpa dese\"\n                },\n                {\n                    \"key\": \"eiusmod fugiat\",\n                    \"value\": \"nulla laborum ut aliqua\"\n                }\n            ],\n            \"region\": {\n                \"id\": -17552451,\n                \"name\": \"consectetur\"\n            },\n            \"resolution\": \"sint Duis\",\n            \"teamContext\": \"ex magna\"\n        }\n    ],\n    \"scheduleCode\": \"dolore sint\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/GmsGame/ingest", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsGame", "ingest"]}}, "status": "Bad Request", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n \"type\": \"ad\",\n \"title\": \"culpa\",\n \"status\": 20004609,\n \"detail\": \"anim est sunt labore quis\",\n \"instance\": \"est\"\n}"}, {"name": "Untitled Example", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"dateTime\": \"2021-10-30T02:08:35.827Z\",\n    \"season\": 91223832,\n    \"awayTeam\": {\n        \"id\": 55301467,\n        \"allStar\": true,\n        \"leagueTeam\": false,\n        \"code\": \"ex tempor ullamco\",\n        \"name\": \"in cul\",\n        \"city\": \"nulla D\",\n        \"abbr\": \"aliqua adipisicing dolore ad cupidatat\",\n        \"conference\": \"velit culpa pariatur elit\",\n        \"division\": \"eu amet cillum ut\",\n        \"defaultLocationId\": 95359042.87401423\n    },\n    \"homeTeam\": {\n        \"id\": 30744485,\n        \"allStar\": false,\n        \"leagueTeam\": true,\n        \"code\": \"esse qui velit occaecat cupidatat\",\n        \"name\": \"tempor quis nulla exercitation\",\n        \"city\": \"aliquip amet in pariatur\",\n        \"abbr\": \"ullamco ut in proident\",\n        \"conference\": \"u\",\n        \"division\": \"Excepteur ut exercitation pariat\",\n        \"defaultLocationId\": 55187435.78405574\n    },\n    \"locationConfiguration\": {\n        \"active\": true,\n        \"capacity\": 3212355.884725675,\n        \"city\": \"incididunt et dolore cupidatat\",\n        \"countryCode\": \"ut eiusmod\",\n        \"locationId\": \"laboris consectetur\",\n        \"locationType\": \"ea id adipisicing laborum reprehenderit\",\n        \"name\": \"aliqua ut do Duis minim\",\n        \"postal\": \"ullamco eu deserunt\",\n        \"stateOrProvince\": \"consequat dolor enim culpa\",\n        \"street\": \"Duis tempor voluptate anim\",\n        \"timeZone\": \"non cillum ut consequat\"\n    },\n    \"mediaConfigurations\": [\n        {\n            \"id\": -9626904,\n            \"distributionName\": \"aute sit\",\n            \"encoder\": \"anim id\",\n            \"language\": \"est consequat\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"culpa ea exercitation esse\",\n                    \"value\": \"dolore elit in\"\n                },\n                {\n                    \"key\": \"sit dolor magna enim\",\n                    \"value\": \"aliquip elit commodo sed\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": -4409421,\n                \"name\": \"anim\"\n            },\n            \"name\": \"commodo minim proident\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"ut sed ea\",\n                    \"value\": \"labore occaecat anim ea velit\"\n                },\n                {\n                    \"key\": \"consequat\",\n                    \"value\": \"minim mollit labore non\"\n                }\n            ],\n            \"region\": {\n                \"id\": -88651170,\n                \"name\": \"magna cupidatat\"\n            },\n            \"resolution\": \"do\",\n            \"teamContext\": \"est voluptate reprehenderit dolor aute\"\n        },\n        {\n            \"id\": -59819875,\n            \"distributionName\": \"sed\",\n            \"encoder\": \"ut\",\n            \"language\": \"quis Excepteur occaecat in\",\n            \"keyValuePairs\": [\n                {\n                    \"key\": \"cupidatat qui nisi\",\n                    \"value\": \"cupidatat consectetur ut sint\"\n                },\n                {\n                    \"key\": \"adipisicing ea ipsum\",\n                    \"value\": \"aute irure elit laboris\"\n                }\n            ],\n            \"mediaType\": {\n                \"id\": 3455563,\n                \"name\": \"dolore sed dolor sunt exercitation\"\n            },\n            \"name\": \"laborum mollit\",\n            \"operationsKeyValuePairs\": [\n                {\n                    \"key\": \"amet ex Ut\",\n                    \"value\": \"culpa dese\"\n                },\n                {\n                    \"key\": \"eiusmod fugiat\",\n                    \"value\": \"nulla laborum ut aliqua\"\n                }\n            ],\n            \"region\": {\n                \"id\": -17552451,\n                \"name\": \"consectetur\"\n            },\n            \"resolution\": \"sint Duis\",\n            \"teamContext\": \"ex magna\"\n        }\n    ],\n    \"scheduleCode\": \"dolore sint\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/GmsGame/ingest", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsGame", "ingest"]}}, "status": "Internal Server Error", "code": 500, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Sends the queue message to request schedule change.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"deleteVideoPlatformSchedule\": false,\n    \"correlationId\": \"exercitation in\",\n    \"requestId\": \"do laborum\",\n    \"requestorActorId\": \"laborum proident\",\n    \"longRunningOperationId\": \"exercitation dolore proi\",\n    \"existingScheduleId\": \"dolore aute\",\n    \"requestorId\": \"aliqua exercitation\",\n    \"requestorIdentity\": \"sed enim anim\",\n    \"requestorLiveEventId\": \"Lorem amet nisi deserunt ad\",\n    \"requestorEventType\": \"voluptate Lorem\",\n    \"requestorLiveEventScheduleId\": \"occae\",\n    \"workflowIntents\": [\n        {\n            \"liveEventTime\": \"1980-08-11T02:49:17.519Z\",\n            \"continueOnError\": true,\n            \"workflowId\": \"officia in\",\n            \"channelId\": \"veniam\",\n            \"actorSpecificDetails\": [\n                {\n                    \"actorId\": \"sunt incididunt cupidatat\",\n                    \"data\": {}\n                },\n                {\n                    \"actorId\": \"sint nulla\",\n                    \"data\": {}\n                }\n            ]\n        },\n        {\n            \"liveEventTime\": \"1953-03-30T05:39:50.534Z\",\n            \"continueOnError\": false,\n            \"workflowId\": \"laboris adipisici\",\n            \"channelId\": \"ea e\",\n            \"actorSpecificDetails\": [\n                {\n                    \"actorId\": \"laboris\",\n                    \"data\": {}\n                },\n                {\n                    \"actorId\": \"eiusmod\",\n                    \"data\": {}\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/RequestScheduleChange", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "RequestScheduleChange"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"deleteVideoPlatformSchedule\": true,\n    \"correlationId\": \"Excepteur reprehenderit incididunt ea fugiat\",\n    \"requestId\": \"in cillum nulla enim Excepteur\",\n    \"requestorActorId\": \"quis Excepteur veniam\",\n    \"longRunningOperationId\": \"irure commodo ea\",\n    \"existingScheduleId\": \"commodo\",\n    \"requestorId\": \"et pariatur voluptate cillum\",\n    \"requestorIdentity\": \"voluptate do in anim\",\n    \"requestorLiveEventId\": \"do\",\n    \"requestorEventType\": \"enim amet\",\n    \"requestorLiveEventScheduleId\": \"Ut qui incididunt velit\",\n    \"workflowIntents\": [\n        {\n            \"liveEventTime\": \"2017-07-22T07:14:53.918Z\",\n            \"continueOnError\": false,\n            \"workflowId\": \"consequat pariatur\",\n            \"channelId\": \"deserunt in\",\n            \"actorSpecificDetails\": [\n                {\n                    \"actorId\": \"minim\",\n                    \"data\": {}\n                },\n                {\n                    \"actorId\": \"ex Excepteur ea occaecat pariatur\",\n                    \"data\": {}\n                }\n            ]\n        },\n        {\n            \"liveEventTime\": \"1942-05-23T18:51:04.455Z\",\n            \"continueOnError\": true,\n            \"workflowId\": \"fugiat minim\",\n            \"channelId\": \"Lorem est cillum\",\n            \"actorSpecificDetails\": [\n                {\n                    \"actorId\": \"au\",\n                    \"data\": {}\n                },\n                {\n                    \"actorId\": \"ad \",\n                    \"data\": {}\n                }\n            ]\n        }\n    ]\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/RequestScheduleChange", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "RequestScheduleChange"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Send the topic event that the GMS game has changed.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type\": 0,\n    \"contentChangeType\": 1,\n    \"forceReingestion\": true,\n    \"correlationId\": \"anim do velit\",\n    \"id\": \"magna quis consequat\",\n    \"dateTime\": \"2008-12-22T06:34:20.967Z\",\n    \"tournamentSeasonId\": \"exercitation magna ad Lorem\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/GmsGameChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsGameChanged"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Simulation/GmsGameChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsGameChanged"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Send the topic event that the GMS event has changed.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type\": 0,\n    \"contentChangeType\": 1,\n    \"forceReingestion\": true,\n    \"correlationId\": \"anim do velit\",\n    \"id\": \"magna quis consequat\",\n    \"dateTime\": \"2008-12-22T06:34:20.967Z\",\n    \"tournamentSeasonId\": \"exercitation magna ad Lorem\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/GmsEventChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsEventChanged"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Simulation/GmsEventChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "GmsEventChanged"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Send the topic event that the Aquilla channel has changed.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type\": 2,\n    \"correlationId\": \"occaecat est amet\",\n    \"id\": \"ex officia\",\n    \"state\": \"exercitation elit\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/AquilaChannelChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "AquilaChannelChanged"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Simulation/AquilaChannelChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "AquilaChannelChanged"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Send the topic event that the Aquilla source has changed.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type\": 2,\n    \"correlationId\": \"occaecat est amet\",\n    \"id\": \"ex officia\",\n    \"state\": \"exercitation elit\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/AquilaSourceChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "AquilaSourceChanged"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Simulation/AquilaSourceChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "AquilaSourceChanged"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Send the topic event that the <PERSON><PERSON><PERSON> whitelist has changed.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"type\": 2,\n    \"correlationId\": \"occaecat est amet\",\n    \"id\": \"ex officia\",\n    \"state\": \"exercitation elit\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/AquilaWhitelistChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "AquilaW<PERSON>elistC<PERSON><PERSON>"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/api/Simulation/AquilaWhitelistChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "AquilaW<PERSON>elistC<PERSON><PERSON>"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Send to the topic the event that the schedule entity has changed.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"correlationId\": \"esse mollit Excepteur minim\",\n    \"scheduleId\": \"ex velit elit Ut\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/ScheduleChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "ScheduleChanged"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"correlationId\": \"esse mollit Excepteur minim\",\n    \"scheduleId\": \"ex velit elit Ut\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/ScheduleChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "ScheduleChanged"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Sends the queue message to request the running of a workflow.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"correlationId\": \"ut id aliqua\",\n    \"requestId\": \"laboris\",\n    \"requestorActorId\": \"Ut officia\",\n    \"longRunningOperationId\": \"in consequat ea\",\n    \"requestorLiveEventId\": \"labore\",\n    \"scheduleId\": \"in magna non \",\n    \"workflowIntent\": {\n        \"liveEventTime\": \"1945-03-25T00:39:57.584Z\",\n        \"continueOnError\": false,\n        \"workflowId\": \"anim esse\",\n        \"channelId\": \"cillum non\",\n        \"actorSpecificDetails\": [\n            {\n                \"actorId\": \"\",\n                \"data\": {}\n            },\n            {\n                \"actorId\": \"voluptate sint ullamco eiusmod\",\n                \"data\": {}\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/RunWorkflow", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "RunWorkflow"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"correlationId\": \"ut id aliqua\",\n    \"requestId\": \"laboris\",\n    \"requestorActorId\": \"Ut officia\",\n    \"longRunningOperationId\": \"in consequat ea\",\n    \"requestorLiveEventId\": \"labore\",\n    \"scheduleId\": \"in magna non \",\n    \"workflowIntent\": {\n        \"liveEventTime\": \"1945-03-25T00:39:57.584Z\",\n        \"continueOnError\": false,\n        \"workflowId\": \"anim esse\",\n        \"channelId\": \"cillum non\",\n        \"actorSpecificDetails\": [\n            {\n                \"actorId\": \"\",\n                \"data\": {}\n            },\n            {\n                \"actorId\": \"voluptate sint ullamco eiusmod\",\n                \"data\": {}\n            }\n        ]\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/RunWorkflow", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "RunWorkflow"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Sends the queue message to request an infrastructure change.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"desiredState\": 1,\n    \"correlationId\": \"officia commodo\",\n    \"requestId\": \"voluptate ut ut proident\",\n    \"requestorActorId\": \"voluptate sint\",\n    \"longRunningOperationId\": \"dolore do velit ipsum\",\n    \"actorId\": \"irure esse dolor\",\n    \"infrastructureId\": \"dolor dolore ea sint\",\n    \"externalSystemInfrastructureId\": \"officia est eiusmod elit\",\n    \"workflowId\": \"adipisicing voluptate nisi ipsum\",\n    \"actorSpecificDetail\": {\n        \"actorId\": \"eiusmod\",\n        \"data\": {}\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/RequestInfrastructureChange", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "RequestInfrastructureChange"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"desiredState\": 1,\n    \"correlationId\": \"officia commodo\",\n    \"requestId\": \"voluptate ut ut proident\",\n    \"requestorActorId\": \"voluptate sint\",\n    \"longRunningOperationId\": \"dolore do velit ipsum\",\n    \"actorId\": \"irure esse dolor\",\n    \"infrastructureId\": \"dolor dolore ea sint\",\n    \"externalSystemInfrastructureId\": \"officia est eiusmod elit\",\n    \"workflowId\": \"adipisicing voluptate nisi ipsum\",\n    \"actorSpecificDetail\": {\n        \"actorId\": \"eiusmod\",\n        \"data\": {}\n    }\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/RequestInfrastructureChange", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "RequestInfrastructureChange"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}, {"name": "Send the topic event that the infrastructure state has changed.", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Ocp-Apim-Subscription-Key", "value": "{{Ocp-Apim-Subscription-Key}}", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"state\": 4,\n    \"correlationId\": \"aliquip elit ea mollit officia\",\n    \"actorId\": \"incididunt aute veniam sed\",\n    \"infrastructureId\": \"si\",\n    \"longRunningOperationId\": \"Ut adipisicing pariatur\",\n    \"requestId\": \"ullamco incididunt\",\n    \"workflowId\": \"sed sit\",\n    \"data\": {}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/InfrastructureChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "InfrastructureChanged"]}}, "response": [{"name": "IAction Result.", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"state\": 4,\n    \"correlationId\": \"aliquip elit ea mollit officia\",\n    \"actorId\": \"incididunt aute veniam sed\",\n    \"infrastructureId\": \"si\",\n    \"longRunningOperationId\": \"Ut adipisicing pariatur\",\n    \"requestId\": \"ullamco incididunt\",\n    \"workflowId\": \"sed sit\",\n    \"data\": {}\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/api/Simulation/InfrastructureChanged", "host": ["{{baseUrl}}"], "path": ["api", "Simulation", "InfrastructureChanged"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "text", "header": [{"key": "Content-Type", "value": "text/plain"}], "cookie": [], "body": ""}]}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "https://localhost:44301", "type": "string"}, {"key": "Ocp-Apim-Subscription-Key", "value": "Ocp-Apim-Subscription-Key", "type": "string"}]}