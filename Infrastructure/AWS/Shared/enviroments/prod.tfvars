environment          = "prod"
vpc_id               = "vpc-005306254e1a3a793"
subnet_ids           = ["subnet-0039f5f2c49ecfca6", "subnet-027472476ffacc89b", "subnet-04ecce716a06abaf3"]
security_group_ids   = ["sg-0db65aa3e1fa7dd07"]
docdb_instance_class = "db.t3.medium"
docdb_instance_count = 2
master_username      = "docdbuser"
aws_region           = "us-west-2"
dmm_endpoint         = "http://lvcmanager.nba-hq.com/"
gmswatchdog_endpoint = "https://orchestrator-gmswatchdog-prod.internal.nba.com"
scheduler_endpoint   = "https://orchestrator-scheduler-prod.internal.nba.com"
# Note: recycling TVP Certificate from PROD Azure, so no change needed for certain
tvp_endpoint                    = "https://appgw-boss.proda.nba.tv3cloud.com/oss"
aquila_endpoint                 = "https://aquila.aas.mediakind.com/"
quortex_endpoint                = "https://api.quortex.io/"
prismaworker_endpoint           = "https://nba-fd-proda-eastus2.azurefd.net/scte224"
prismamanager_endpoint          = "https://nba-fd-proda-eastus2.azurefd.net/scte224"
aquila_settings_accountid       = "4ec533a5-4f94-48a5-8f13-635283cc8a48"
aquila_settings_accountname     = "NBAPROD"
aquila_settings_useremail       = "<EMAIL>"
aquila_settings_authendpoint    = "https://hub.aas.mediakind.com/api/v1.0/auth/login"
dmm_settings_accountid          = "null"
quortex_pool_uuid               = "pool_12b3wpsz"
quortex_pool_uuid_dr            = "pool_3kx7uxh6"
quortex_pool_uuid_radio         = "pool_b28ds8pd"
storageaccount_snapshot         = "null"
prisma_settings_managerid       = "NBA-Game-224"
quortex_settings_accountid      = "nba"
azure_subscription_id           = "96073bf6-fb80-40d4-b72f-785ec0a29c61"
azure_vidplat_cosmosdb_rg       = "ott-pdue2-vidplat-rg001"
azure_vidplat_cosmosdb_name     = "ott-pdue2-vidplat-cdb001"
aws_vidplat_mongodb_name        = "prod-vidplat-main"
azure_vidplat_cosmosdb_endpoint = "https://ott-pdue2-vidplat-cdb001.documents.azure.com:443/"
azure_tenant_id                 = "e898ff4a-4b69-45ee-a3ae-1cd6f239feb2"
eks_cluster_id                  = "49372F5059CB674CA978339C51DC7F04"
tvp_settings_authendpoint       = "https://appgw-stsssl.proda.nba.tv3cloud.com/certactive"
ecms_base_url                   = "https://manage.nba.com/wp-json/internal/api/v1"
quortex_settings_token_endpoint = "https://api.quortex.io/1.0/token/"
eventgrid_eg1                   = "https://ott-pdue2-vidplat-evg001.eastus2-1.eventgrid.azure.net/api/events"
eventgrid_eg2                   = "https://ott-pdue2-vidplat-evg002.eastus2-1.eventgrid.azure.net/api/events"
ecr_names                       = [] # repos created in prod environment by Roberto manually on 06/23/2025
gms_endpoint                    = "https://gms.internal.nba.com/"
share_secret_policy             = "Terraform-ott-pd-us-west-2-001-shared-secrets-es"