data "aws_iam_policy_document" "video_platform_trust" {
  statement {
    effect = "Allow"
    actions = [
      "sts:AssumeRoleWithWebIdentity"
    ]

    principals {
      identifiers = [
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:oidc-provider/oidc.eks.${var.aws_region}.amazonaws.com/id/${var.eks_cluster_id}"
      ]
      type = "Federated"
    }

    condition {
      test     = "StringEquals"
      variable = "oidc.eks.${var.aws_region}.amazonaws.com/id/${var.eks_cluster_id}:aud"
      values = [
        "sts.amazonaws.com"
      ]
    }

    condition {
      test     = "StringLike"
      variable = "oidc.eks.${var.aws_region}.amazonaws.com/id/${var.eks_cluster_id}:sub"
      values = [
        "system:serviceaccount:video-platform:orchestrator-*"
      ]
    }
  }
}

data "aws_iam_policy_document" "video_platform" {
  statement {
    effect  = "Allow"
    actions = ["sqs:*"]
    resources = [
      "arn:aws:sqs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:vp-*"
    ]
  }

  statement {
    effect  = "Allow"
    actions = ["sns:*"]
    resources = [
      "arn:aws:sns:${var.aws_region}:${data.aws_caller_identity.current.account_id}:vp-*"
    ]
  }

  statement {
    effect  = "Allow"
    actions = ["ssm:*"]
    resources = [
      "arn:aws:ssm:${var.aws_region}:${data.aws_caller_identity.current.account_id}:parameter/mst/*"
    ]
  }

  statement {
    effect = "Allow"
    actions = [
      "secretsmanager:DescribeSecret",
      "secretsmanager:GetRandomPassword",
      "secretsmanager:GetResourcePolicy",
      "secretsmanager:GetSecretValue",
      "secretsmanager:ListSecretVersionIds"
    ]
    resources = [
      "arn:aws:secretsmanager:${var.aws_region}:${data.aws_caller_identity.current.account_id}:secret:/video-platform/${var.environment}*"
    ]
  }
}

data "aws_iam_policy" "shared_secrets" {
  name = var.share_secret_policy
}

resource "aws_iam_policy" "video_platform" {
  name   = "mst-${var.environment}-videoplatform-policy001"
  policy = data.aws_iam_policy_document.video_platform.json
}

resource "aws_iam_role" "video_platform_irsa_role" {
  name               = "mst-${var.environment}-videoplatform-role001"
  assume_role_policy = data.aws_iam_policy_document.video_platform_trust.json
}

resource "aws_iam_role_policy_attachments_exclusive" "video_platform_irsa_policies" {
  role_name = aws_iam_role.video_platform_irsa_role.name
  policy_arns = [
    aws_iam_policy.video_platform.arn,
    data.aws_iam_policy.shared_secrets.arn
  ]
}