output "docdb_cluster_endpoint" {
  description = "The endpoint of the DocumentDB cluster"
  value       = aws_docdb_cluster.docdb_cluster.endpoint
}

output "docdb_cluster_reader_endpoint" {
  description = "The reader endpoint of the DocumentDB cluster"
  value       = aws_docdb_cluster.docdb_cluster.reader_endpoint
}

output "docdb_cluster_port" {
  description = "The port on which the DocumentDB cluster accepts connections"
  value       = aws_docdb_cluster.docdb_cluster.port
}

output "environment" {
  description = "The environment in which the DocumentDB cluster is deployed"
  value       = var.environment
}

output "docdb_username" {
  description = "The username for the DocumentDB cluster"
  value       = aws_docdb_cluster.docdb_cluster.master_username
  sensitive   = true
}

output "docdb_password" {
  description = "The master password for the DocumentDB cluster"
  value       = jsondecode(data.aws_secretsmanager_secret_version.secrets.secret_string)["docdb_password"]
  sensitive   = true
}