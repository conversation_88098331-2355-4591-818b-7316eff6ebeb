module "privateendpoint_file" {
  source                          = "../../../Modules/PrivateEndpointWithRecord/v1.0/"
  azurerm_private_endpoint_name   = var.names.private_endpoint_file
  location                        = var.location
  resource_group_name             = var.resource_group_name
  subnet_id                       = var.private_endpoint_subnet_id
  private_service_connection_name = "file-storage-connection"
  private_connection_resource_id  = local.storageaccount.id
  subresource_names               = ["file"]
  private_dns_zone_group_name     = var.dns_zones.file.name
  private_dns_zone_ids            = var.dns_zones.file.id
  tags                            = var.tags
  depends_on = [
    local.secondary_swift_connection,
    azurerm_app_service_virtual_network_swift_connection.connection
  ]
}

module "privateendpoint_blob" {
  source                          = "../../../Modules/PrivateEndpointWithRecord/v1.0/"
  azurerm_private_endpoint_name   = var.names.private_endpoint_blob
  location                        = var.location
  resource_group_name             = var.resource_group_name
  subnet_id                       = var.private_endpoint_subnet_id
  private_service_connection_name = "blob-storage-connection"
  private_connection_resource_id  = local.storageaccount.id
  subresource_names               = ["blob"]
  private_dns_zone_group_name     = var.dns_zones.blob.name
  private_dns_zone_ids            = var.dns_zones.blob.id
  tags                            = var.tags
  depends_on = [
    local.secondary_swift_connection,
    azurerm_app_service_virtual_network_swift_connection.connection
  ]
}

# Queue private endpoint (required if is Durable function)
module "privateendpoint_queue" {
  count                           = var.is_durable ? 1 : 0
  source                          = "../../../Modules/PrivateEndpointWithRecord/v1.0/"
  azurerm_private_endpoint_name   = var.names.private_endpoint_queue
  location                        = var.location
  resource_group_name             = var.resource_group_name
  subnet_id                       = var.private_endpoint_subnet_id
  private_service_connection_name = "queue-storage-connection"
  private_connection_resource_id  = local.storageaccount.id
  subresource_names               = ["queue"]
  private_dns_zone_group_name     = var.dns_zones.queue.name
  private_dns_zone_ids            = var.dns_zones.queue.id
  tags                            = var.tags
  depends_on = [
    local.secondary_swift_connection,
    azurerm_app_service_virtual_network_swift_connection.connection
  ]
}

# Table private endpoint (required if is Durable function)
module "privateendpoint_table" {
  count                           = var.is_durable ? 1 : 0
  source                          = "../../../Modules/PrivateEndpointWithRecord/v1.0/"
  azurerm_private_endpoint_name   = var.names.private_endpoint_table
  location                        = var.location
  resource_group_name             = var.resource_group_name
  subnet_id                       = var.private_endpoint_subnet_id
  private_service_connection_name = "table-storage-connection"
  private_connection_resource_id  = local.storageaccount.id
  subresource_names               = ["table"]
  private_dns_zone_group_name     = var.dns_zones.table.name
  private_dns_zone_ids            = var.dns_zones.table.id
  tags                            = var.tags
  depends_on = [
    local.secondary_swift_connection,
    azurerm_app_service_virtual_network_swift_connection.connection
  ]
}

# Function Private endpoint
module "privateendpoint_function" {
  count                           = var.create_private_endpoint ? 1 : 0
  source                          = "../../../Modules/PrivateEndpointWithRecord/v1.0/"
  azurerm_private_endpoint_name   = var.names.private_endpoint_function
  location                        = var.location
  resource_group_name             = var.resource_group_name
  subnet_id                       = var.private_endpoint_subnet_id
  private_service_connection_name = "function-connection"
  private_connection_resource_id  = local.functionapp.id
  subresource_names               = ["sites"]
  private_dns_zone_group_name     = var.dns_zones.azurewebsites.name
  private_dns_zone_ids            = var.dns_zones.azurewebsites.id
  tags                            = var.tags
  depends_on = [
    local.secondary_swift_connection,
    azurerm_app_service_virtual_network_swift_connection.connection
  ]
}

locals {
  private_endpoint_file     = module.privateendpoint_file
  private_endpoint_blob     = module.privateendpoint_blob
  private_endpoint_queue    = var.is_durable ? module.privateendpoint_queue[0] : null
  private_endpoint_table    = var.is_durable ? module.privateendpoint_table[0] : null
  private_endpoint_function = var.create_private_endpoint ? module.privateendpoint_function[0] : null
}
