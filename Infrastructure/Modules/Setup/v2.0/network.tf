# retrieve Network info.
module "networks" {
  for_each             = var.read_network_info ? local.naming : {}
  source               = "git::*********************:v3/nbadev/TerraformModules/DataModuleVNETAndSubnets?ref=v1.0.0"
  resource_group_name  = each.value.generated_names.network.resource_group[0]
  virtual_network_name = each.value.generated_names.network.virtual_network[0]
  name_suffixes = [
    "endpoint",
    "ws1func",
    "ws1app",
    "container"
  ]
}

# Spoke Network
locals {
  primary_network = {
    VNet             = var.read_network_info ? module.networks["primary_location"].vnet : null
    subnet_endpoint1 = var.read_network_info ? module.networks["primary_location"].subnets_by_suffix.endpoint[0] : null
    subnet_endpoint2 = var.read_network_info ? module.networks["primary_location"].subnets_by_suffix.endpoint[1] : null
    subnet_functions = var.read_network_info ? module.networks["primary_location"].subnets_by_suffix.ws1func[0] : null
    subnet_app       = var.read_network_info ? module.networks["primary_location"].subnets_by_suffix.ws1app[0] : null
    subnet_container = var.read_network_info ? module.networks["primary_location"].subnets_by_suffix.container[0] : null
  }
  secondary_network = {
    VNet             = local.is_high_available && var.read_network_info ? module.networks["secondary_location"].vnet : null
    subnet_endpoint1 = local.is_high_available && var.read_network_info ? module.networks["secondary_location"].subnets_by_suffix.endpoint[0] : null
    subnet_endpoint2 = local.is_high_available && var.read_network_info ? module.networks["secondary_location"].subnets_by_suffix.endpoint[1] : null
    subnet_functions = local.is_high_available && var.read_network_info ? module.networks["secondary_location"].subnets_by_suffix.ws1func[0] : null
    subnet_app       = local.is_high_available && var.read_network_info ? module.networks["secondary_location"].subnets_by_suffix.ws1app[0] : null
    subnet_container = local.is_high_available && var.read_network_info ? module.networks["secondary_location"].subnets_by_suffix.container[0] : null
  }
}

# Network Hub
locals {
  primary_ng_rg = {
    primary_ng_rg = module.constants.network_hub.primary.dns_resource_group
  }
  secondary_ng_rg = local.is_high_available ? { secondary_ng_rg = module.constants.network_hub.secondary.dns_resource_group } : {}
  networkhub_rgs  = merge(local.primary_ng_rg, local.secondary_ng_rg)
}

module "dns_zones" {
  for_each            = var.read_dns_zones ? local.networkhub_rgs : {}
  source              = "git::*********************:v3/nbadev/TerraformModules/DataModulePrivateDNSZones?ref=v1.0.1"
  resource_group_name = each.value
  providers = {
    azurerm = azurerm.networkhub
  }
}

locals {
  primary_private_dns = var.read_dns_zones ? {
    appconfig     = module.dns_zones["primary_ng_rg"].azconfig
    azurewebsites = module.dns_zones["primary_ng_rg"].azurewebsites
    blob          = module.dns_zones["primary_ng_rg"].blob
    documents     = module.dns_zones["primary_ng_rg"].documents
    eventgrid     = module.dns_zones["primary_ng_rg"].eventgrid
    file          = module.dns_zones["primary_ng_rg"].file
    queue         = module.dns_zones["primary_ng_rg"].queue
    table         = module.dns_zones["primary_ng_rg"].table
    vaultcore     = module.dns_zones["primary_ng_rg"].vaultcore
  } : {}
  secondary_private_dns = var.read_dns_zones && local.is_high_available ? {
    appconfig     = module.dns_zones["secondary_ng_rg"].azconfig
    azurewebsites = module.dns_zones["secondary_ng_rg"].azurewebsites
    blob          = module.dns_zones["secondary_ng_rg"].blob
    documents     = module.dns_zones["secondary_ng_rg"].documents
    eventgrid     = module.dns_zones["secondary_ng_rg"].eventgrid
    file          = module.dns_zones["secondary_ng_rg"].file
    queue         = module.dns_zones["secondary_ng_rg"].queue
    table         = module.dns_zones["secondary_ng_rg"].table
    vaultcore     = module.dns_zones["secondary_ng_rg"].vaultcore
  } : {}
}

locals {
  network_hub = {
    subscription_id = module.subscriptions.subscription_ids.nba_pdna_network_sub001
  }
}
