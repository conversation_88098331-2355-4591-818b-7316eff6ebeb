# Create resource tags
module "resourcetags" {
  source              = "git::*********************:v3/nbadev/TerraformModules/ResourceTags?ref=v1.0.1"
  application         = module.constants.resource_tags.application
  data_classification = module.constants.resource_tags.data_classification
  department          = module.constants.resource_tags.department
  distribution_email  = module.constants.resource_tags.distribution_email
  environment         = var.environment
  league              = module.constants.resource_tags.league
  pipeline_details    = var.pipeline_details
  sla                 = module.constants.resource_tags.sla
  sub_department      = module.constants.resource_tags.sub_department
}
