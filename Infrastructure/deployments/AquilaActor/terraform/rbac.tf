locals {
  appconfigs = local.is_high_available ? [
    local.primary.resources.appconfig.id,
    local.secondary.resources.appconfig.id
  ] : [local.primary.resources.appconfig.id]
}

# Adding a role assignment that allows the function app host identity to retrieve from app configuration
module "rbac_appconfig" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = local.appconfigs
  role_definition_name = "App Configuration Data Reader"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

# Adding a role assignment that allows the function app host identity to write into Cosmos DataBase
module "rbac_cosmos" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = [local.primary.resources.cosmos.id]
  role_definition_name = "DocumentDB Account Contributor"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

locals {
  eventgrids = compact(
    [
      local.primary.resources.eventgrid.id,
      local.primary.resources.eventgrid_health.id,
      try(local.secondary.resources.eventgrid.id, null),
      try(local.secondary.resources.eventgrid_health.id, null)
    ]
  )
}

# Adding a role assignment that allows the function app host identity to publish to the shared event grid
module "rbac_eventgrid" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = local.eventgrids
  role_definition_name = "EventGrid Data Sender"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

# Adding a role assignment that allows the function app host identity to receive messages from service bus
locals {
  primary_queues = {
    primary_iscr_aquila_channels = local.primary.resources.servicebus_queues.iscr_aquila_channels.id
  }
  secondary_queues = local.is_high_available ? {
    secondary_iscr_aquila_channels = local.secondary.resources.servicebus_queues.iscr_aquila_channels.id
  } : {}
  service_bus_queues = merge(local.primary_queues, local.secondary_queues)
}

module "rbac_servicebus_reader" {
  source               = "../../../Modules/MultipleRoleAssigment/v2.0/"
  scopes               = local.service_bus_queues
  role_definition_name = "Azure Service Bus Data Receiver"
  principal_ids        = local.functionapp.identities_map
  providers = {
    azurerm = azurerm.SharedServicesProvider
  }
}
