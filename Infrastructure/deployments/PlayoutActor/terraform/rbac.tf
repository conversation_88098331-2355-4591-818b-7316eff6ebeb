# Adding a role assignment that allows the function app host identity to write into Cosmos DataBase
module "rbac_cosmos" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = [local.primary.resources.cosmos.id]
  role_definition_name = "DocumentDB Account Contributor"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on           = [local.functionapp]
}

# Adding a role assignment that allows the function app host identity to retrieve from app configuration
module "rbac_appconfig" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = compact([local.primary.resources.appconfig.id, try(local.secondary.resources.appconfig.id, null)])
  role_definition_name = "App Configuration Data Reader"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

# Adding a role assignment that allows the function app host identity to publish to the shared event grid
module "rbac_eventgrid" {
  source               = "../../../Modules/MultipleRoleAssigment/v1.0/"
  scopes               = compact([local.primary.resources.eventgrid.id, try(local.secondary.resources.eventgrid.id, null)])
  role_definition_name = "EventGrid Data Sender"
  principal_ids        = local.functionapp.function_principal_id_all
  depends_on = [
    local.functionapp
  ]
}

# Service bus Data receiver
locals {
  primary_queues = {
    primary_iscr_playouts = local.primary.resources.servicebus_queues.iscr_playouts.id
    primary_psrc          = local.primary.resources.servicebus_queues.psrc.id
  }
  secondary_queues = local.is_high_available ? {
    secondary_iscr_playouts = local.secondary.resources.servicebus_queues.iscr_playouts.id
    secondary_psrc          = local.secondary.resources.servicebus_queues.psrc.id
  } : {}
  service_bus_queues = merge(local.primary_queues, local.secondary_queues)
}

module "rbac_servicebus_reader" {
  source               = "../../../Modules/MultipleRoleAssigment/v2.0/"
  scopes               = local.service_bus_queues
  role_definition_name = "Azure Service Bus Data Receiver"
  principal_ids        = local.functionapp.identities_map
  providers = {
    azurerm = azurerm.SharedServicesProvider
  }
}
