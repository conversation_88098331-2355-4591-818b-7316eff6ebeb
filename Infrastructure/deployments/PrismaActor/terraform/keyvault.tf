locals {
  secretname_storage_connection = "PrismaActor--StorageAccount--PrimaryConnectionString-sa"
}

module "keyvaultaccesspolicy_function" {
  count                   = length(local.functionapp.function_principal_id_all)
  source                  = "git::*********************:v3/nbadev/TerraformModules/KeyVaultAccessPolicy?ref=v2.0.0"
  tenant_id               = data.azurerm_client_config.current.tenant_id
  key_vault_id            = local.primary.resources.keyvault.id
  object_id               = local.functionapp.function_principal_id_all[count.index]
  key_permissions         = []
  secret_permissions      = ["Get", "List"]
  storage_permissions     = []
  certificate_permissions = []
}

# Key vault secrets

# # Adding the connection string to key vault for app settings
module "function_storage_connection_string" {
  source       = "git::*********************:v3/nbadev/TerraformModules/KeyVaultSecret?ref=v1.0.0"
  secret_name  = local.secretname_storage_connection
  secret_value = local.functionapp.storage_primary_connection_string
  key_vault_id = local.primary.resources.keyvault.id
  tags = {
    Sensitivity   = "Standard"
    External      = "No"
    PII           = "No"
    Period        = 90
    Justification = ""
    Purpose       = "Storage account connection string used by the Function"
    ResourceID    = local.functionapp.storage_id
    CredentialID  = "PrimaryKey"
    OwnerName     = local.secret_tags.owner_name
  }
  content_type = "ConnectionString"
}
