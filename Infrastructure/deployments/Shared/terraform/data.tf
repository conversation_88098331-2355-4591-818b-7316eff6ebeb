data "azurerm_client_config" "current" {}

data "azurerm_user_assigned_identity" "aks_msi" {
  provider            = azurerm.aks
  name                = var.aks_cluster_settings.cluster_msi_name
  resource_group_name = var.aks_cluster_settings.rsg
}

data "azurerm_app_configuration" "app_config" {
  name                = module.setup.generated_names.appconf.app_configuration[0]
  resource_group_name = module.setup.generated_names.appconf.resource_group[0]
}

data "azurerm_servicebus_namespace" "shared_servicebus_namespace" {
  provider            = azurerm.sharedservices
  name                = var.shared_servicebus_namespace.service_bus_name
  resource_group_name = var.shared_servicebus_namespace.rsg
}

data "azurerm_servicebus_queue" "queues" {
  provider     = azurerm.sharedservices
  for_each     = toset(local.service_bus_queues)
  namespace_id = data.azurerm_servicebus_namespace.shared_servicebus_namespace.id
  name         = each.key
}

data "azurerm_eventgrid_topic" "eventgrid_topic" {
  name                = module.setup.primary.adapted_names.eventgrid.name
  resource_group_name = module.setup.primary.adapted_names.eventgrid.resource_group
}