provider "azurerm" {
  features {
    key_vault {
      purge_soft_delete_on_destroy               = false
      purge_soft_deleted_certificates_on_destroy = false
      purge_soft_deleted_keys_on_destroy         = false
      purge_soft_deleted_secrets_on_destroy      = false
      recover_soft_deleted_key_vaults            = true
      recover_soft_deleted_certificates          = true
      recover_soft_deleted_keys                  = true
      recover_soft_deleted_secrets               = true
    }
  }
}

provider "azurerm" {
  alias           = "networkhub"
  subscription_id = module.setup.network_hub.subscription_id
  features {}
}

provider "azurerm" {
  alias           = "sharedservices"
  subscription_id = module.setup.shared_services_subscription_id
  features {}
}

provider "azurerm" {
  alias           = "aks"
  subscription_id = var.aks_cluster_settings.subscription_id
  features {}
}