# Deploy App Service Playout Service
module "appservice_secondary" {
  count                    = local.is_high_available ? 1 : 0
  source                   = "git::*********************:v3/nbadev/TerraformModules/AppService?ref=v1.1.1"
  azurerm_app_service_name = local.secondary_service_names.app_service
  location                 = local.secondary.location
  resource_group_name      = local.secondary_service_names.resource_group
  app_service_plan_id      = local.secondary.resources.serviceplan_app_service.id
  linux_fx_version         = "DOTNETCORE|3.1"
  tags                     = local.tags
  app_settings = {
    APPINSIGHTS_INSTRUMENTATIONKEY                = local.secondary.resources.application_insights.instrumentation_key
    ConfigSettings__CommonKey                     = module.setup.constants.appconfig_common_key
    ConfigSettings__Endpoint                      = local.primary.resources.appconfig.endpoint
    ConfigSettings__FailoverEndpoint              = local.secondary.resources.appconfig.endpoint
    ConfigSettings__RefreshIntervalInSecs         = 1
    ConfigSettings__ServiceName                   = "Simulator"
    Serilog__WriteTo__1__Args__instrumentationKey = local.secondary.resources.application_insights.instrumentation_key
    WEBSITE_ALT_DNS_SERVER                        = local.secondary_dns_server_alt
    WEBSITE_DNS_SERVER                            = local.secondary_dns_server
    WEBSITE_RUN_FROM_PACKAGE                      = 1
  }
}

locals {
  appservice_secondary = local.is_high_available ? module.appservice_secondary[0] : null
}

# Connect to SubNet
resource "azurerm_app_service_virtual_network_swift_connection" "connection_secondary" {
  count          = local.is_high_available ? 1 : 0
  app_service_id = local.appservice_secondary.id
  subnet_id      = local.secondary.resources.subnet_app.id
}

module "appservice_privateendpoint_secondary" {
  count                           = local.is_high_available ? 1 : 0
  source                          = "git::*********************:v3/nbadev/TerraformModules/PrivateEndpoint?ref=v1.0.0"
  resource_group_name             = local.secondary_service_names.resource_group
  azurerm_private_endpoint_name   = local.secondary_service_names.private_endpoints[local.private_endpoint_indexes.app_service]
  location                        = local.secondary.location
  subnet_id                       = local.secondary.resources.subnet_endpoint1.id
  private_service_connection_name = "app-connection"
  private_connection_resource_id  = local.appservice_secondary.id
  subresource_names               = ["sites"]
  private_dns_zone_group_name     = local.secondary.private_dns_zones.azurewebsites.name
  private_dns_zone_ids            = local.secondary.private_dns_zones.azurewebsites.id
  tags                            = local.tags
}

# locals {
#   appservice_privateendpoint_secondary = local.is_high_available ? module.appservice_privateendpoint_secondary[0] : null
#   aspesec_record_sets                  = local.is_high_available ? local.appservice_privateendpoint_secondary.private_dns_zone_configs[0].record_sets : []
# }

module "azurewebsites_dnsrecord_secondary" {
  count                             = local.is_high_available ? 1 : 0
  source                            = "git::*********************:v3/nbadev/TerraformModules/PrivateDNSARecord?ref=v1.0.1"
  azurerm_private_dns_a_record_name = local.appservice_secondary.name
  resource_group_name               = local.primary.private_dns_zones.azurewebsites.resource_group_name
  records                           = module.appservice_privateendpoint_secondary[0].private_service_connection.0.private_ip_address
  ttl                               = 10
  tags                              = local.tags
  zone_name                         = local.primary.private_dns_zones.azurewebsites.name
  providers = {
    azurerm = azurerm.networkhub
  }
}

module "azurewebsites_dnsrecord_secondary_scm" {
  count                             = local.is_high_available ? 1 : 0
  source                            = "git::*********************:v3/nbadev/TerraformModules/PrivateDNSARecord?ref=v1.0.1"
  azurerm_private_dns_a_record_name = "${local.appservice_secondary.name}.scm"
  resource_group_name               = local.primary.private_dns_zones.azurewebsites.resource_group_name
  records                           = module.appservice_privateendpoint_secondary[0].private_service_connection.0.private_ip_address
  ttl                               = 10
  tags                              = local.tags
  zone_name                         = local.primary.private_dns_zones.azurewebsites.name
  providers = {
    azurerm = azurerm.networkhub
  }
}

module "diagnostic_logs_app_service_secondary" {
  count                      = local.is_high_available ? 1 : 0
  source                     = "git::*********************:v3/nbadev/TerraformModules/DiagnosticLogs?ref=v1.0.0"
  name                       = "diag-${local.appservice_secondary.name}"
  target_resource_id         = local.appservice_secondary.id
  log_analytics_workspace_id = local.secondary.resources.log_analytics_workspace.id
  logs = [
    "AppServiceAntivirusScanAuditLogs",
    "AppServiceAppLogs",
    "AppServiceAuditLogs",
    "AppServiceConsoleLogs",
    "AppServiceFileAuditLogs",
    "AppServiceHTTPLogs",
    "AppServiceIPSecAuditLogs",
    "AppServicePlatformLogs"
  ]
  metrics = ["AllMetrics"]
}
