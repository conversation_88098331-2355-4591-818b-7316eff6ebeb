terraform {
  required_version = ">=0.13"
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=3.0.2"
    }
  }

  backend "azurerm" {
    resource_group_name  = "ott-dvue2-deploy-rg001"
    storage_account_name = "ottdvue2deploysa001"
    container_name       = "terraform-state"
    key                  = "ott-dvue2-vdthpwd-func001.tfstate"
  }
}

provider "azurerm" {
  features {
    key_vault {
      purge_soft_deleted_certificates_on_destroy = false
      purge_soft_deleted_keys_on_destroy         = false
      purge_soft_deleted_secrets_on_destroy      = false
      recover_soft_deleted_certificates          = true
      recover_soft_deleted_keys                  = true
      recover_soft_deleted_secrets               = true
    }
  }
}

data "azurerm_client_config" "current" {
}

provider "azurerm" {
  alias           = "SharedServicesProvider"
  subscription_id = local.shared_services_subscription_id
  features {}
}
