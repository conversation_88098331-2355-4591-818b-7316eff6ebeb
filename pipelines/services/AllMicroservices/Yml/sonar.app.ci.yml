trigger: none

schedules:
- cron: "0 0 * * *"
  displayName: Daily midnight build
  branches:
    include:
    - main
  always: true

resources:
  repositories:
    - repository: templates
      type: git
      name: DevOps
      
parameters:
  - name: checkCodeCoverage
    default: false
    type: boolean


pr: none

stages:
  - stage: Build
    jobs:
      - template: ../../../templates/jobs/build.yml
        parameters:
          solution_path: '$(System.DefaultWorkingDirectory)/src/Services/NBA.NextGen.VideoPlatform.Services.sln'
          project_path: '$(System.DefaultWorkingDirectory)/src/Services/NBA.NextGen.VideoPlatform.Services.sln'
          service_name: 'sonar'
          checkCodeCoverage: ${{ parameters.checkCodeCoverage }}
          security_scan: false
          skip_artifact_creation: true
          sonar: true
