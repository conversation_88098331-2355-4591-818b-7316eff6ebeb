{"ServiceName": "AquilaWatchdog", "Settings": {"ApiManagement:EnableMocking": "#{enable_mocking}#", "AquilaSettings:Endpoint": "https://#{TerraformOTTAPIMOutputs.backend_apim_name}#.azure-api.net/mkaquila/", "AquilaSettings:RetryCount": "3", "AquilaConnection:AccountId": "#{AquilaSettings_AccountId}#", "NotifierSettings:MaxDelayInSecs": "10", "NotifierSettings:RetryCount": "3", "NotifierSettings:Topics:0:Name": "AquilaWatchdog", "NotifierSettings:Topics:0:EndPoint": "#{TerraformVideoPlatformSharedOutputs.eventgrid_topic_endpoint_videoplatform}#", "NotifierSettings:Topics:0:FailoverEndpoint": "#{TerraformVideoPlatformSharedOutputs.eventgrid_topic_endpoint_videoplatform_secondary}#", "NotifierSettings:Topics:1:Name": "VideoPlatformHealth", "NotifierSettings:Topics:1:EndPoint": "#{TerraformVideoPlatformSharedOutputs.eventgrid_health_topic_endpoint_videoplatform}#", "NotifierSettings:Topics:1:FailoverEndpoint": "#{TerraformVideoPlatformSharedOutputs.eventgrid_health_topic_endpoint_videoplatform_secondary}#", "LoggingSettings:SystemName": "VideoPlatform", "LoggingSettings:ServiceName": "AquilaWatchdog"}}