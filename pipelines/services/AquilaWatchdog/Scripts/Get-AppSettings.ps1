$config = @'
"APPLICATIONINSIGHTS_CONNECTION_STRING=$(TerraformServiceOutputs.applicationinsights_connection_string)",
"AzureWebJobsStorage=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"WEBSITE_CONTENTAZUREFILECONNECTIONSTRING=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"FUNCTIONS_EXTENSION_VERSION=~4",
"FUNCTIONS_WORKER_RUNTIME=dotnet",
"FUNCTIONS_INPROC_NET8_ENABLED=1",
"WEBSITE_RUN_FROM_PACKAGE=1",
"IntegrationServiceBusConnectionString__fullyQualifiedNamespace=$(TerraformServiceOutputs.servicebus_namespace_connection_string_value)",
"AquilaChannelCron=0 * * * * *",
"AquilaSourceCron=0 */5 * * * *",
"AquilaWhitelistCron=0 */5 * * * *",
"ConfigSettings__ServiceName=AquilaWatchdog",
"ConfigSettings__CommonKey=$(TerraformVideoPlatformSharedOutputs.app_configuration_common_key)",
"ConfigSettings__Endpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint)",
"ConfigSettings__FailoverEndpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint_secondary)",
"ConfigSettings__RefreshIntervalInSecs=1",
"ConfigRefreshTimeoutCron=0 */5 * * * *",
"AquilaWatchdogEventingQueueName=$(TerraformVideoPlatformSharedOutputs.servicebusqueue_aquila_watchdog_eventing_queuename)"
'@;

$config = $config.replace("`n", "").replace("`r", "");
Write-Host "##vso[task.setvariable variable=result;isoutput=true]$config"