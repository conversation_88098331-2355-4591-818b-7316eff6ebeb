$config = @'
"APPLICATIONINSIGHTS_CONNECTION_STRING=$(TerraformServiceOutputs.applicationinsights_connection_string)",
"FUNCTIONS_WORKER_RUNTIME=dotnet",
"FUNCTIONS_INPROC_NET8_ENABLED=1",
"FUNCTIONS_EXTENSION_VERSION=~4",
"AzureWebJobsStorage=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"WEBSITE_CONTENTAZUREFILECONNECTIONSTRING=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"IntegrationServiceBusConnectionString__fullyQualifiedNamespace=$(TerraformServiceOutputs.servicebus_namespace_connection_string_value)",
"ConfigSettings__ServiceName=Orchestrator",
"ConfigSettings__CommonKey=$(TerraformVideoPlatformSharedOutputs.app_configuration_common_key)",
"ConfigSettings__Endpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint)",
"ConfigSettings__FailoverEndpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint_secondary)",
"ConfigSettings__RefreshIntervalInSecs=1",
"ConfigRefreshTimeoutCron=0 */5 * * * *",
"OrchestratorHealthTriggerCron=0 */5 * * * *",
"WorkflowRequestQueueName=$(TerraformVideoPlatformSharedOutputs.servicebusqueue_workflow_request_queuename)"
'@

$config = $config.replace("`n", "").replace("`r", "");
Write-Host "##vso[task.setvariable variable=result;isoutput=true]$config"