$config = @'
"APPLICATIONINSIGHTS_CONNECTION_STRING=$(TerraformServiceOutputs.applicationinsights_connection_string)",
"AzureWebJobsStorage=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"WEBSITE_CONTENTAZUREFILECONNECTIONSTRING=$(TerraformServiceOutputs.function_storage_connection_string_value)",
"FUNCTIONS_EXTENSION_VERSION=~4",
"IntegrationServiceBusConnectionString__fullyQualifiedNamespace=$(TerraformServiceOutputs.servicebus_namespace_connection_string_value)",
"SchedulerSynchronizeCron=0 * * * * *",
"ConfigSettings__ServiceName=Scheduler",
"ConfigSettings__CommonKey=$(TerraformVideoPlatformSharedOutputs.app_configuration_common_key)",
"ConfigSettings__Endpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint)",
"ConfigSettings__FailoverEndpoint=$(TerraformVideoPlatformSharedOutputs.app_configuration_endpoint_secondary)",
"ConfigSettings__RefreshIntervalInSecs=1",
"ConfigRefreshTimeoutCron=0 */5 * * * *"
'@

$config = $config.replace("`n", "").replace("`r", "");
Write-Host "##vso[task.setvariable variable=result;isoutput=true]$config"