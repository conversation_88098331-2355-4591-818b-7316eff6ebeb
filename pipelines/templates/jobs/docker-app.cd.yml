parameters:
  - name: service_connection
    type: string
    
  - name: storage_account
    type: string

  - name: container
    type: string

  - name: state_file
    type: string

  - name: shared_state_file
    type: string

  - name: resource_group
    type: string

  - name: terraform_version
    type: string

  - name: environment
    type: string

  - name: ado_environment
    type: string

  - name: build_trigger_id
    type: string

  - name: ott_backend_state_file
    type: string

  - name: package_path
    type: string

  - name: agent_pool
    type: string

  - name: jobName
    type: string
    default: 'Service'

  - name: shared_services_service_connection
    type: string

jobs:
  - deployment: ${{parameters.jobName}}
    pool: ${{ parameters.agent_pool }}
    displayName: Application Deployment ${{parameters.jobName}}
    variables:
      - name: tag
        value: latest
    environment: ${{ parameters.ado_environment }}
    strategy:
      runOnce:
        deploy:
          steps:

            - download: none

            - task: DownloadPipelineArtifact@2
              displayName: Download Pipeline Artifact
              inputs:
                source: specific
                project: DTC
                pipeline: $(resources.pipeline.artifacts.pipelineID)
                runVersion: latestFromBranch
                runBranch: $(Build.SourceBranch)
                artifact: Drop
                targetPath: $(Pipeline.Workspace)/artifacts/Drop

            - checkout: self
              clean: true
              fetchDepth: 5
              lfs: true

            - task: ms-devlabs.custom-terraform-tasks.custom-terraform-installer-task.TerraformInstaller@0
              displayName: Install Terraform ${{ parameters.terraform_version }}
              inputs:
                terraformVersion: ${{ parameters.terraform_version }}

            # TODO: once the apim outputs are available in the service, remove this step and replace its use.
            - template: ../steps/tf.output.yml
              parameters:
                backendServiceArm: ${{parameters.service_connection }}
                backendAzureRmResourceGroupName: ${{ parameters.resource_group }}
                backendAzureRmStorageAccountName: ${{ parameters.storage_account }}
                backendAzureRmContainerName: ${{ parameters.container }}
                backendAzureRmKey: ${{ parameters.ott_backend_state_file }}
                prefix: TerraformOTTAPIMOutputs

            - template: ../steps/tf.output.yml
              parameters:
                backendServiceArm: ${{parameters.service_connection}}
                backendAzureRmResourceGroupName: ${{ parameters.resource_group }}
                backendAzureRmStorageAccountName: ${{ parameters.storage_account }}
                backendAzureRmContainerName: ${{ parameters.container }}
                backendAzureRmKey: ${{ parameters.shared_state_file }}
                prefix: TerraformVideoPlatformSharedOutputs

            - template: ../steps/tf.output.yml
              parameters:
                backendServiceArm: ${{parameters.service_connection}}
                backendAzureRmResourceGroupName: ${{ parameters.resource_group }}
                backendAzureRmStorageAccountName: ${{ parameters.storage_account }}
                backendAzureRmContainerName: ${{ parameters.container }}
                backendAzureRmKey: ${{ parameters.state_file }}
                prefix: TerraformServiceOutputs

            - task: AzureCLI@2
              displayName: Acr Token Generation
              name : GenerateToken
              inputs:
                azureSubscription: ${{parameters.shared_services_service_connection}}
                scriptType: pscore
                scriptLocation: scriptPath
                scriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Acr-Token.ps1
                arguments: >
                  -AcrName '$(TerraformServiceOutputs.acr_name)'
                  -AcrUrl '$(TerraformServiceOutputs.acr_login_server)'
                  -AcrTokenName '$(TerraformServiceOutputs.acr_username)'
                  -AcrMyScopeMap '$(TerraformServiceOutputs.acr_scopemap_name)'

            - task: AzurePowerShell@5
              displayName: Storing Token
              inputs:
                azureSubscription: ${{parameters.service_connection}}
                scriptType: filePath
                scriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Set-AcrToken.ps1
                scriptArguments: >
                  -AcrToken $(GenerateToken.acrtoken)               
                  -KeyvaultName $(TerraformServiceOutputs.keyvault_name)
                  -SecretName $(TerraformServiceOutputs.secretname_acr_password)
                azurePowerShellVersion: LatestVersion

            - task: PowerShell@2
              displayName: Remove line endings for bash scripts
              inputs:
                targetType: 'filePath'
                filePath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Remove-LineEndings.ps1
                arguments: >
                  -FilePath '$(Pipeline.Workspace)/artifacts/Drop/scripts/start-ffmpeg.sh'

            - task: PowerShell@2
              displayName: Remove line endings for bash scripts
              inputs:
                targetType: 'filePath'
                filePath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Remove-LineEndings.ps1
                arguments: >
                  -FilePath '$(Pipeline.Workspace)/artifacts/Drop/scripts/start-gstreamer.sh'

            - task: AzureCLI@2
              displayName: Docker Push
              inputs:
                azureSubscription: ${{parameters.shared_services_service_connection}}
                scriptType: pscore
                scriptLocation: scriptPath
                scriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Push-DockerImage.ps1
                arguments: >
                  -AcrName '$(TerraformServiceOutputs.acr_name)'
                  -AcrUrl '$(TerraformServiceOutputs.acr_login_server)'
                  -Image 'nbanextgen/ffmpeg/playout:$(tag)'
                  -DockerFileFolder '$(Pipeline.Workspace)/artifacts/Drop/scripts'
                  -DockerFileName 'dockerfile.ffmpeg'

            - task: AzureCLI@2
              displayName: Docker Push
              inputs:
                azureSubscription: ${{parameters.shared_services_service_connection}}
                scriptType: pscore
                scriptLocation: scriptPath
                scriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Push-DockerImage.ps1
                arguments: >
                  -AcrName '$(TerraformServiceOutputs.acr_name)'
                  -AcrUrl '$(TerraformServiceOutputs.acr_login_server)'
                  -Image 'nbanextgen/gstreamer/playout:$(tag)'
                  -DockerFileFolder '$(Pipeline.Workspace)/artifacts/Drop/scripts'
                  -DockerFileName 'dockerfile.gstreamer'
            
            - template: ../steps/config.yml
              parameters:
                serviceName: ${{parameters.jobName}}
                service_connection: ${{parameters.service_connection}}
                keyvaultName: $(TerraformServiceOutputs.keyvault_name)
                appConfigName: $(TerraformVideoPlatformSharedOutputs.app_configuration_name)
                targetFiles: $(Pipeline.Workspace)/artifacts/Drop/scripts/${{parameters.jobName}}/config.json
                scriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Set-ConfigSettings.ps1

            - task: AzureRmWebAppDeployment@4
              displayName: Deploy service to primary region
              inputs:
                azureSubscription: ${{parameters.service_connection}}
                WebAppName: $(TerraformServiceOutputs.service_application_name)
                ResourceGroupName: $(TerraformServiceOutputs.service_resource_group)
                packageForLinux: ${{parameters.package_path}}

            - task: AzureCLI@2
              displayName: Get Service URL
              name: ImportApim
              inputs:
                azureSubscription: ${{parameters.service_connection}}
                scriptType: pscore
                scriptLocation: inlineScript
                inlineScript: |
                  # Getting the Service endpoint to use.
                  IF ([string]::IsNullOrWhitespace("$(TerraformServiceOutputs.frontdoor_name)")) {
                      $url =  "https://$(TerraformServiceOutputs.app_service_name).azurewebsites.net/"
                  }
                  else {
                      $endpoint = az afd endpoint show --profile-name $(TerraformServiceOutputs.frontdoor_name) --resource-group  $(TerraformServiceOutputs.frontdoor_resource_group) --endpoint-name $(TerraformServiceOutputs.frontdoor_endpoint_name) --subscription $(TerraformServiceOutputs.frontdoor_subscription) | ConvertFrom-Json

                      if ($null -eq $endpoint) {
                          Write-Host "AFD Endpoint not found."
                      }
                      else {
                          $url = $endpoint.hostName
                          $url = "https://$url"
                      }
                  } 

                  Write-Host "##vso[task.setvariable variable=serviceHostName;isoutput=true;issecret=false]$url"

            - task: AzurePowerShell@4
              displayName: Update named value (Playout URL)
              inputs:
                azureSubscription: ${{parameters.service_connection}}
                ScriptType: InlineScript
                Inline: |
                  $apimContext = New-AzApiManagementContext -ResourceGroupName $(TerraformOTTAPIMOutputs.resource_group_name) -ServiceName $(TerraformOTTAPIMOutputs.backend_apim_name)

                  $id = Get-AzApiManagementNamedValue -Context $apimContext -NamedValueId "playoutservice-base-url" -ErrorAction SilentlyContinue

                  if ($id) {
                      Set-AzApiManagementNamedValue -Context $apimContext -NamedValueId "playoutservice-base-url" -Value $(ImportApim.serviceHostName) -Name "playoutservice-base-url"
                  }
                azurePowerShellVersion: LatestVersion

            ## TODO: Add the following steps to the pipeline when the Swagger version is fixed (and remove the previous).
            # - task: AzureCLI@2
            #   displayName: Import Into APIM
            #   name: ImportApim
            #   inputs:
            #     azureSubscription: ${{parameters.service_connection}}
            #     scriptType: pscore
            #     scriptLocation: scriptPath
            #     scriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Import-ApimApiViaAzureFunction.ps1
            #     arguments:
            #       -path "/$(service_name)"`
            #       -apiId "$(service_name)"`
            #       -apimName "$(TerraformOTTAPIMOutputs.backend_apim_name)"`
            #       -apimrgName "$(TerraformOTTAPIMOutputs.resource_group_name)"`
            #       -functionrgName "$(TerraformServiceOutputs.service_resource_group)"`
            #       -azureFunctionName "$(TerraformServiceOutputs.app_service_name)"
            #       -customSpecificationSuffix "swagger/v1/swagger.json"
            #       -FrontDoorEndpointName "$(TerraformServiceOutputs.frontdoor_endpoint_name)"
            #       -FrontDoorName "$(TerraformServiceOutputs.frontdoor_name)"
            #       -FrontDoorResourceGroup "$(TerraformServiceOutputs.frontdoor_resource_group)"
            #       -FrontDoorSubscription "$(TerraformServiceOutputs.frontdoor_subscription)"
            #       -isApp true   

            # - task: AzurePowerShell@4
            #   displayName: Create backend and policy
            #   inputs:
            #     azureSubscription: ${{parameters.service_connection}}
            #     ScriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Add-ApimBackendService.ps1
            #     ScriptArguments:
            #       -ApiId $(service_name)
            #       -ApimName $(TerraformOTTAPIMOutputs.backend_apim_name)
            #       -ApimResourceGroupName $(TerraformOTTAPIMOutputs.resource_group_name)
            #       -BackendId $(TerraformServiceOutputs.app_service_name)
            #       -FunctionKey "$(ImportApim.functionKey)"
            #       -ServiceAlias $(service_name)
            #       -ServiceHostName $(ImportApim.serviceHostName)
            #     azurePowerShellVersion: LatestVersion
            
             # Secondary region
            - template: ../steps/config.yml
              parameters:
                serviceName: ${{parameters.jobName}}
                service_connection: ${{parameters.service_connection}}
                keyvaultName: $(TerraformServiceOutputs.keyvault_name)
                appConfigName: $(TerraformVideoPlatformSharedOutputs.secondary_app_configuration_name)
                targetFiles: $(Pipeline.Workspace)/artifacts/Drop/scripts/${{parameters.jobName}}/config.json
                scriptPath: $(Pipeline.Workspace)/artifacts/Drop/scripts/Set-ConfigSettings.ps1              

            - task: AzureRmWebAppDeployment@4
              displayName: Deploy service to secondary region
              condition: and(succeeded(), ne(variables['TerraformServiceOutputs.service_application_name_secondary'], ''))
              inputs:
                azureSubscription: ${{parameters.service_connection}}
                WebAppName: $(TerraformServiceOutputs.service_application_name_secondary)
                ResourceGroupName: $(TerraformServiceOutputs.service_resource_group_secondary)
                packageForLinux: ${{parameters.package_path}}

  # This templates runs all the Security validations in place for the project
  - template: pipelines/templates/jobs/app-security-cd-template.yml@templates
    parameters:
      url_to_scan: $(url)
      scanport: 443
      pool_name: $(private_agent_pool_name_scan)
      checkout_resource_name: templates
      artifact_name: playoutservice_${{ parameters.environment}}_owaspzap
      owasp_disabled: false
      depends_on:
        - ${{parameters.jobName}}
      additional_variables:
        url: $[ dependencies.${{parameters.jobName}}.outputs['${{parameters.jobName}}.TerraformServiceOutputs.service_playout_url_primary'] ]
