<#
.SYNOPSIS
    Disable functions in Function APP
    
.DESCRIPTION
    Disable functions in Function APP of a resource groups

.PARAMETER ResourceGroups
    Resource Group Name.

.PARAMETER Action
    Action


#>

param(
    [string[]] $ResourceGroups = (throw new Exception("The List of Resource Groups is mandatory")),
    [string] $Action = (throw new Exception("The Action is mandatory"))    
) & {
    foreach ($resourceGroup in $ResourceGroups) {

        Write-Host "Looking for Function Apps in $resourceGroup..."

        #fetch function apps in a resource group
        $functionapps = Get-AzFunctionApp -ResourceGroupName  $resourceGroup | Select-Object -Property Name

        foreach ($functionapp in $functionapps) {
            
            $functionName = $functionapp.Name
            if (!($functionName.Contains('vdbddf'))) {
                
                Write-Host "Processing $functionName..."
                #Fetch functions from function app
                $FunctionsList = func azure functionapp list-functions $functionName --show-keys
        
                #Remove empty rows
                $FunctionsList = $FunctionsList.Where({ $_ -ne "" })
        
                #Remove Functions header
                [System.Collections.ArrayList]$functionList = $FunctionsList
                $functionList.RemoveAt(0)

                #Empty Hash Table
                $appsettings = @{}

                foreach ($function in $functionList) {
                    $function = $function.Trim().replace(' ', '')

                    if (!($function.StartsWith("Invoke")) -and (!($function.Contains("httpTrigger")))) {

                        #Splitting the function name to get the name without the trigger description.
                        $function = $function.Split('-')[0]

                        #Add the key value pair in to empty hash table
                        $appsettings.Add("AzureWebJobs.$function.Disabled", $action)              
                    }
                }
                #Executes if hash table is not empty
                if ($appsettings.Count -ne 0) {
                    [bool] $stopLoop = $false
                    [int] $retryCount = 0
                    do {
                        try {
                            # ErrorAction added to force to throw an exception
                            Update-AzFunctionAppSetting -Name $functionName -ResourceGroupName $resourceGroup -AppSetting $appsettings -Force -ErrorAction Stop | Out-Null
                            $stopLoop = $true
                        }
                        catch {
                            #Retry 5 times if update fails
                            if ($retryCount -gt 5) {
                                Write-Host "##vso[task.logissue type=warning] Failed to update $functionName."
                                $stopLoop = $true
                            }
                            else {
                                Write-Host $_.Exception.Message
                                Write-Host "Retrying in 30 seconds..."
                                Start-Sleep -Seconds 30
                                $retryCount++
                            }
                        }
                    }
                    While ($stoploop -eq $false)
                }
                else {
                    Write-Host "$functionName does not have functions to enable/disable."
                } 
            }
        }
    }
}