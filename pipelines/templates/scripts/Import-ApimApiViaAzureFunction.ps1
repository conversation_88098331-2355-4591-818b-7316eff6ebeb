<#
.SYNOPSIS
    Import a FunctionApp into APIM.
<#

.DESCRIPTION
    Import FunctionApp into APIM by taking input parameters

.PARAMETER path
    Path to API in APIM

.PARAMETER apiId
    Path to API in APIM

.PARAMETER apimrgName
  APIM Resource Group Name

.PARAMETER apimName
  APIM Name

.PARAMETER functionrgName
  Function APP Resource Group Name

.PARAMETER azureFunctionName
  Function APP Name

.PARAMETER customSpecificationSuffix
  custom Specification Suffix

.PARAMETER isApp
    Whether Web APP of Function APP

.PARAMETER FrontDoorName
    Front Door Name

.PARAMETER FrontDoorResourceGroup
    Front Door Resource Group Name

.PARAMETER FrontDoorSubscription
    The Subscription where Front Door hosted

.PARAMETER FrontDoorEndpointName
    The frontdoor endpoint name related to this FunctionApp
#>

param(
    [Parameter(Mandatory = $true)]
    [string]
    $path,

    [Parameter(Mandatory = $true)]
    [string]
    $apiId,

    [Parameter(Mandatory = $true)]
    [string]
    $apimrgName,

    [Parameter(Mandatory = $true)]
    [string]
    $apimName,

    [Parameter(Mandatory = $true)]
    [string]
    $functionrgName,

    [Parameter(Mandatory = $true)]
    [string]
    $azureFunctionName,

    [Parameter(Mandatory = $false)]
    [string]
    $customSpecificationSuffix,

    [string] $isApp = "false",
    [string] $FrontDoorName,
    [string] $FrontDoorResourceGroup,
    [string] $FrontDoorSubscription,
    [string] $FrontDoorEndpointName
)

$basePath = "https://$azureFunctionName.azurewebsites.net/"
$defaultSuffix = "api/swagger.json"

[bool]$Stoploop = $false
[int]$Retrycount = "0"
[bool]$IsApp = [System.Convert]::ToBoolean($isApp)

if ($customSpecificationSuffix) {
    $specificationUrl = $basePath + $customSpecificationSuffix
}
else {
    $specificationUrl = $basePath + $defaultSuffix
}

do {

    # Enabling (temporally) http for StreamMarkers
    if ($apiId -eq "StreamMarkersUpdate") {
        $resp = az apim api import --path $path --api-id $apiId --resource-group $apimrgName --service-name $apimName --specification-url $specificationUrl --specification-format Swagger --protocols https http
    }
    else {
        $resp = az apim api import --path $path --api-id $apiId --resource-group $apimrgName --service-name $apimName --specification-url $specificationUrl --specification-format Swagger --protocols https
    }

    if ($null -eq $resp  ) {
        if ($Retrycount -gt 7) {
            Write-Host $_
            throw "Process canceled after 7 retries."
            $Stoploop = $true
        }
        else {
            Write-Host "Failed to import the API. Retrying in 60 seconds..."
            Start-Sleep -Seconds 60
            $Retrycount = $Retrycount + 1
        }
    }
    else {
        az apim product api add --api-id $apiId --resource-group $apimrgName --service-name $apimName --product-id "videoplatformproduct" -o none
        Write-Host "API $apiId imported into $apimName."
        $Stoploop = $true
    }
}
While ($Stoploop -eq $false)

# Getting the Service endpoint to use.
IF ([string]::IsNullOrWhitespace($FrontDoorName)) {
    $url = $basePath
}
else {
    $endpoint = az afd endpoint show --profile-name $FrontDoorName --resource-group  $FrontDoorResourceGroup --endpoint-name $FrontDoorEndpointName --subscription $FrontDoorSubscription | ConvertFrom-Json

    if ($null -eq $endpoint) {
        Write-Host "AFD Endpoint not found."
    }
    else {
        $url = $endpoint.hostName
        $url = "https://$url"
    }
} 

if ($IsApp) {
    $value = ""
}
else {
    # Getting the Function Key.
    $value = az functionapp keys list -g $functionrgName -n $azureFunctionName --query functionKeys.default -o tsv
    $url = "$url/api"
}

Write-Host "##vso[task.setvariable variable=functionKey;isoutput=true;issecret=true]$value"
Write-Host "##vso[task.setvariable variable=serviceHostName;isoutput=true;issecret=false]$url"