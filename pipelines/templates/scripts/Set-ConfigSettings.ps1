<#
.SYNOPSIS
    Update App Configuration Key Vaule pairs.

.DESCRIPTION
    Update App Configuration Key Vaule pairs and remove deprecated key value.

.PARAMETER appConfigurationName
    App Configuration Name

.PARAMETER configPath
    The path for config.json file

.PARAMETER keyVaultName
    Azure Key Vault Name

#>

param
(
    [string] $appConfigurationName = $(throw 'App Configuration name is required'),
    [string] $configPath = $(throw 'Config path is required'),
    [bool] $enforce = $false,
    [string] $keyVaultName = $(throw 'Key Vault name is required')
)
$ErrorActionPreference = 'Stop'

function Set-AppConfigKeyvaultReference($appConfigurationName, $keyName, $keyVaultName, $secretName) {
    az appconfig kv set-keyvault -n $appConfigurationName --key $keyName --secret-identifier https://$keyVaultName.vault.azure.net/secrets/$secretName --yes
}

function Set-AppConfigKeyValuePairs($appConfigurationName, $keyName, $secretName) {
    az appconfig kv set -n $appConfigurationName --key $keyName --value $secretName --yes
}

$data = (Get-Content $configPath | Out-String | ConvertFrom-Json)
$serviceName = $data.ServiceName

$output = @{};
[string[]] $newKeys = @{};

$data.Settings[0] | Get-Member -MemberType *Property | ForEach-Object {
    $output.($_.name) = $data.Settings[0].($_.name);
    $newKeys += $serviceName + ':' + $_.name.Replace("Secret:", "");
}

Write-Host "Checking if there is settings to remove..."
[string] $filter = "${serviceName}:*"
# Getting existing app configuration keys
$existingKeys = (az appconfig kv list -n $appConfigurationName --key $filter --all --query "[*].key") | ConvertFrom-Json

# Selecting key to be removed
$keysToRemove = $existingKeys | Where-Object { 
    $newKeys -notcontains $_ 
}

# Removing keys
foreach ($key in $keysToRemove) {
    az appconfig kv delete -n $appConfigurationName --key $key --yes
}

# Upserting keys
Write-Host "Upserting settings..."
foreach ($key in $output.Keys) {
    $currentname = $key
    $keyName = $serviceName + ':' + $currentname
    Write-Host $keyName -BackgroundColor Green

    if ($currentname.StartsWith("Secret")) {
        $keyName = $keyName.Replace("Secret:", "")
        if ($enforce) {
            Set-AppConfigKeyvaultReference -appConfigurationName $appConfigurationName -keyName $keyName -keyVaultName $keyVaultName -secretName $output.Item($currentname)
            az appconfig kv set-keyvault -n $appConfigurationName --key $keyName --secret-identifier https://$keyVaultName.vault.azure.net/secrets/$output.Item($currentname) --yes
        }
        else {
            try {
                $currentValue = (az appconfig kv show --key $keyName -n $appConfigurationName --query value)
                $currentValue = $currentvalue.Replace('"', '')
                if ($currentvalue -ne $output.Item($currentname)) {
                    Set-AppConfigKeyvaultReference -appConfigurationName $appConfigurationName -keyName $keyName -keyVaultName $keyVaultName -secretName $output.Item($currentname)
                }
            }
            catch {
                Set-AppConfigKeyvaultReference -appConfigurationName $appConfigurationName -keyName $keyName -keyVaultName $keyVaultName -secretName $output.Item($currentname)
            }
        }
    }
    else {
        $newValue = $output.Item($currentname)
        if ([string]::IsNullOrEmpty($newValue)) {
            Write-Host "Empty value for $keyName. Skipping key value pair setting."
        }
        else {
        
            if ($enforce) {
                Set-AppConfigKeyValuePairs -appConfigurationName $appConfigurationName -keyName $keyName -secretName $newValue
            }
            else {
                try {
                    $currentValue = (az appconfig kv show --key $keyName -n $appConfigurationName --query value)
                    $currentValue = $currentvalue.Replace('"', '')
                    if ($currentvalue -ne $newValue) {
                        Set-AppConfigKeyValuePairs -appConfigurationName $appConfigurationName -keyName $keyName -secretName $newValue
                    }
                }
                catch {
                    Set-AppConfigKeyValuePairs -appConfigurationName $appConfigurationName -keyName $keyName -secretName $newValue
                }
            }
        }
    }
}
