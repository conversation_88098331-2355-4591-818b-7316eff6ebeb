{"collections": [{"name": "GmsEntitlementRules", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "GmsEntitlementRules.json"}}, {"name": "GmsTeamZips", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "GmsTeamZips.json"}}, {"name": "Source", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "Source.json"}}, {"name": "VideoPlatformPlayoutAsset", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "VideoPlatformPlayoutAsset.json"}}, {"name": "VideoPlatformSource", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "VideoPlatformSource.json"}}, {"name": "VideoPlatformTemplate", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "VideoPlatformTemplate.json"}}, {"name": "VideoPlatformWorkflow", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "VideoPlatformWorkflow.json"}}, {"name": "EsniAudience", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "EsniAudience.json"}}, {"name": "GmsWatchdogContext", "seedData": {"enabled": true, "clearBeforeSeeding": false, "file": "GmsWatchdogContext.json"}}, {"name": "Whitelist"}, {"name": "VideoPlatformStreamMarker"}, {"name": "VideoPlatformSchedule"}, {"name": "GmsEvent"}, {"name": "GmsGame"}, {"name": "GmsEntitlement"}, {"name": "VideoPlatformBlackout"}, {"name": "VideoPlatformChannel"}]}