// "//-----------------------------------------------------------------------".
// <copyright file="RunningAquilaChannelRequestChangeStrategy.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies
{
    using System;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <inheritdoc/>
    public class RunningAquilaChannelRequestChangeStrategy : IAquilaRequestChangeStrategy
    {
        /// <summary>
        /// The media service.
        /// </summary>
        private readonly IAquilaClientService aquilaClientService;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<RunningAquilaChannelRequestChangeStrategy> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="RunningAquilaChannelRequestChangeStrategy"/> class.
        /// </summary>
        /// <param name="aquilaClientService">The media service.</param>
        /// <param name="telemetryService"> The telemetry service.</param>
        /// <param name="logger">The logger.</param>
        public RunningAquilaChannelRequestChangeStrategy(IAquilaClientService aquilaClientService, ITelemetryService telemetryService, ILogger<RunningAquilaChannelRequestChangeStrategy> logger)
        {
            this.aquilaClientService = aquilaClientService;
            this.telemetryService = telemetryService;
            this.logger = logger;
        }

        /// <inheritdoc/>
        public AquilaChannelState ChannelState => AquilaChannelState.Started;

        /// <inheritdoc/>
        public async Task RequestChangeAsync(AquilaChannelState desiredAquilaChannelState, string instanceId, string channelId, string eventId)
        {
            switch (desiredAquilaChannelState)
            {
                case AquilaChannelState.Started:
                    // nothing
                    break;
                case AquilaChannelState.Starting:
                    // nothing
                    break;
                case AquilaChannelState.Stopping:
                    await this.aquilaClientService.StopChannelInstanceAsync(channelId, instanceId).ConfigureAwait(false);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStopping, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.Stopped:
                    await this.aquilaClientService.StopChannelInstanceAsync(channelId, instanceId).ConfigureAwait(false);
                    this.telemetryService.TrackEvent(eventId, EventTypes.AquilaActorChannelStateToStopped, EventData.CorrelationTag);
                    break;
                case AquilaChannelState.Deleted:
                    this.logger.LogError("RunningAquilaChannelRequestChangeStrategy: Cannot delete channel {ChannelId} because the channel is running", channelId);
                    throw new InvalidOperationException($"Cannot delete channel {channelId} because the channel is running");
                default:
                    throw new NotSupportedException();
            }
        }
    }
}
