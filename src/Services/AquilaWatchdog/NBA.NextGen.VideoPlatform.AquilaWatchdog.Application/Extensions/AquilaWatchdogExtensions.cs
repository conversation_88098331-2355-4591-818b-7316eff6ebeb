// "//-----------------------------------------------------------------------".
// <copyright file="AquilaWatchdogExtensions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Extensions
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// Extension methods for Aquila Watchdog.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public static class AquilaWatchdogExtensions
    {
        /// <summary>
        /// Converts <see cref="AquilaEntityType"/> to <see cref="EventTypes"/>.
        /// </summary>
        /// <param name="eventType">Type of the event.</param>
        /// <returns>The event type.</returns>
        public static string ToNextGenEventType(this AquilaEntityType eventType)
        {
            return eventType switch
            {
                AquilaEntityType.Channel => EventTypes.AquilaChannelUpdated,
                AquilaEntityType.Source => EventTypes.AquilaSourceUpdated,
                AquilaEntityType.WhiteList => EventTypes.AquilaWhitelistUpdated,
                _ => throw new InvalidOperationException($"Can not convert Aquila Event type {eventType} to NextGen Event Type."),
            };
        }
    }
}
