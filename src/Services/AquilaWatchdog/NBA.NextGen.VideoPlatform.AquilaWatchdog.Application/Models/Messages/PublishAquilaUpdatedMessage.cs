// "//-----------------------------------------------------------------------".
// <copyright file="PublishAquilaUpdatedMessage.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Models.Messages
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using Newtonsoft.Json;

    /// <summary>
    /// The publish updated message.
    /// </summary>
    public class PublishAquilaUpdatedMessage : CorrelatedMessage
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="PublishAquilaUpdatedMessage"/> class.
        /// </summary>
        public PublishAquilaUpdatedMessage()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="PublishAquilaUpdatedMessage"/> class.
        /// </summary>
        /// <param name="source">The source.</param>
        public PublishAquilaUpdatedMessage([NotNull] Source source)
        {
            source.Required(nameof(source));
            this.Id = source.Id;
            this.Type = AquilaEntityType.Source;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="PublishAquilaUpdatedMessage"/> class.
        /// </summary>
        /// <param name="channel">The source.</param>
        public PublishAquilaUpdatedMessage([NotNull] Channel channel)
        {
            channel.Required(nameof(channel));
            this.Id = channel.Id;
            this.Type = AquilaEntityType.Channel;
            this.State = channel.State;
            this.CalculatedState = channel.CalculatedState;
            this.ChannelInstances = channel.Instances;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="PublishAquilaUpdatedMessage"/> class.
        /// </summary>
        /// <param name="whitelist">The whitelist.</param>
        public PublishAquilaUpdatedMessage([NotNull] Whitelist whitelist)
        {
            whitelist.Required(nameof(whitelist));
            this.Id = whitelist.Id;
            this.Type = AquilaEntityType.WhiteList;
        }

        /// <summary>
        /// Gets or sets the identifier.
        /// </summary>
        [JsonProperty(Required = Required.Always)]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the type.
        /// </summary>
        public AquilaEntityType Type { get; set; }

        /// <summary>
        /// Gets or sets the state.
        /// </summary>
        public string State { get; set; }

        /// <summary>
        /// Gets or sets the calculated state.
        /// </summary>
        public string CalculatedState { get; set; }

        /// <summary>
        /// Gets or sets the <see cref="ChannelInstance"/>s.
        /// </summary>
        public IEnumerable<ChannelInstance> ChannelInstances { get; set; }
    }
}
