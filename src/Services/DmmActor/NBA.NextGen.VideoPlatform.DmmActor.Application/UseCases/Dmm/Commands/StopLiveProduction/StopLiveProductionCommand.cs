// "//-----------------------------------------------------------------------".
// <copyright file="StopLiveProductionCommand.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Application.UseCases.Dmm.Commands.StopLiveProduction
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Start Content Protection Command.
    /// </summary>
    public class StopLiveProductionCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the MediaID.
        /// </summary>
        public string MediaId { get; set; }
    }
}