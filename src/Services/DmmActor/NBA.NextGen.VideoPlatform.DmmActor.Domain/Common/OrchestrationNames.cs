// "//-----------------------------------------------------------------------".
// <copyright file="OrchestrationNames.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.DmmActor.Domain.Common
{
    /// <summary>
    /// The orchestration names.
    /// </summary>
    public static class OrchestrationNames
    {
        /// <summary>
        /// Start content protection and live production.
        /// </summary>
        public const string StartContentProtectionAndLiveProductionRequest =
            nameof(StartContentProtectionAndLiveProductionRequest);

        /// <summary>
        /// The start DMM api request to start content protection.
        /// </summary>
        public const string StartContentProtectionRequest = nameof(StartContentProtectionRequest);

        /// <summary>
        /// The stop DMM api request to stop content protection.
        /// </summary>
        public const string StopContentProtectionRequest = nameof(StopContentProtectionRequest);

        /// <summary>
        /// The start DMM api request to start content protection.
        /// </summary>
        public const string StartLiveProductionRequest = nameof(StartLiveProductionRequest);

        /// <summary>
        /// The stop DMM api request to stop content protection.
        /// </summary>
        public const string StopLiveProductionRequest = nameof(StopLiveProductionRequest);

        /// <summary>
        /// The Get Content Protection status endpoint.
        /// </summary>
        public const string GetContentProtectionStatus = nameof(GetContentProtectionStatus);

        /// <summary>
        /// The Get Live Production status endpoint.
        /// </summary>
        public const string GetLiveProductionStatus = nameof(GetLiveProductionStatus);

        /// <summary>
        /// The Get Content Protection status endpoint.
        /// </summary>
        public const string ContentProtectionStart = nameof(ContentProtectionStart);

        /// <summary>
        /// The Get LiveProductionStart start endpoint.
        /// </summary>
        public const string LiveProductionStart = nameof(LiveProductionStart);

        /// <summary>
        /// The Get Content Protection status endpoint.
        /// </summary>
        public const string ContentProtectionStop = nameof(ContentProtectionStop);

        /// <summary>
        /// The Get Content Protection status endpoint.
        /// </summary>
        public const string LiveProductionStop = nameof(LiveProductionStop);

        /// <summary>
        /// The Get latest content protection data.
        /// </summary>
        public const string LatestContentProtectionData = nameof(LatestContentProtectionData);

        /// <summary>
        /// The Get the Dmm Acot name.
        /// </summary>
        public const string DmmSubscriptionWorkflowInstancePrepend = "DmmActor";
    }
}
