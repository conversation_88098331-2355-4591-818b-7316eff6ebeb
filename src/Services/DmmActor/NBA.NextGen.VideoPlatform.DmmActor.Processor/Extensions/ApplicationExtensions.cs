using System.Diagnostics.CodeAnalysis;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Interfaces;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Strategies;
using NBA.NextGen.VideoPlatform.DmmActor.Application.Strategies.Factories;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.DmmActor.Application;
using Azure.Messaging.ServiceBus;
using Azure.Identity;
using MST.Common.Azure.Extensions;
using Microsoft.Extensions.Options;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using Microsoft.Azure.Cosmos;
using MST.Common.Azure.Data;
using NBA.NextGen.Shared.Infrastructure.Data;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
using MST.Common.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
using MST.Common.AWS.Extensions;
using Amazon.SQS;
using MST.Common.MongoDB.Extensions;
using MongoDB.Driver;
using Azure;
using Azure.Messaging.EventGrid;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

namespace NBA.NextGen.VideoPlatform.DmmActor.Processor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{
    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddApplication();
        services.AddEventGrid(configuration);
        services.AddSharedInfrastructure();
        services.AddVideoPlatformSharedInfrastructure(configuration);
        services.AddHttpClient();
        services.AddBlobClient(configuration);
        services.AddAquilaSharedInfrastructure(configuration);
        services.AddTvpSharedInfrastructure(configuration);
        services.AddDmmSharedInfrastructure();
        services.AddRefitClients(configuration);
        services.AddApplicationStrategies();

        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();
        var queueName = configuration.GetSection("SQS")["DmmStateChangeRequest"] ?? throw new NullReferenceException();
        var queueName_dl = configuration.GetSection("SQS")["DmmStateChangeRequest_DL"] ?? throw new NullReferenceException();
        var eventQueue = configuration.GetSection("SQS")["EventGridEventsOrchestratorQueue"] ?? throw new NullReferenceException();
        services.RegisterSQSSender<InfrastructureStateChangedEventSQS>(eventQueue, _ => Guid.NewGuid().ToString());
        services.RegisterSQSConsumer<ReceivedMessageEventHandler>(queueName, queueName_dl);

        services.AddMSTInfrastructure();

        services.Configure<NotifierSettings>(configuration.GetSection("NotifierSettings"));
        services.Configure<DataProviders>(configuration.GetSection("DataProviders"));
        services.AddHttpClient<CosmosDbHelper>();
        services.AddData(configuration);

        // Add Event Grid Notifier for VideoPlatform Topic
        var provider = services.BuildServiceProvider();
        var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

        var vpNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.VideoPlatform);
        if (vpNotifierSettings is null)
        {
            throw new NullReferenceException();
        }

        var eventKey = new AzureKeyCredential(configuration["event_grid_key"]);
        var egClient = new EventGridPublisherClient(vpNotifierSettings.EndpointUri, eventKey);
        services.AddKeyedSingleton(TopicNames.VideoPlatform, egClient);
        services.RegisterEventGridSender<ServiceHealthChangedEvent>(nameof(ServiceHealthChangedEvent), x => nameof(ServiceHealthChangedEvent), keyedServiceName: TopicNames.VideoPlatform);

        
        return services;
    }
    public static void AddApplicationStrategies(this IServiceCollection services)
    {
        services.AddSingleton<IDmmStrategyFactory, DmmStrategyFactory>();
        services.AddSingleton<IDmmStrategy, StartContentProtectionAndLiveProductionStrategy>();
        services.AddSingleton<IDmmStrategy, StopLiveProductionStrategy>();
        services.AddSingleton<IDmmStrategy, StartContentProtectionStrategy>();
        services.AddSingleton<IDmmStrategy, StopContentProtectionStrategy>();
        services.AddSingleton<IDmmStrategy, StartLiveProductionStrategy>();
    }
}
