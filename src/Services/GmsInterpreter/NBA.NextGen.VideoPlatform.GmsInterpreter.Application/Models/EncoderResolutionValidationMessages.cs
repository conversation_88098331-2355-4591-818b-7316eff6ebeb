// "//-----------------------------------------------------------------------".
// <copyright file="EncoderResolutionValidationMessages.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Models
{
    /// <summary>
    /// EncoderResolutionValidationMessages.
    /// </summary>
    public static class EncoderResolutionValidationMessages
    {
        /// <summary>
        /// Gets the OK Message.
        /// </summary>
        public static string OKMessage => "OK";

        /// <summary>
        /// Gets the EncoderEmptyOrNull Message.
        /// </summary>
        public static string EncoderEmptyOrNullMessage => "Encoder is null or empty for channel {0}, {1} {2}";

        /// <summary>
        /// Gets the InvalidVideoPlatformSource Message.
        /// </summary>
        public static string InvalidVideoPlatformSourceMessage => "Encoder value for channel {0}, {1} {2} is {3}, but there is no VideoPlatformSource that maps to that encoder value";

        /// <summary>
        /// Gets the InvalidAquilaSource Message.
        /// </summary>
        public static string InvalidAquilaSourceMessage => "Encoder value for channel {0}, {1} {2} is {3} which maps to Aquila source with name {4}, but there is no source with that name in Aquila";

        /// <summary>
        /// Gets the InvalidVideoPlatformTemplate Message.
        /// </summary>
        public static string InvalidVideoPlatformTemplateMessage => "Resolution value for channel {0}, {1} {2} is \"{3}\", but there is no VideoPlatformTemplate that maps to that resolution value";

        /// <summary>
        /// Gets the InvalidAquilaTemplate Message.
        /// </summary>
        public static string InvalidAquilaTemplateMessage => "Resolution value for channel {0}, {1} {2} is {3} which maps to Aquila template with name {4}, but there is no template with that name in Aquila";
    }
}
