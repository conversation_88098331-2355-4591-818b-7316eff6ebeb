using System.Diagnostics.CodeAnalysis;
using Amazon.SQS;
using Azure.Identity;
using Azure.Messaging.ServiceBus;
using GmsWatchdog.Processor;
using MST.Common.AWS.Extensions;
using MST.Common.Azure.Extensions;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Application;
using NBA.NextGen.VideoPlatform.GmsWatchdog.Infrastructure;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Processor.Extensions;

[ExcludeFromCodeCoverage]
public static class ApplicationExtensions
{

    public static IServiceCollection AddApplicationDependencies(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddApplication();
        services.AddEventGrid(configuration);
        configuration["QueueSettings:UseWebSockets"] = "true";
        services.AddInfrastructure(configuration);
        services.AddAquilaSharedInfrastructure(configuration);
        services.AddTvpSharedInfrastructure(configuration);
        services.AddServiceHandlers(configuration);

        return services;
    }

    public static IServiceCollection AddServiceHandlers(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddSingleton<IAmazonSQS, AmazonSQSClient>();

        var queueName = configuration.GetSection("SQS")["GmsWatchdogEventing"] ?? throw new NullReferenceException();
        var queueName_dl = configuration.GetSection("SQS")["GmsWatchdogEventing_DL"] ?? throw new NullReferenceException();

        services.RegisterSQSConsumer<ReceivedMessagePublishEventHandler>(queueName, queueName_dl);

        return services;
    }
}