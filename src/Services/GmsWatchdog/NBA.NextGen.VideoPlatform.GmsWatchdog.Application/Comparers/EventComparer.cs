// "//-----------------------------------------------------------------------".
// <copyright file="EventComparer.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Comparers
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;

    /// <summary>
    /// The event comparer.
    /// </summary>
    public class EventComparer : IComparer<GmsEvent>
    {
        /// <inheritdoc/>
        public int Compare([NotNull] GmsEvent x, [AllowNull] GmsEvent y)
        {
            x.Required(nameof(x));
            if (y is null || x.LastUpdated > y.LastUpdated)
            {
                return 1;
            }

            foreach (var newMedia in x.Media)
            {
                var oldMedia = y.Media.FirstOrDefault(x => x.Id == newMedia.Id);
                if (oldMedia is null
                    || newMedia.LastUpdated > oldMedia.LastUpdated)
                {
                    return 1;
                }

                // using a normal for because schedules do not have an identifier, we are assuming they come ordered
                for (var i = 0; i < newMedia.Schedules.ToList().Count; i++)
                {
                    var newSchedule = newMedia.Schedules.ElementAtOrDefault(i);
                    var oldSchedule = oldMedia.Schedules.ElementAtOrDefault(i);
                    if (oldSchedule is null
                        || newSchedule.LastUpdated > oldSchedule.LastUpdated
                        || newSchedule.Operations.LastUpdated > oldSchedule.Operations.LastUpdated)
                    {
                        return 1;
                    }
                }
            }

            return 0;
        }
    }
}
