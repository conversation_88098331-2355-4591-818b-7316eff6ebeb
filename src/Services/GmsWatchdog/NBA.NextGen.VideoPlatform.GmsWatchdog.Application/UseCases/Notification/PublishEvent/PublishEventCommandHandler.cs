namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Application.UseCases.Notification.Commands.PublishEvent
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.GmsWatchdog.Application.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;

    public class PublishEventCommandHandler : IRequestHandler<PublishEventCommand, Unit>
    {
        private readonly IMessageSender<GmsUpdatedEvent> _eventNotifier;
        private readonly IMessageSender<GmsUpdatedEventSQS> _queueClient;

        private readonly IMapper _mapper;

        private readonly ILogger<PublishEventCommandHandler> _logger;

        private readonly ITelemetryService _telemetryService;

        public PublishEventCommandHandler(
            [NotNull] IMessageSenderFactory eventProvider,
            [NotNull] IMessageSenderFactory queueClientprovider,
            IMapper mapper,
            ILogger<PublishEventCommandHandler> logger,
            ITelemetryService telemetryService)
        {
            eventProvider.Required(nameof(eventProvider));
            _mapper = mapper;
            _logger = logger;
            _eventNotifier = eventProvider.Resolve<GmsUpdatedEvent>();
            _telemetryService = telemetryService;
            _queueClient = queueClientprovider.Resolve<GmsUpdatedEventSQS>();
        }

        public async Task<Unit> Handle([NotNull] PublishEventCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            _logger.LogInformation("Publishing event for {EventType} with id {Id} ", request.Type, request.Id);

            var gmsUpdatedEvent = _mapper.Map<GmsUpdatedEvent>(request);
            var gmsUpdatedEventSQS = _mapper.Map<GmsUpdatedEventSQS>(request);

            await _eventNotifier.SendAsync(gmsUpdatedEvent);
            await _queueClient.SendAsync(gmsUpdatedEventSQS);

            _logger.LogInformation("Event published for {EventType} with id {Id} ", request.Type, request.Id);
            _telemetryService.TrackEvent(request.Id, EventData.EventNotifiedToProcessor, EventData.CorrelationTag);
            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
