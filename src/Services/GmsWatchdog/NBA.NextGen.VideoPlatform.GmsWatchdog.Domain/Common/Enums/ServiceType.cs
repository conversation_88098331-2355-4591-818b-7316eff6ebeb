// "//-----------------------------------------------------------------------".
// <copyright file="ServiceType.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.Common.Enums
{
    using System.Runtime.Serialization;

    /// <summary>
    /// The type of service that is running.
    /// </summary>
    public enum ServiceType
    {
        /// <summary>
        /// Game
        /// </summary>
        [EnumMember(Value = "game")]
        Game,

        /// <summary>
        /// Event
        /// </summary>
        [EnumMember(Value = "event")]
        Event,

        /// <summary>
        /// Event
        /// </summary>
        [EnumMember(Value = "teamzips")]
        TeamZips,
    }
}
