<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    
    <Features>IOperation</Features>
    <Features>$(Features);flow-analysis</Features>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    <Compile Include="..\..\..\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\..\..\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="AWSSDK.SQS" Version="4.0.0.2" />
    <PackageReference Include="NBA.NextGen.Shared.infrastructure" Version="2.0.1" />
    <PackageReference Include="NBA.NextGen.Vendor.Api.Gms" Version="1.3.25" />
    <PackageReference Include="Serilog" Version="2.10.0" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="3.0.1" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="3.1.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="3.1.1" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
    <PackageReference Include="MST.Common.Azure" Version="2.5.0" />
    <PackageReference Include="MST.Common.MongoDB" Version="0.3.1-alpha" />
    <PackageReference Include="MST.Common.AWS" Version="0.3.6-beta" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj">
      <PrivateAssets>All</PrivateAssets>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj">
      <PrivateAssets>All</PrivateAssets>
    </ProjectReference>
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.GmsWatchdog.Application\NBA.NextGen.VideoPlatform.GmsWatchdog.Application.csproj">
      <PrivateAssets>All</PrivateAssets>
    </ProjectReference>
    <ProjectReference Include="..\NBA.NextGen.VideoPlatform.GmsWatchdog.Domain\NBA.NextGen.VideoPlatform.GmsWatchdog.Domain.csproj">
      <PrivateAssets>All</PrivateAssets>
    </ProjectReference>
  </ItemGroup>

</Project>
