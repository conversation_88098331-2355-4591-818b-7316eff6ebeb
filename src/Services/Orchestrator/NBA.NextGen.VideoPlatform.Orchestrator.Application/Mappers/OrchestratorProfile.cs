// "//-----------------------------------------------------------------------".
// <copyright file="OrchestratorProfile.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Application.Mappers
{
    using System;
    using AutoMapper;
    using NBA.NextGen.VideoPlatform.Orchestrator.Application.UseCases.Orchestration.Commands;
    using NBA.NextGen.VideoPlatform.Orchestrator.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Application.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Orchestrator AutoMapper Profile.
    /// </summary>
    public class OrchestratorProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="OrchestratorProfile"/> class.
        /// </summary>
        public OrchestratorProfile()
        {
            this.CreateMap<ActorSpecificDetail, OrchestratorRequestActorSpecificDetail>();
            this.CreateMap<OrchestratorRequestActorSpecificDetail, ActorSpecificDetail>();

            this.CreateMap<WorkflowRequest, OrchestratorRequest>()
                .ForMember(x => x.InfrastructureId, y => y.MapFrom(z => z.WorkflowIntent.ChannelId))
                .ForMember(x => x.WorkflowId, y => y.MapFrom(z => z.WorkflowIntent.WorkflowId))
                .ForMember(x => x.LiveEventTime, y => y.MapFrom(z => z.WorkflowIntent.LiveEventTime))
                .ForMember(x => x.ContinueOnError, y => y.MapFrom(z => z.WorkflowIntent.ContinueOnError))
                .ForMember(x => x.ActorSpecificDetails, y => y.MapFrom(z => z.WorkflowIntent.ActorSpecificDetails))
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(z => Guid.NewGuid().ToString()))
                .ForMember(x => x.ExternalSystemInfrastructureId, y => y.MapFrom(z => z.WorkflowIntent.ChannelId))
                .ForMember(x => x.WorkflowState, y => y.Ignore())
                .ForMember(x => x.Result, y => y.Ignore());

            this.CreateMap<OrchestratorRequest, PublishRequestAcknowledgementEventCommand>()
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(z => z.LongRunningOperationId))
                .ForMember(x => x.ReceivedRequestActorId, y => y.MapFrom(z => z.RequestorActorId))
                .ForMember(x => x.ReceivedRequestId, y => y.MapFrom(z => z.RequestId));

            this.CreateMap<PublishRequestAcknowledgementEventCommand, RequestAcknowledgementEvent>()
                .ForMember(x => x.RequestIdAcknowledged, y => y.MapFrom(z => z.ReceivedRequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, y => y.MapFrom(z => z.ReceivedRequestActorId))
                .ForMember(x => x.AcknowledgedByActorId, y => y.MapFrom(z => ActorIds.Orchestrator));

            this.CreateMap<OrchestratorRequest, SendInfrastructureStateChangeRequestCommand>()
                .ForMember(x => x.DesiredState, y => y.ConvertUsing(new NbaWorkflowToInfrastructureStateConverter(), z => z.WorkflowId))
                .ForMember(x => x.ActorId, y => y.Ignore())
                .ForMember(x => x.ActorSpecificDetails, y => y.MapFrom((src, dest, destMember, resContext) => resContext.Items[nameof(OrchestratorRequestActorSpecificDetail)]))
                .AfterMap((s, d) =>
                {
                    d.ActorId = d.ActorSpecificDetails.ActorId;
                });

            this.CreateMap<SendInfrastructureStateChangeRequestCommand, InfrastructureStateChangeRequest>()
                .ForMember(x => x.ActorSpecificDetail, y => y.Ignore())
                .ForMember(x => x.RequestorActorId, y => y.MapFrom(z => ActorIds.Orchestrator));

            this.CreateMap<object, InfrastructureStateChangedEvent>().ConvertUsing(new CustomEventTypeConverter<object, InfrastructureStateChangedEvent>());

            this.CreateMap<PublishWorkflowStateUpdatedEventCommand, ActorWorkflowRequestLiveEventChannelState>()
                .ForMember(x => x.LastUpdateTime, y => y.MapFrom(z => DateTime.Now))
                .ForMember(x => x.DesiredState, y => y.MapFrom(z => z.InfrastructureState));

            this.CreateMap<OrchestratorRequest, PublishWorkflowStateUpdatedEventCommand>()
                .ForMember(x => x.InfrastructureState, y => y.MapFrom(z => z.Result));

            this.CreateMap<PublishWorkflowStateUpdatedEventCommand, WorkflowStateChangedEvent>();
        }
    }
}
