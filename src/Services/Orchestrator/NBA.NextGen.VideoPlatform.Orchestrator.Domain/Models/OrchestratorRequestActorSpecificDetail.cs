// "//-----------------------------------------------------------------------".
// <copyright file="OrchestratorRequestActorSpecificDetail.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Orchestrator.Domain.Models
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// The live event actor specific detail.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class OrchestratorRequestActorSpecificDetail
    {
        /// <summary>
        /// Gets or sets the actorid.
        /// </summary>
        public string ActorId { get; set; }

        /// <summary>
        /// Gets or sets the data which the Actor needs to do work.
        /// </summary>
        public object Data { get; set; }
    }
}
