<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" Sdk="Microsoft.Docker.Sdk" DefaultTargets="Build">
    <PropertyGroup Label="Globals">
        <ProjectVersion>2.1</ProjectVersion>
        <DockerTargetOS>Linux</DockerTargetOS>
        <DockerLaunchAction>LaunchBrowser</DockerLaunchAction>
        <ProjectGuid>9466AD57-AA14-4559-BB5D-C207C6BAFA5A</ProjectGuid>
        <DockerLaunchBrowser>True</DockerLaunchBrowser>
        <DockerServiceUrl>{Scheme}://localhost:{ServicePort}/swagger</DockerServiceUrl>
        <DockerServiceName>third-party-actor</DockerServiceName>
        <DockerLaunchBrowser>true</DockerLaunchBrowser>
        <AdditionalComposeFilePaths>local-compose.yml;env-compose.yml</AdditionalComposeFilePaths>
    </PropertyGroup>
    <ItemGroup>
        <None Include="docker-compose.yml" />
        <None Include=".dockerignore" />
        <None Include="local-compose.yml" />
    </ItemGroup>
</Project>