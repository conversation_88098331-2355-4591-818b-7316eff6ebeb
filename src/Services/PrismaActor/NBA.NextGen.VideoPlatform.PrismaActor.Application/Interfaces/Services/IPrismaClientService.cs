// "//-----------------------------------------------------------------------".
// <copyright file="IPrismaClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading.Tasks;
    using NBA.NextGen.Shared.Application.HealthDetails;
    using NBA.NextGen.Vendor.Api.MkPrismaWorker;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;

    /// <summary>
    /// The prisma service.
    /// </summary>
    public interface IPrismaClientService : IHealthStatus
    {
        /// <summary>
        /// Makes an API call to PRISMA to ensure the service is healthy. If it is not, it throws an <see cref="MkPrismaWorkerClientException"/>.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task HealthCheckAsync();

        /// <summary>
        /// Get the esni resource asynchronous.
        /// </summary>
        /// <param name="resourceId">The resource identifier.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<EsniBaseEntity> GetEsniResourceAsync(string resourceId);

        /// <summary>
        /// Gets the esni resource asynchronous, deserializing the result as T.
        /// </summary>
        /// <typeparam name="T">The ESNI resource type.</typeparam>
        /// <param name="resourceId">The resource identifier.</param>
        /// <returns>The ESNI resource.</returns>
        Task<T> GetEsniResourceAsync<T>([NotNull] string resourceId)
            where T : EsniBaseEntity;

        /// <summary>
        /// Get the esni resource asynchronous or null when not found.
        /// </summary>
        /// <param name="resourceId">The resource identifier.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task<EsniBaseEntity> TryGetEsniResourceAsync([NotNull] string resourceId);

        /// <summary>
        /// Get the esni resource asynchronous or null when not found.
        /// </summary>
        /// <typeparam name="T">The type of the ESNI entity.</typeparam>
        /// <param name="resourceId">The resource identifier.</param>
        /// <returns>The ESNI entity.</returns>
        Task<T> TryGetEsniResourceAsync<T>([NotNull] string resourceId)
            where T : EsniBaseEntity;

        /// <summary>
        /// Deletes the esni resource asynchronous.
        /// </summary>
        /// <param name="resourceId">The resource identifier.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task DeleteEsniResourceAsync(string resourceId);

        /// <summary>
        /// Upserts the esni media asynchronous.
        /// </summary>
        /// <param name="esniData">The esni data.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        Task UpsertEsniMediaAsync([NotNull] PrismaMedia esniData);

        /// <summary>
        /// Upserts the esni policy asynchronous.
        /// </summary>
        /// <param name="esniData">The esni data.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        Task UpsertEsniPolicyAsync([NotNull] PrismaPolicy esniData);

        /// <summary>
        /// Upserts the esni viewing policy asynchronous.
        /// </summary>
        /// <param name="esniData">The esni data.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        Task UpsertEsniViewingPolicyAsync(PrismaViewingPolicy esniData);

        /// <summary>
        /// Upserts the esni viewing policy asynchronous.
        /// </summary>
        /// <param name="esniData">The esni data.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        Task UpsertEsniAudienceAsync(PrismaAudience esniData);

        /// <summary>
        /// Updates the media point match time.
        /// </summary>
        /// <param name="esniMediaId">The ESNI media identifier.</param>
        /// <param name="mediaPointId">The media point identifier.</param>
        /// <param name="matchTime">The match time.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        Task UpdateMediaPointMatchTimeAsync(string esniMediaId, string mediaPointId, DateTimeOffset matchTime);

        /// <summary>
        /// Updates the media point policy duration.
        /// </summary>
        /// <param name="esniMediaId">The ESNI media identifier.</param>
        /// <param name="mediaPointId">The media point identifier.</param>
        /// <param name="policyId">The policy identifier.</param>
        /// <param name="duration">The duration.</param>
        /// <returns>
        /// A <see cref="Task" /> representing the asynchronous operation.
        /// </returns>
        Task UpdateMediaPointApplyDurationAsync([NotNull] string esniMediaId, string mediaPointId, string policyId, TimeSpan duration);
    }
}
