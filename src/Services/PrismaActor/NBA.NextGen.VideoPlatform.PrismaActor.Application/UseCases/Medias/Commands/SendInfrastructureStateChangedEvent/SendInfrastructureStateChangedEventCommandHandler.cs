namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    public class SendInfrastructureStateChangedEventCommandHandler : IRequestHandler<SendInfrastructureStateChangedEventCommand, Unit>
    {
        private readonly IMessageSender<InfrastructureStateChangedEvent> _eventNotifier;
        private readonly IMessageSender<InfrastructureStateChangedEventSQS> _queueClient;

        private readonly IMapper _mapper;

        private readonly ILogger<SendInfrastructureStateChangedEventCommandHandler> _logger;

        private readonly ITelemetryService _telemetryService;

        public SendInfrastructureStateChangedEventCommandHandler([NotNull] IMessageSenderFactory eventNotifierProvider, [NotNull] IMessageSenderFactory queueClientprovider, IMapper mapper, ILogger<SendInfrastructureStateChangedEventCommandHandler> logger, ITelemetryService telemetryService)
        {
            eventNotifierProvider.Required(nameof(eventNotifierProvider));
            queueClientprovider.Required(nameof(queueClientprovider));
            _eventNotifier = eventNotifierProvider.Resolve<InfrastructureStateChangedEvent>();
            _mapper = mapper;
            _logger = logger;
            _telemetryService = telemetryService;
            _queueClient = queueClientprovider.Resolve<InfrastructureStateChangedEventSQS>();
        }

        public async Task<Unit> Handle([NotNull] SendInfrastructureStateChangedEventCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            _logger.LogInformation("Triggered Send Infrastructure State Changed for LongRunningOperationId {Id}", request.LongRunningOperationId);
            var changedNotification = _mapper.Map<InfrastructureStateChangedEvent>(request);
            await _eventNotifier.SendAsync(changedNotification).ConfigureAwait(false);
            var changedNotificationQueue = _mapper.Map<InfrastructureStateChangedEventSQS>(request);
            await _queueClient.SendAsync(changedNotificationQueue);

            if (!string.IsNullOrEmpty(request.EventId))
            {
                _telemetryService.TrackEvent(request.EventId, EventTypes.PrismaActorInfrastructureStateChanged, EventData.CorrelationTag);
            }

            _logger.LogInformation("Executed  Send Infrastructure State Changed for LongRunningOperationId {Id}", request.LongRunningOperationId);
            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
