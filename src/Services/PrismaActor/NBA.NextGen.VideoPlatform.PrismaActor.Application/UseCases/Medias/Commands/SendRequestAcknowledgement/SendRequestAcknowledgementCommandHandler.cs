// "//-----------------------------------------------------------------------".
// <copyright file="SendRequestAcknowledgementCommandHandler.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands
{
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Extensions.Logging;
    using MST.Common.Messaging;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Handles raise events command.
    /// </summary>
    /// <seealso cref="MediatR.IRequestHandler{RaiseChannelEventCommand, Unit}" />
    public class SendRequestAcknowledgementCommandHandler : IRequestHandler<SendRequestAcknowledgementCommand, Unit>
    {
        /// <summary>
        /// The Prisma Event Notifier.
        /// </summary>
        private readonly IMessageSender<RequestAcknowledgementEvent> eventNotifier;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<SendRequestAcknowledgementCommandHandler> logger;

        /// <summary>
        /// The telemetry service.
        /// </summary>
        private readonly ITelemetryService telemetryService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SendRequestAcknowledgementCommandHandler" /> class.
        /// </summary>
        /// <param name="eventNotifierProvider">The Prisma Event Notifier.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="telemetryService">The telemetry services.</param>
        public SendRequestAcknowledgementCommandHandler([NotNull] IMessageSenderFactory eventNotifierProvider, IMapper mapper, ILogger<SendRequestAcknowledgementCommandHandler> logger, ITelemetryService telemetryService)
        {
            eventNotifierProvider.Required(nameof(eventNotifierProvider));
            this.eventNotifier = eventNotifierProvider.Resolve<RequestAcknowledgementEvent>();
            this.mapper = mapper;
            this.logger = logger;
            this.telemetryService = telemetryService;
        }

        /// <summary>
        /// Handles a request.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <returns>
        /// Response from the request.
        /// </returns>
        public async Task<Unit> Handle([NotNull] SendRequestAcknowledgementCommand request, CancellationToken cancellationToken)
        {
            request.Required(nameof(request));
            this.logger.LogInformation("Triggered Send Channel State Change Request Acknowledgement Command Handler for ReceivedRequestId {Id}", request.ReceivedRequestId);
            var acknowledgementNotification = this.mapper.Map<RequestAcknowledgementEvent>(request);
            await this.eventNotifier.SendAsync(acknowledgementNotification).ConfigureAwait(false);
            if (!string.IsNullOrEmpty(request.EventId))
            {
                this.telemetryService.TrackEvent(request.EventId, EventTypes.PrismaActorRequestAcknowledgement, EventData.CorrelationTag);
            }

            this.logger.LogInformation("Executed Send Channel State Change Request Acknowledgement Command Handler for ReceivedRequestId {Id}", request.ReceivedRequestId);
            return await Unit.Task.ConfigureAwait(false);
        }
    }
}
