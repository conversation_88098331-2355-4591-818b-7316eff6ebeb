// "//-----------------------------------------------------------------------".
// <copyright file="UpsertEsniMediaCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands
{
    using System.Collections.Generic;
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;

    /// <summary>
    /// The Upsert Esni Resource Command.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class UpsertEsniMediaCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the medias to add.
        /// </summary>
        /// <value>
        /// The medias to add.
        /// </value>
        public IEnumerable<PrismaMedia> MediasToUpsert { get; set; }

        /// <summary>
        /// Gets or sets the medias to delete.
        /// </summary>
        /// <value>
        /// The medias to add.
        /// </value>
        public IEnumerable<string> EsniResourceIdsToDelete { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the long-running operation, if applicable.
        /// </summary>
        /// <value>
        /// The identifier.
        /// </value>
        public string LongRunningOperationId { get; set; }

        /// <summary>
        /// Gets or sets the Event Id.
        /// </summary>
        public string EventId { get; set; }
    }
}
