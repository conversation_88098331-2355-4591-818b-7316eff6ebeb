// "//-----------------------------------------------------------------------".
// <copyright file="OpenApiConfigurationOptions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Scheduler.Function.Configuration
{
    using System.Diagnostics.CodeAnalysis;
    using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Configurations;
    using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
    using Microsoft.OpenApi.Models;

    /// <summary>
    /// OpenApiConfigurationOptions.
    /// </summary>
    /// <seealso cref="DefaultOpenApiConfigurationOptions" />
    [ExcludeFromCodeCoverage]
    public class OpenApiConfigurationOptions : DefaultOpenApiConfigurationOptions
    {
        /// <summary>
        /// Gets or sets info.
        /// </summary>
        public override OpenApiInfo Info { get; set; } = new OpenApiInfo
        {
            Version = "1.0.0",
            Title = "Scheduler-OrchestratorAPI",
            Description = "Scheduler-OrchestratorAPI",
        };

        /// <summary>
        /// Open APi Version.
        /// </summary>
        /// <inheritdoc />
        public override OpenApiVersionType OpenApiVersion { get; set; } = OpenApiVersionType.V2;
    }
}
