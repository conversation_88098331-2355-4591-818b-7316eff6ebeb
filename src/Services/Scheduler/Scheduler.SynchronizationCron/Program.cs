using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NBA.NextGen.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.Scheduler.Application;
using NBA.NextGen.VideoPlatform.Scheduler.Application.UseCases.Evaluation.Commands.Evaluate;
using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
using MST.Common.Extensions;
using Microsoft.Extensions.Options;
using NBA.NextGen.Shared.Infrastructure.EventGrid;
using Azure.Identity;
using MST.Common.Azure.Extensions;
using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
using Microsoft.Azure.Cosmos;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformBlackouts.Model;
using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
using MST.Common.Azure.Data;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Repositories;
using NBA.NextGen.Shared.Infrastructure.Data;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformTemplates.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSources.Entities;
using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformPlayoutAssets.Entities;
using Azure;
using Azure.Messaging.EventGrid;
using MongoDB.Driver;
using MST.Common.MongoDB.Extensions;

var builder = Host.CreateApplicationBuilder(args);

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory())
        .AddJsonFile("appsettings.json", false, true)
        .AddParameterStore(builder.Configuration)
        .AddAmazonSecretsManager(builder.Configuration)
        .AddEnvironmentVariables();


builder.Services.AddSharedInfrastructure();
builder.Services.AddVideoPlatformSharedInfrastructure(builder.Configuration);
builder.Services.AddAquilaSharedInfrastructure(builder.Configuration);
builder.Services.AddTvpSharedInfrastructure(builder.Configuration);
builder.Services.AddApplicationV2(builder.Configuration);
builder.Services.AddEventGrid(builder.Configuration);
builder.Services.AddSingleton<Microsoft.ApplicationInsights.TelemetryClient>();
builder.Services.AddDmmSharedInfrastructure();
builder.Services.AddRefitClients(builder.Configuration);

builder.Services.AddMSTInfrastructure();
// Add Event Grid Notifier for VideoPlatform Topic
builder.Services.Configure<NotifierSettings>(builder.Configuration.GetSection("NotifierSettings"));
builder.Services.Configure<DataProviders>(builder.Configuration.GetSection("DataProviders"));
builder.Services.AddHttpClient<CosmosDbHelper>();

var provider = builder.Services.BuildServiceProvider();
var notifierSettings = provider.GetService<IOptions<NotifierSettings>>();

var vpNotifierSettings = notifierSettings?.Value.Topics.FirstOrDefault(t => t.Name == TopicNames.VideoPlatform);
if (vpNotifierSettings is null)
{
    throw new NullReferenceException();
}

var eventKey = new AzureKeyCredential(builder.Configuration["event_grid_key"]);
var vpEGClient = new EventGridPublisherClient(vpNotifierSettings.EndpointUri, eventKey);
builder.Services.AddKeyedSingleton(vpNotifierSettings.Name, vpEGClient);
builder.Services.RegisterEventGridSender<WorkflowStateChangedEvent>(EventTypes.WorkflowStateChanged, x => EventTypes.WorkflowStateChanged, keyedServiceName: vpNotifierSettings.Name);
builder.Services.RegisterEventGridSender<ScheduleEvaluationEvent>(EventTypes.ScheduleEvaluation, x => EventTypes.ScheduleEvaluation, keyedServiceName: vpNotifierSettings.Name);


var dataProviders = provider.GetService<IOptions<DataProviders>>();
var cosmosDataProvider = dataProviders.Value.FirstOrDefault(d => d.Name == "Cosmos");
var mongoDataProvider = dataProviders?.Value.FirstOrDefault(d => d.Name == "Mongo");

if (cosmosDataProvider is null)
{
    throw new NullReferenceException("Could not find Cosmos Data Provider in Configuration");
}

if (mongoDataProvider is null)
{
    throw new NullReferenceException("Could not find Mongo Data Provider in Configuration");
}

var cosmosDbClient = new CosmosClient(builder.Configuration["cosmos_connection_string"], new CosmosClientOptions
{
    Serializer = new CosmosDataSerializer(),
    ConnectionMode = ConnectionMode.Gateway
});

var mongoDbClient = new MongoClient(
    builder.Configuration["docdb_connection_string"] ?? 
    throw new InvalidOperationException("MongoDB connection string not found"));

builder.Services.AddSingleton(cosmosDbClient);
builder.Services.AddSingleton(mongoDbClient);

builder.Services.RegisterCosmosContainer<GmsEntitlement>(cosmosDataProvider.Database, "GmsEntitlement", g => g.Id, _ => typeof(GmsEntitlement).Name, keyOverride: $"cosmos_{typeof(GmsEntitlement).Name}");
builder.Services.RegisterMongoDBRepository<GmsEntitlement>(mongoDataProvider.Database, "GmsEntitlement", g => g.Id, keyOverride:  $"mongo_{typeof(GmsEntitlement).Name}");
builder.Services.RegisterDualWriteRepository<GmsEntitlement>($"mongo_{typeof(GmsEntitlement).Name}", $"cosmos_{typeof(GmsEntitlement).Name}");

builder.Services.RegisterCosmosContainer<VideoPlatformSchedule>(cosmosDataProvider.Database, "VideoPlatformSchedule", g => g.Id, _ => typeof(VideoPlatformSchedule).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSchedule).Name}");
builder.Services.RegisterMongoDBRepository<VideoPlatformSchedule>(mongoDataProvider.Database, "VideoPlatformSchedule", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformSchedule).Name}");
builder.Services.RegisterDualWriteRepository<VideoPlatformSchedule>($"mongo_{typeof(VideoPlatformSchedule).Name}", $"cosmos_{typeof(VideoPlatformSchedule).Name}");

builder.Services.RegisterCosmosContainer<GmsGame>(cosmosDataProvider.Database, "GmsGame", g => g.Id, _ => typeof(GmsGame).Name, keyOverride: $"cosmos_{typeof(GmsGame).Name}");
builder.Services.RegisterMongoDBRepository<GmsGame>(mongoDataProvider.Database, "GmsGame", g => g.Id, keyOverride:  $"mongo_{typeof(GmsGame).Name}");
builder.Services.RegisterDualWriteRepository<GmsGame>($"mongo_{typeof(GmsGame).Name}", $"cosmos_{typeof(GmsGame).Name}");

builder.Services.RegisterCosmosContainer<VideoPlatformTemplate>(cosmosDataProvider.Database, "VideoPlatformTemplate", g => g.Id, _ => typeof(VideoPlatformTemplate).Name, keyOverride: $"cosmos_{typeof(VideoPlatformTemplate).Name}");
builder.Services.RegisterMongoDBRepository<VideoPlatformTemplate>(mongoDataProvider.Database, "VideoPlatformTemplate", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformTemplate).Name}");
builder.Services.RegisterDualWriteRepository<VideoPlatformTemplate>($"mongo_{typeof(VideoPlatformTemplate).Name}", $"cosmos_{typeof(VideoPlatformTemplate).Name}");

builder.Services.RegisterCosmosContainer<VideoPlatformPlayoutAsset>(cosmosDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, _ => typeof(VideoPlatformPlayoutAsset).Name, keyOverride: $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");
builder.Services.RegisterMongoDBRepository<VideoPlatformPlayoutAsset>(mongoDataProvider.Database, "VideoPlatformPlayoutAsset", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformPlayoutAsset).Name}");
builder.Services.RegisterDualWriteRepository<VideoPlatformPlayoutAsset>($"mongo_{typeof(VideoPlatformPlayoutAsset).Name}", $"cosmos_{typeof(VideoPlatformPlayoutAsset).Name}");

builder.Services.RegisterCosmosContainer<VideoPlatformSource>(cosmosDataProvider.Database, "VideoPlatformSource", g => g.Id, _ => typeof(VideoPlatformSource).Name, keyOverride: $"cosmos_{typeof(VideoPlatformSource).Name}");
builder.Services.RegisterMongoDBRepository<VideoPlatformSource>(mongoDataProvider.Database, "VideoPlatformSource", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformSource).Name}");
builder.Services.RegisterDualWriteRepository<VideoPlatformSource>($"mongo_{typeof(VideoPlatformSource).Name}", $"cosmos_{typeof(VideoPlatformSource).Name}");

builder.Services.RegisterCosmosContainer<EsniAudience>(cosmosDataProvider.Database, "EsniAudience", g => g.Id, _ => typeof(EsniAudience).Name, keyOverride: $"cosmos_{typeof(EsniAudience).Name}");
builder.Services.RegisterMongoDBRepository<EsniAudience>(mongoDataProvider.Database, "EsniAudience", g => g.Id, keyOverride:  $"mongo_{typeof(EsniAudience).Name}");
builder.Services.RegisterDualWriteRepository<EsniAudience>($"mongo_{typeof(EsniAudience).Name}", $"cosmos_{typeof(EsniAudience).Name}");

builder.Services.RegisterCosmosContainer<VideoPlatformBlackout>(cosmosDataProvider.Database, "VideoPlatformBlackout", g => g.Id, _ => typeof(VideoPlatformBlackout).Name, keyOverride: $"cosmos_{typeof(VideoPlatformBlackout).Name}");
builder.Services.RegisterMongoDBRepository<VideoPlatformBlackout>(mongoDataProvider.Database, "VideoPlatformBlackout", g => g.Id, keyOverride:  $"mongo_{typeof(VideoPlatformBlackout).Name}");
builder.Services.RegisterDualWriteRepository<VideoPlatformBlackout>($"mongo_{typeof(VideoPlatformBlackout).Name}", $"cosmos_{typeof(VideoPlatformBlackout).Name}");

builder.Services.RegisterCosmosContainer<GmsEvent>(cosmosDataProvider.Database, "GmsEvent", g => g.Id, _ => typeof(GmsEvent).Name, keyOverride: $"cosmos_{typeof(GmsEvent).Name}");
builder.Services.RegisterMongoDBRepository<GmsEvent>(mongoDataProvider.Database, "GmsEvent", g => g.Id, keyOverride:  $"mongo_{typeof(GmsEvent).Name}");
builder.Services.RegisterDualWriteRepository<GmsEvent>($"mongo_{typeof(GmsEvent).Name}", $"cosmos_{typeof(GmsEvent).Name}");

builder.Services.RegisterCosmosContainer<GmsTeamZips>(cosmosDataProvider.Database, "GmsTeamZips", g => g.Id, _ => typeof(GmsTeamZips).Name, keyOverride: $"cosmos_{typeof(GmsTeamZips).Name}");
builder.Services.RegisterMongoDBRepository<GmsTeamZips>(mongoDataProvider.Database, "GmsTeamZips", g => g.Id, keyOverride:  $"mongo_{typeof(GmsTeamZips).Name}");
builder.Services.RegisterDualWriteRepository<GmsTeamZips>($"mongo_{typeof(GmsTeamZips).Name}", $"cosmos_{typeof(GmsTeamZips).Name}");

using IHost host = builder.Build();

var mediator = host.Services.GetService<IMediator>() ?? throw new ArgumentNullException(nameof(IMediator));
var forcedWindowThresholdInMilliseconds = 45000;

var command = new EvaluateCommand
{
    Id = Guid.NewGuid().ToString(),
    CurrentEvaluation = DateTime.UtcNow,
    LastEvaluation = DateTime.UtcNow.AddMinutes(-1),
};

var diff = Math.Abs(command.LastEvaluation.Subtract(command.CurrentEvaluation).TotalMilliseconds);
if (diff < forcedWindowThresholdInMilliseconds)
{
    command.LastEvaluation = command.CurrentEvaluation + ScheduleEvaluationOffsets.EvaluationCorrection;
}

await mediator.Send(command);