namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application
{
    using System.Diagnostics.CodeAnalysis;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using NBA.NextGen.Shared.Application;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.VideoPlatform.Shared.Application.UseCases.HealthChecks.Commands;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Configuration;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Factories;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Mappers;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Services.Interfaces;
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.Strategies;

    [ExcludeFromCodeCoverage]
    public static class RegisterServices
    {
        public static void AddApplication(this IServiceCollection serviceCollection, [NotNull] IConfiguration configuration)
        {
            configuration.Required(nameof(configuration));
            serviceCollection.AddMediatRServices(typeof(RegisterServices).Assembly, typeof(HealthCheckCommand).Assembly);
            serviceCollection.AddAutoMapper(cfg => cfg.AddProfile(new StreamMarkersProfile()));
            serviceCollection.AddTransient<IStreamMarkerStrategy, GameStartStreamMarkerStrategy>();
            serviceCollection.AddTransient<IStreamMarkerStrategy, BroadcastStartStreamMarkerStrategy>();
            serviceCollection.AddTransient<IStreamMarkerStrategy, GameEndStreamMarkerStrategy>();
            serviceCollection.AddTransient<IStreamMarkerStrategy, PostGameStartStreamMarkerStrategy>();
            serviceCollection.AddTransient<IStreamMarkerStrategy, PostGameEndStreamMarkerStrategy>();
            serviceCollection.AddTransient<IStreamMarkerStrategyFactory, StreamMarkerStrategyFactory>();
            serviceCollection.Configure<StreamMarkerListenerOptions>(configuration.GetSection(nameof(StreamMarkerListenerOptions)));
        }
    }
}
