// "//-----------------------------------------------------------------------".
// <copyright file="UpdateListeningWindowCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.UpdateListeningWindow
{
    using System.Diagnostics.CodeAnalysis;
    using MediatR;

    /// <summary>
    /// Command to update the state of the listening window.
    /// </summary>
    [ExcludeFromCodeCoverage]
    public class UpdateListeningWindowCommand : IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the channel identifier.
        /// </summary>
        public string ChannelId { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the listening window is enabled or not.
        /// </summary>
        public bool Enable { get; set; }
    }
}
