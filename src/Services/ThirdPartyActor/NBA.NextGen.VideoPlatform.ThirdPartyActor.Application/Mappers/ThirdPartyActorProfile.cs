// "//-----------------------------------------------------------------------".
// <copyright file="ThirdPartyActorProfile.cs" company="PlaceholderCompany">
// Copyright (c) PlaceholderCompany. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.Mappers
{
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using AutoMapper;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.CreateOttEndpoint;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.DeleteOttEndpoint;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.GetOttEndpoint;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestAcknowldgement;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.SendOttEndpointStateChangeRequestCompleted;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Commands.UpdateOttEndpoint;
    using NBA.NextGen.VideoPlatform.ThirdPartyActor.Application.UseCases.Channels.Queries;

    /// <summary>
    /// The thirdParty profile.
    /// </summary>
    /// <seealso cref="Profile" />
    [ExcludeFromCodeCoverage]
    public class ThirdPartyActorProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ThirdPartyActorProfile"/> class.
        /// </summary>
        public ThirdPartyActorProfile()
        {
            this.CreateMap<InfrastructureStateChangeRequest, GetOrchestratorQuery>();

            this.CreateMap<InfrastructureStateChangeRequest<IList<OttEndpointCreationInfo>>,
                SendOttEndpointStateChangeRequestAcknowldgementCommand>();

            this.CreateMap<InfrastructureStateChangeRequest<OttEndpointCreationInfo>,
                    GetOttEndpointCommand>()
                .ForMember(x => x.PoolUuid, y => y.MapFrom(x => x.ActorSpecificDetail.Data.PoolUuid))
                .ForMember(x => x.Uuid, y => y.MapFrom(x => x.ActorSpecificDetail.Data.Uuid));

            this.CreateMap<InfrastructureStateChangeRequest<IList<OttEndpointCreationInfo>>,
                    CreateOttEndpointCommand>()
                .ForMember(x => x.OttEndpoints, y => y.MapFrom(x => x.ActorSpecificDetail.Data))
                .ForMember(x => x.EventId, y => y.MapFrom(x => x.ActorSpecificDetail.Data.FirstOrDefault().EventId));

            this.CreateMap<InfrastructureStateChangeRequest<OttEndpointCreationInfo>,
                    UpdateOttEndpointCommand>()
                .ForMember(x => x.PoolUuid, y => y.MapFrom(x => x.ActorSpecificDetail.Data.PoolUuid))
                .ForMember(x => x.Uuid, y => y.MapFrom(x => x.ActorSpecificDetail.Data.Uuid))
                .ForMember(x => x.CustomPath, y => y.MapFrom(x => x.ActorSpecificDetail.Data.CustomPath))
                .ForMember(x => x.Enabled, y => y.MapFrom(x => x.ActorSpecificDetail.Data.Enabled))
                .ForMember(x => x.StartTime, y => y.MapFrom(x => x.ActorSpecificDetail.Data.StartTime));

            this.CreateMap<InfrastructureStateChangeRequest<OttEndpointStateChangeInfo>,
                    DeleteOttEndpointCommand>()
                .ForMember(x => x.PoolUuid, y => y.MapFrom(x => x.ActorSpecificDetail.Data.PoolUuid))
                .ForMember(x => x.CustomPath, y => y.MapFrom(x => x.ActorSpecificDetail.Data.CustomPath))
                .ForMember(x => x.Uuid, y => y.MapFrom(x => x.ActorSpecificDetail.Data.EndpointUuid))
                .ForMember(x => x.ChannelId, y => y.MapFrom(x => x.ActorSpecificDetail.Data.ChannelId));

            this.CreateMap<SendOttEndpointStateChangeRequestAcknowldgementCommand, RequestAcknowledgementEvent>()
                .ForMember(x => x.AcknowledgedByActorId, y => y.MapFrom(x => ActorIds.ThirdPartyActor))
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(x => x.LongRunningOperationId));

            this.CreateMap<InfrastructureStateChangeRequest<OttEndpointCreationInfo>, SendOttEndpointStateChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.RequestIdAcknowledged, y => y.MapFrom(x => x.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, y => y.MapFrom(x => x.RequestorActorId))
                .ForMember(x => x.EventId, y => y.MapFrom(x => x.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(x => x.LongRunningOperationId))
                .ForMember(x => x.WorkflowId, y => y.MapFrom(x => x.WorkflowId));

            this.CreateMap<InfrastructureStateChangeRequest<OttEndpointStateChangeInfo>, SendOttEndpointStateChangeRequestAcknowldgementCommand>()
                .ForMember(x => x.RequestIdAcknowledged, y => y.MapFrom(x => x.RequestId))
                .ForMember(x => x.RequestorActorIdAcknowledged, y => y.MapFrom(x => x.RequestorActorId))
                .ForMember(x => x.EventId, y => y.MapFrom(x => x.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(x => x.LongRunningOperationId))
                .ForMember(x => x.WorkflowId, y => y.MapFrom(x => x.WorkflowId));

            this.CreateMap<InfrastructureStateChangeRequest<OttEndpointCreationInfo>, SendOttEndpointStateChangeRequestCompletedCommand>()
                .ForMember(x => x.EventId, y => y.MapFrom(x => x.ActorSpecificDetail.Data.EventId))
                .ForMember(x => x.LongRunningOperationId, y => y.MapFrom(x => x.LongRunningOperationId))
                .ForMember(x => x.State, y => y.MapFrom(x => x.DesiredState))
                .ForMember(x => x.WorkflowId, y => y.MapFrom(x => x.WorkflowId));

            this.CreateMap<SendOttEndpointStateChangeRequestCompletedCommand, InfrastructureStateChangedEvent>()
                .ForMember(x => x.ActorId, y => y.MapFrom(z => ActorIds.ThirdPartyActor))
                .ForMember(x => x.Data, opts =>
                {
                    opts.PreCondition(src => !string.IsNullOrEmpty(src.ErrorMessage));
                    opts.MapFrom(z => new Dictionary<string, object> { { "Error", z.ErrorMessage } });
                });
            
            this.CreateMap<SendOttEndpointStateChangeRequestCompletedCommand, InfrastructureStateChangedEventSQS>()
                .ForMember(x => x.ActorId, y => y.MapFrom(z => ActorIds.ThirdPartyActor))
                .ForMember(x => x.Data, opts =>
                {
                    opts.PreCondition(src => !string.IsNullOrEmpty(src.ErrorMessage));
                    opts.MapFrom(z => new Dictionary<string, object> { { "Error", z.ErrorMessage } });
                });
        }
    }
}
