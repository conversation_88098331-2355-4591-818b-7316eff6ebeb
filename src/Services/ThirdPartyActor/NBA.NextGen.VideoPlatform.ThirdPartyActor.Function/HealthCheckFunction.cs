namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Function;

using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using NBA.NextGen.Shared.Domain.Enums;


public class HealthCheckFunction
{
    [FunctionName(nameof(CheckStatus))]
    public IActionResult CheckStatus([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "liveness")] HttpRequest httpRequest) => new OkObjectResult(HealthStatus.Healthy);
}