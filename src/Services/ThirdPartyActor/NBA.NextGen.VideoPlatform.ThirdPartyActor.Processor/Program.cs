using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.ThirdPartyActor.Processor.Extensions;

var builder = WebApplication.CreateBuilder(args);

builder.Configuration
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", false, true)
    .AddJsonFile("local.settings.json", true, true)
    .AddParameterStore(builder.Configuration)
    .AddAmazonSecretsManager(builder.Configuration)
    .AddEnvironmentVariables();

builder.Services.AddApplicationInsightsTelemetryWorkerService(builder.Configuration);
builder.Services.AddApplicationDependencies(builder.Configuration);

var app = builder.Build();

var logger = app.Services.GetService<ILogger<Program>>();

logger!.LogInformation("Getting Started!");

app.Run();