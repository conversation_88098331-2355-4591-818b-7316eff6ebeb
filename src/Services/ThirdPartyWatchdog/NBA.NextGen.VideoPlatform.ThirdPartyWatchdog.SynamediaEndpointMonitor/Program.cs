using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using NBA.NextGen.VideoPlatform.Shared.Infrastructure;
using NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.Application.UseCases.Endpoints.Queries;
using NBA.NextGen.VideoPlatform.ThirdPartyWatchdog.SynamediaEndpointMonitor;

HostApplicationBuilder builder = Host.CreateApplicationBuilder(args);

builder.Configuration.SetBasePath(Directory.GetCurrentDirectory())
                    .AddJsonFile("appsettings.json", false, true)
                    .AddParameterStore(builder.Configuration)
                    .AddEnvironmentVariables();

builder.Services.AddApplicationDependencies(builder.Configuration);
builder.Services.AddSingleton<Microsoft.ApplicationInsights.TelemetryClient>();

using IHost host = builder.Build();

var mediator = host.Services.GetService<IMediator>();

if (mediator is not null)
{
    await mediator.Send(new CheckOttEndpointsQuery());
}