// "//-----------------------------------------------------------------------".
// <copyright file="UpdateOperationalStateFromEndScteMarkerCommand.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateOperationalStateFromEndScteMarker
{
    using MediatR;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Command to send production update requests.
    /// </summary>
    /// <seealso cref="IRequest{Unit}" />
    public class UpdateOperationalStateFromEndScteMarkerCommand : CorrelatedMessage, IRequest<Unit>
    {
        /// <summary>
        /// Gets or sets the Event Id.
        /// </summary>
        public string LiveEventId { get; set; }
    }
}
