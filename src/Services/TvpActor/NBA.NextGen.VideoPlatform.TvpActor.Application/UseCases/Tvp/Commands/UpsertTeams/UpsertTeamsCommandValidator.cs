// "//-----------------------------------------------------------------------".
// <copyright file="UpsertTeamsCommandValidator.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertTeams
{
    using FluentValidation;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddTeams;

    /// <summary>
    /// <see cref="UpsertTeamsCommandValidator"/>.
    /// </summary>
    public class UpsertTeamsCommandValidator : AbstractValidator<UpsertTeamsCommand>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertTeamsCommandValidator"/> class.
        /// </summary>
        public UpsertTeamsCommandValidator()
        {
            this.ValidateInput();
        }

        /// <summary>
        /// Validates the input.
        /// </summary>
        public void ValidateInput()
        {
            this.RuleFor(x => x.TvpEventTeamCreationInfos).NotNull().WithMessage($"{nameof(UpsertTeamsCommand.TvpEventTeamCreationInfos)} cannot be null");
            this.RuleForEach(x => x.TvpEventTeamCreationInfos).ChildRules(tvpEventTeamCreationInfo =>
            {
                tvpEventTeamCreationInfo.RuleFor(x => x.ExternalId).NotEmpty().WithMessage($"{nameof(TvpEventTeamCreationInfo.ExternalId)} cannot be empty");
                tvpEventTeamCreationInfo.RuleFor(x => x.Name).NotEmpty().WithMessage($"{nameof(TvpEventTeamCreationInfo.Name)} cannot be empty");
            });
        }
    }
}
