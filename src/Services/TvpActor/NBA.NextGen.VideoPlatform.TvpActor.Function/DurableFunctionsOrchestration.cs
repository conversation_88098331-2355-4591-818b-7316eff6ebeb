// "//-----------------------------------------------------------------------".
// <copyright file="DurableFunctionsOrchestration.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Function
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics.CodeAnalysis;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.VideoPlatformDurableFunctions;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Configuration;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddEntitlementsToProduction;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddTeams;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.DeleteEvents;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.DeleteEventSchedules;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.RemoveProductionFromSubscriptions;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.RemoveProductionPackage;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateAllProductionsContentState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateAllProductionsState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateContentState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEndTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualEndTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualStartTime;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatus;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventStatusOnly;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertLocation;
    using NBA.NextGen.VideoPlatform.TvpActor.Domain.Common;
    using Newtonsoft.Json;

    /// <summary>
    /// Durable function starter.
    /// </summary>
    public class DurableFunctionsOrchestration : VideoPlatformOrchestrationBase
    {
        /// <summary>
        /// The log information template message.
        /// </summary>
        private const string LogInformationInitiatedInstanceMessage = "Initiated instance {InstanceId}, at {date} is replaying? {isReplaying}";

        /// <summary>
        /// The log information activity function message template.
        /// </summary>
        private const string LogInformationActivityFunctionMessage = "{activity} Function requested for LongRunningOperationId: {opId}";

        /// <summary>
        /// The log information activity function executed message template.
        /// </summary>
        private const string LogInformationActivityFunctionExecutedMessage = "{activity} Function executed";

        /// <summary>
        /// The log information activity function executed message template.
        /// </summary>
        private const string LogInformationActivityFunctionExecutedOnOrchestrationNameMessage = "{activity} Function executed for {marker}";

        /// <summary>
        /// The log information activity function requested message template.
        /// </summary>
        private const string LogInformationActivityFunctionRequestedMessage = "{activity} Function requested";

        /// <summary>
        /// The log information orcestration function message template.
        /// </summary>
        private const string LogInformationOrchestrationNameMessage = "Triggered {orchestrationName} orchestration for tvp actor {externalSystemInfrastructureId} and context {context}";

        /// <summary>
        /// The log information failed function name message.
        /// </summary>
        private const string LogInformationFailedFunctionNameMessage = "{functionName} failed with the exception of type: {type} with message: {message}";

        /// <summary>
        /// The log information activity function for long running operation identifier message.
        /// </summary>
        private const string LogInformationActivityFunctionForLongRunningOperationIdMessage = "{activity} Function executed for LongRunningOperationId: {opId}";

        /// <summary>
        /// The mediator.
        /// </summary>
        private readonly IMediator mediator;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The date time service.
        /// </summary>
        private readonly IDateTime dateTimeService;

        /// <summary>
        /// The TVP Actor options.
        /// </summary>
        private readonly TvpActorOptions tvpActorOptions;

        /// <summary>
        /// The logger.
        /// </summary>
        private ILogger logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="DurableFunctionsOrchestration"/> class.
        /// </summary>
        /// <param name="mediator">The mediator.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="factory">The factory.</param>
        /// <param name="dateTime">The date time service.</param>
        /// <param name="tvpActorOptions">The TVP Actor options.</param>
        public DurableFunctionsOrchestration(
            IMediator mediator,
            ILogger<DurableFunctionsOrchestration> logger,
            IMapper mapper,
            IVideoPlatformCorrelationProviderFactory factory,
            IDateTime dateTime,
            [NotNull] IOptionsMonitor<TvpActorOptions> tvpActorOptions)
            : base(mapper, logger, factory)
        {
            tvpActorOptions.Required(nameof(tvpActorOptions));
            this.mediator = mediator;
            this.logger = logger;
            this.mapper = mapper;
            this.dateTimeService = dateTime;
            this.tvpActorOptions = tvpActorOptions.CurrentValue;
        }

        /// <summary>
        /// Set the status TVP final.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.UpdateTeamRsnZip)]
        public async Task UpdateTeamRsnZipAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.UpdateTeamRsnZip)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<GmsTeamZips>>();

            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.UpdateTeamRsnZip,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var updateGeoAreaCommand = this.mapper.Map<UpdateGeoAreaCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateGeoAreaZips, context, updateGeoAreaCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException e)
            {
                this.logger.LogError($"Error updating GeoAreas {e}");
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Runs the asynchronous.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.SetupTVPWorkflow)]
        public async Task SetupAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.SetupTVPWorkflow)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpEventCreationInfo>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.SetupTVPWorkflow,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var upsertLocationCommand = this.mapper.Map<UpsertLocationCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpsertLocation, context, upsertLocationCommand).ConfigureAwait(true);

                var syncTeamsCommand = this.mapper.Map<UpsertTeamsCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpsertTeams, context, syncTeamsCommand).ConfigureAwait(true);

                var syncCommand = this.mapper.Map<AddEntitlementsToProductionCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.AssociateEntitlementWithSubscription, context, syncCommand).ConfigureAwait(true);

                var updateStateCommand = this.mapper.Map<UpdateAllProductionsStateCommand>(request);
                updateStateCommand.State = TvpProductionStatus.Init;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateAllProductionsStatus, context, updateStateCommand).ConfigureAwait(true);

                var syncStatusCommand = this.mapper.Map<UpdateEventStatusCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateEventStatus, context, syncStatusCommand).ConfigureAwait(true);

                var updateAllProductionsContentState = this.mapper.Map<UpdateAllProductionsContentStateCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateAllContentState, context, updateAllProductionsContentState).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.SetupTVPWorkflow,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Set the status TVP started.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.StartTVPWorkflow)]
        public async Task StartAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.StartTVPWorkflow)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpUpdateEventStatus>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.StartTVPWorkflow,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var syncCommand = this.mapper.Map<UpdateEventStatusCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateEventStatus, context, syncCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.StartTVPWorkflow,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Set the status TVP final.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.EndTVPWorkflow)]
        public async Task EndAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.EndTVPWorkflow)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpUpdateEventStatus>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.EndTVPWorkflow,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var syncCommand = this.mapper.Map<UpdateEventStatusCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateEventStatus, context, syncCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.EndTVPWorkflow,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// CleanupAsync.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.CleanupTVPWorkflow)]
        public async Task CleanupAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.CleanupTVPWorkflow)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(LogInformationInitiatedInstanceMessage, context.InstanceId, context.CurrentUtcDateTime, context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpEventCleanupInfo>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.CleanupTVPWorkflow,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var syncCommand = this.mapper.Map<RemoveEntitlementsFromProductionCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.RemoveEntitlementWithSubscription, context, syncCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.CleanupTVPWorkflow,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// CleanupAsync.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.DeleteTVPWorkflow)]
        public async Task DeleteItemAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.DeleteTVPWorkflow)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpEventDeleteInfo>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.DeleteTVPWorkflow,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var removeEntitlementsFromProductionCommand = this.mapper.Map<RemoveEntitlementsFromProductionCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.RemoveEntitlementWithSubscription, context, removeEntitlementsFromProductionCommand).ConfigureAwait(true);

                var deleteEventSchedulesCommand = this.mapper.Map<DeleteEventSchedulesCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.DeleteEventSchedules, context, deleteEventSchedulesCommand).ConfigureAwait(true);

                var deleteEventsCommand = this.mapper.Map<DeleteEventCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.DeleteEvent, context, deleteEventsCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.DeleteTVPWorkflow,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Updates a production status.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.UpdateProductionStatusOrchestration)]
        public async Task UpdateProductionStatusOrchestrationAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.UpdateProductionStatusOrchestration)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProductionUpdateStatusInfo>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                "Triggered {orchestrationName} orchestration for tvp actor with context {context}",
                OrchestrationNames.UpdateProductionStatusOrchestration,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                var updateProductionStateCommand = this.mapper.Map<UpdateProductionStateCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionStatus, context, updateProductionStateCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.EndTVPWorkflow,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Updates the status of multiple productions.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.UpdateMultipleProductionsStatusOrchestration)]
        public async Task UpdateMultipleProductionsStatusOrchestrationAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.UpdateMultipleProductionsStatusOrchestration)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<IList<TvpProductionUpdateStatusInfo>>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.UpdateMultipleProductionsStatusOrchestration,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                foreach (var tvpProductionUpdateStatusInfo in request.ActorSpecificDetail.Data)
                {
                    var updateProductionStateCommand = this.mapper.Map<UpdateProductionStateCommand>(tvpProductionUpdateStatusInfo);
                    updateProductionStateCommand.LongRunningOperationId = request.LongRunningOperationId;
                    updateProductionStateCommand.CorrelationId = request.CorrelationId;
                    _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionStatus, context, updateProductionStateCommand).ConfigureAwait(true);
                }
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.EndTVPWorkflow,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Durable Orchestration to process SCTE35 game end marker.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.ProcessGameEndMarker)]
        public async Task ProcessGameEndMarkerAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.ProcessGameEndMarker)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProcessGameEndMarkerModel>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.ProcessGameEndMarker,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                // If it's primary feed, update actual end time and end time
                if (request.ActorSpecificDetail.Data.IsPrimaryFeed)
                {
                    // Update actual end time to AcquisitionTime, from SCTE35 marker
                    var updateEventActualEndTimeCommand = this.mapper.Map<UpdateEventActualEndTimeCommand>(request);
                    _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateEventActualEndTimeActivity, context, updateEventActualEndTimeCommand).ConfigureAwait(true);

                    // Update end time to now plus ProductionLaggedTime, configurable in tvpActorOptions
                    var updateEventEndTimeCommand = this.mapper.Map<UpdateEventEndTimeCommand>(request);
                    updateEventEndTimeCommand.EndTime = this.dateTimeService.Now.AddMinutes(this.tvpActorOptions.ProductionLaggedTime);
                    _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateEventEndTimeActivity, context, updateEventEndTimeCommand).ConfigureAwait(true);
                }

                // Update operational state of the production where the marker was received
                var updateProductionStateCommand = this.mapper.Map<UpdateProductionStateCommand>(request);
                updateProductionStateCommand.LongRunningOperationId = request.LongRunningOperationId;
                updateProductionStateCommand.CorrelationId = request.CorrelationId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionStatus, context, updateProductionStateCommand).ConfigureAwait(true);

                // If it's primary feed, then set event status to final
                if (request.ActorSpecificDetail.Data.IsPrimaryFeed)
                {
                    // Waiting some minutes before continue processing, configurable in tvpActorOptions
                    var deadline = context.CurrentUtcDateTime.Add(TimeSpan.FromMinutes(this.tvpActorOptions.TimeBetweenCommandsForGameEndMarkerInMinutes));
                    await context.CreateTimer(deadline, CancellationToken.None).ConfigureAwait(true);

                    // Update event status to final
                    var updateEventStatusOnlyCommand = this.mapper.Map<UpdateEventStatusOnlyCommand>(request);
                    _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateEventStatusOnlyActivity, context, updateEventStatusOnlyCommand).ConfigureAwait(true);
                }
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.ProcessGameEndMarker,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Durable Orchestration to process SCTE35 post game start marker.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.ProcessPostGameStartMarker)]
        public async Task ProcessPostGameStartMarkerAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.ProcessPostGameStartMarker)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(LogInformationInitiatedInstanceMessage, context.InstanceId, context.CurrentUtcDateTime, context.IsReplaying);

            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProcessPostGameStartMarkerModel>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(LogInformationOrchestrationNameMessage, OrchestrationNames.ProcessPostGameStartMarker, request.ExternalSystemInfrastructureId, JsonConvert.SerializeObject(request));

            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                // Update operational state of the production where the marker was received
                var updateProductionContentState = this.mapper.Map<UpdateContentStateCommand>(request);
                updateProductionContentState.CorrelationId = request.CorrelationId;
                updateProductionContentState.LongRunningOperationId = request.LongRunningOperationId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionContentState, context, updateProductionContentState).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(LogInformationFailedFunctionNameMessage, OrchestrationNames.ProcessPostGameStartMarker, ex.InnerException?.GetType()?.Name ?? ex.GetType()?.Name, ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Durable Orchestration to process SCTE35 post game end marker.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.ProcessPostGameEndMarker)]
        public async Task ProcessPostGameEndMarkerAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.ProcessPostGameEndMarker)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(LogInformationInitiatedInstanceMessage, context.InstanceId, context.CurrentUtcDateTime, context.IsReplaying);

            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProcessPostGameEndMarkerModel>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(LogInformationOrchestrationNameMessage, OrchestrationNames.ProcessPostGameEndMarker, request.ExternalSystemInfrastructureId, JsonConvert.SerializeObject(request));

            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var ackCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, ackCommand).ConfigureAwait(true);

                // Update operational state of the production where the marker was received
                var updateProductionStateCommand = this.mapper.Map<UpdateProductionStateCommand>(request);
                updateProductionStateCommand.CorrelationId = request.CorrelationId;
                updateProductionStateCommand.LongRunningOperationId = request.LongRunningOperationId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionStatus, context, updateProductionStateCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(LogInformationFailedFunctionNameMessage, OrchestrationNames.ProcessPostGameEndMarker, ex.InnerException?.GetType()?.Name ?? ex.GetType()?.Name, ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Durable Orchestration to process SCTE35 game start marker.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.ProcessGameStartMarker)]
        public async Task ProcessProgramGameStartMarkerAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.ProcessGameStartMarker)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProcessGameStartMarkerModel>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.ProcessGameStartMarker,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var acknowldgementCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, acknowldgementCommand).ConfigureAwait(true);

                var updateProductionStateCommand = this.mapper.Map<UpdateProductionStateCommand>(request);
                updateProductionStateCommand.LongRunningOperationId = request.LongRunningOperationId;
                updateProductionStateCommand.CorrelationId = request.CorrelationId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionStatus, context, updateProductionStateCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.ProcessGameStartMarker,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Durable Orchestration to process SCTE35 broadcast start marker.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.ProcessBroadcastStartMarker)]
        public async Task ProcessBroadcastStartMarkerAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.ProcessBroadcastStartMarker)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProcessBroadcastStartMarkerModel>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.ProcessBroadcastStartMarker,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var acknowldgementCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, acknowldgementCommand).ConfigureAwait(true);

                foreach (var tvpProcessBroadcastStartMarkerDetail in request.ActorSpecificDetail.Data.Details)
                {
                    if (tvpProcessBroadcastStartMarkerDetail.IsPrimaryFeed)
                    {
                        var updateEventActualStartTimeCommand = this.mapper.Map<UpdateEventActualStartTimeCommand>(request);
                        _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateEventActualStartTimeActivity, context, updateEventActualStartTimeCommand).ConfigureAwait(true);
                    }

                    var updateProductionStateCommand = this.mapper.Map<UpdateProductionStateCommand>(tvpProcessBroadcastStartMarkerDetail);
                    updateProductionStateCommand.EventId = request.ActorSpecificDetail.Data.LiveEventId;
                    updateProductionStateCommand.LongRunningOperationId = request.LongRunningOperationId;
                    _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionStatus, context, updateProductionStateCommand).ConfigureAwait(true);

                    this.logger.LogInformation(
                        LogInformationActivityFunctionExecutedOnOrchestrationNameMessage,
                        ActivityFunctionNames.UpdateProductionStatus,
                        OrchestrationNames.ProcessBroadcastStartMarker);

                    var updateProductionContentState = this.mapper.Map<UpdateContentStateCommand>(tvpProcessBroadcastStartMarkerDetail);
                    updateProductionContentState.CorrelationId = request.CorrelationId;
                    updateProductionContentState.LongRunningOperationId = request.LongRunningOperationId;
                    _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.UpdateProductionContentState, context, updateProductionContentState).ConfigureAwait(true);

                    this.logger.LogInformation(
                        LogInformationActivityFunctionExecutedOnOrchestrationNameMessage,
                        ActivityFunctionNames.UpdateProductionContentState,
                        OrchestrationNames.ProcessBroadcastStartMarker);
                }
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.ProcessBroadcastStartMarker,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Durable Orchestration to remove a production from a package.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.RemoveProductionFromPackage)]
        public async Task RemoveProductionFromPackageAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.RemoveProductionFromPackage)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProductionPackage>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.RemoveProductionFromPackage,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var acknowldgementCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, acknowldgementCommand).ConfigureAwait(true);

                var removeProductionPackageCommand = this.mapper.Map<RemoveProductionPackageCommand>(request);
                removeProductionPackageCommand.CorrelationId = request.CorrelationId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.RemoveProductionPackage, context, removeProductionPackageCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.RemoveProductionFromPackage,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Durable Orchestration to remove a production from a package.
        /// </summary>
        /// <param name="context">The durable client.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        [FunctionName(OrchestrationNames.RemoveSingleProductionFromPackage)]
        public async Task RemoveSingleProductionFromPackageAsync(
            [OrchestrationTrigger(Orchestration = OrchestrationNames.RemoveSingleProductionFromPackage)][NotNull] IDurableOrchestrationContext context)
        {
            context.Required(nameof(context));
            this.logger = context.CreateReplaySafeLogger(this.logger);
            this.logger.LogInformation(
                LogInformationInitiatedInstanceMessage,
                context.InstanceId,
                context.CurrentUtcDateTime,
                context.IsReplaying);
            var request = context.GetInput<InfrastructureStateChangeRequest<TvpProductionPackage>>();
            var correlatedMessage = request as CorrelatedMessage;
            this.SetCorrelationMessage(correlatedMessage);
            this.logger.LogInformation(
                LogInformationOrchestrationNameMessage,
                OrchestrationNames.RemoveSingleProductionFromPackage,
                request.ExternalSystemInfrastructureId,
                JsonConvert.SerializeObject(request));
            var changeAppliedNotificationCommand = this.mapper.Map<SendChangeRequestCompletedCommand>(request);

            try
            {
                var acknowldgementCommand = this.mapper.Map<SendChangeRequestAcknowldgementCommand>(request);
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendStateChangeAcknowledgementEvent, context, acknowldgementCommand).ConfigureAwait(true);

                var removeProductionFromSubscriptionsCommand = this.mapper.Map<RemoveProductionFromSubscriptionsCommand>(request);
                removeProductionFromSubscriptionsCommand.CorrelationId = request.CorrelationId;
                _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.RemoveSingleProductionPackage, context, removeProductionFromSubscriptionsCommand).ConfigureAwait(true);
            }
            catch (FunctionFailedException ex)
            {
                this.logger.LogError(
                    LogInformationFailedFunctionNameMessage,
                    OrchestrationNames.RemoveSingleProductionFromPackage,
                    ex.InnerException.GetType().Name,
                    ex.InnerException.Message);
                changeAppliedNotificationCommand.ErrorMessage = ex.Message;
                changeAppliedNotificationCommand.State = InfrastructureState.Failed;
            }

            _ = await this.CallVideoPlatformActivityAsync(ActivityFunctionNames.SendChangeStateAppliedEvent, context, changeAppliedNotificationCommand).ConfigureAwait(true);
        }

        /// <summary>
        /// Activity function used to apply channel state change.
        /// </summary>
        /// <param name="request">request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.SendStateChangeAcknowledgementEvent)]
        public async Task SendStateChangeAcknowledgementEventAsync([ActivityTrigger][NotNull] SendChangeRequestAcknowldgementCommand request)
        {
            request.Required(nameof(request));

            // stop using interpolation
            this.logger.LogInformation(
                LogInformationActivityFunctionRequestedMessage,
                ActivityFunctionNames.SendStateChangeAcknowledgementEvent);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionExecutedMessage,
                ActivityFunctionNames.SendStateChangeAcknowledgementEvent);
        }

        /// <summary>
        /// Create Game Subscription Package Event.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.AssociateEntitlementWithSubscription)]
        public async Task AssociateEntitlementWithSubscriptionAsync([ActivityTrigger][NotNull] AddEntitlementsToProductionCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(
                LogInformationActivityFunctionMessage,
                ActivityFunctionNames.AssociateEntitlementWithSubscription,
                request.LongRunningOperationId);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionForLongRunningOperationIdMessage,
                ActivityFunctionNames.AssociateEntitlementWithSubscription,
                request.LongRunningOperationId);
        }

        /// <summary>
        /// Removes the entitlement with subscription asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.RemoveEntitlementWithSubscription)]
        public async Task RemoveEntitlementWithSubscriptionAsync([ActivityTrigger][NotNull] RemoveEntitlementsFromProductionCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(
                LogInformationActivityFunctionMessage,
                ActivityFunctionNames.RemoveEntitlementWithSubscription,
                request.LongRunningOperationId);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionForLongRunningOperationIdMessage,
                ActivityFunctionNames.RemoveEntitlementWithSubscription,
                request.LongRunningOperationId);
        }

        /// <summary>
        /// Removes the entitlement with subscription asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateEventStatus)]
        public async Task UpdateEventStatusAsync([ActivityTrigger][NotNull] UpdateEventStatusCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(
                LogInformationActivityFunctionMessage,
                ActivityFunctionNames.UpdateEventStatus,
                request.LongRunningOperationId);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionForLongRunningOperationIdMessage,
                ActivityFunctionNames.UpdateEventStatus,
                request.LongRunningOperationId);
        }

        /// <summary>
        /// Updates the state of productions asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateAllProductionsStatus)]
        public async Task UpdateAllProductionStatusAsync([ActivityTrigger][NotNull] UpdateAllProductionsStateCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(
                LogInformationActivityFunctionMessage,
                ActivityFunctionNames.UpdateAllProductionsStatus,
                request.LongRunningOperationId);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionForLongRunningOperationIdMessage,
                ActivityFunctionNames.UpdateAllProductionsStatus,
                request.LongRunningOperationId);
        }

        /// <summary>
        /// Updates the content state of productions asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateAllContentState)]
        public async Task UpdateAllProductionContentStateAsync([ActivityTrigger][NotNull] UpdateAllProductionsContentStateCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(
                LogInformationActivityFunctionMessage,
                ActivityFunctionNames.UpdateAllContentState,
                request.LongRunningOperationId);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionForLongRunningOperationIdMessage,
                ActivityFunctionNames.UpdateAllContentState,
                request.LongRunningOperationId);
        }

        /// <summary>
        /// Updates the state of productions asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateProductionStatus)]
        public async Task UpdateProductionStatusAsync([ActivityTrigger][NotNull] UpdateProductionStateCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(
                LogInformationActivityFunctionMessage,
                ActivityFunctionNames.UpdateProductionStatus,
                request.LongRunningOperationId);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionForLongRunningOperationIdMessage,
                ActivityFunctionNames.UpdateProductionStatus,
                request.LongRunningOperationId);
        }

        /// <summary>
        /// Updates the content state of a production asynchronously.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateProductionContentState)]
        public async Task UpdateProductionContentStateAsync([ActivityTrigger][NotNull] UpdateContentStateCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionMessage, ActivityFunctionNames.UpdateProductionContentState, request.LongRunningOperationId);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionForLongRunningOperationIdMessage, ActivityFunctionNames.UpdateProductionContentState, request.LongRunningOperationId);
        }

        /// <summary>
        /// Sends the request completion function asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.SendChangeStateAppliedEvent)]
        public async Task SendRequestCompletionFunctionAsync([ActivityTrigger][NotNull] SendChangeRequestCompletedCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(
                LogInformationActivityFunctionRequestedMessage,
                ActivityFunctionNames.SendChangeStateAppliedEvent);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(
                LogInformationActivityFunctionExecutedMessage,
                ActivityFunctionNames.SendChangeStateAppliedEvent);
        }

        /// <summary>
        /// Upserts the location asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpsertLocation)]
        public async Task UpsertLocationAsync([ActivityTrigger][NotNull] UpsertLocationCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.UpsertLocation);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.UpsertLocation);
        }

        /// <summary>
        /// Upserts the teams asynchronous.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpsertTeams)]
        public async Task UpsertTeamsAsync([ActivityTrigger][NotNull] UpsertTeamsCommand request)
        {
            request.Required(nameof(request));

            // stop using interpolation
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.UpsertTeams);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.UpsertTeams);
        }

        /// <summary>
        /// Deletes the event schedules asynchronously.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.DeleteEventSchedules)]
        public async Task DeleteEventSchedulesAsync([ActivityTrigger][NotNull] DeleteEventSchedulesCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.DeleteEventSchedules);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.DeleteEventSchedules);
        }

        /// <summary>
        /// Deletes the events asynchronously.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.DeleteEvent)]
        public async Task DeleteEventsAsync([ActivityTrigger][NotNull] DeleteEventCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.DeleteEvent);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.DeleteEvent);
        }

        /// <summary>
        /// Activity to update the TVP Event End time.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateEventEndTimeActivity)]
        public async Task UpdateEventEndTimeActivityAsync([ActivityTrigger][NotNull] UpdateEventEndTimeCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.UpdateEventEndTimeActivity);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.UpdateEventEndTimeActivity);
        }

        /// <summary>
        /// Activity to update the TVP Event Actual Start Time.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateEventActualStartTimeActivity)]
        public async Task UpdateEventActualStartTimeActivityAsync([ActivityTrigger][NotNull] UpdateEventActualStartTimeCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.UpdateEventActualStartTimeActivity);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.UpdateEventActualStartTimeActivity);
        }

        /// <summary>
        /// Activity to update the TVP Event Actual Time.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateEventActualEndTimeActivity)]
        public async Task UpdateEventActualEndTimeActivityAsync([ActivityTrigger][NotNull] UpdateEventActualEndTimeCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.UpdateEventActualEndTimeActivity);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.UpdateEventActualEndTimeActivity);
        }

        /// <summary>
        /// Activity to update the TVP Event status only.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateEventStatusOnlyActivity)]
        public async Task UpdateEventStatusOnlyActivityAsync([ActivityTrigger][NotNull] UpdateEventStatusOnlyCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.UpdateEventStatusOnlyActivity);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.UpdateEventStatusOnlyActivity);
        }

        /// <summary>
        /// Activity to remove a production from a package.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.RemoveProductionPackage)]
        public async Task RemoveProductionPackageAsync([ActivityTrigger][NotNull] RemoveProductionPackageCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.RemoveProductionPackage);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.RemoveProductionPackage);
        }

        /// <summary>
        /// Activity to remove a production from a package.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.RemoveSingleProductionPackage)]
        public async Task RemoveSingleProductionPackageAsync([ActivityTrigger][NotNull] RemoveProductionFromSubscriptionsCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.RemoveSingleProductionPackage);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.RemoveSingleProductionPackage);
        }

        /// <summary>
        /// Update GeoArea team Zips.
        /// </summary>
        /// <param name="request">The request.</param>
        /// <returns>Task.</returns>
        [FunctionName(ActivityFunctionNames.UpdateGeoAreaZips)]
        public async Task UpdateGeoAreaTeamZipsAsync([ActivityTrigger][NotNull] UpdateGeoAreaCommand request)
        {
            request.Required(nameof(request));
            this.logger.LogInformation(LogInformationActivityFunctionRequestedMessage, ActivityFunctionNames.UpdateGeoAreaZips);

            await this.mediator.Send(request).ConfigureAwait(false);

            this.logger.LogInformation(LogInformationActivityFunctionExecutedMessage, ActivityFunctionNames.UpdateGeoAreaZips);
        }
    }
}