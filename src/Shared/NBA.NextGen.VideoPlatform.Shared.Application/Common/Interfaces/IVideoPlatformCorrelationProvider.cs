// "//-----------------------------------------------------------------------".
// <copyright file="IVideoPlatformCorrelationProvider.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces
{
    /// <summary>
    /// IVideoPlatformCorrelationProvider.
    /// </summary>
    public interface IVideoPlatformCorrelationProvider
    {
        /// <summary>
        /// Gets the Correlation Id.
        /// </summary>
        public string CorrelationId { get; }

        /// <summary>
        /// Initialize.
        /// </summary>
        /// <param name="correlationId">correlation id.</param>
        public void Initialize(string correlationId);

        /// <summary>
        /// The Has Correlation.
        /// </summary>
        /// <returns>bool.</returns>
        public bool HasCorrelation();
    }
}
