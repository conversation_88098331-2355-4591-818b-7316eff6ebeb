// "//-----------------------------------------------------------------------".
// <copyright file="NextGenExtensions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Gms.Extensions
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Runtime.CompilerServices;
    using System.Runtime.Serialization.Formatters.Binary;
    using System.Text;
    using System.Xml;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Prisma.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Options;
    using NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Constants;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformBlackouts.Model;
    using NBA.NextGen.VideoPlatform.Shared.Domain.VideoPlatformSchedules.Entities;
    using Newtonsoft.Json;

    #pragma warning disable CA1502

    /// <summary>
    /// The next gen extension methods.
    /// </summary>
#pragma warning disable CA1506
    public static class NextGenExtensions
#pragma warning restore CA1506
    {
        /// <summary>
        /// Tournament Season Info Max Length.
        /// </summary>
        private const int TournamentSeasonInfoMaxLength = 2;

        /// <summary>
        /// The PostGameExperience const.
        /// </summary>
        private const string PostGameExperience = "postgame";

        /// <summary>
        /// The manifest_url.
        /// </summary>
        private const string ManifestUrl = "index.m3u8";

        /// <summary>
        /// The "NSS-Associated-Experiences".
        /// </summary>
        private const string NSSAssociatedExperiences = "NSS-Associated-Experiences";

        /// <summary>
        /// The Local Blackout duration. ISO 8610 format.
        /// </summary>
        private const string LocalBlackoutDuration = "PT75H";

        /// <summary>
        /// The Regional Blackout duration. ISO 8610 format.
        /// </summary>
        private const string RegionalBlackoutDuration = "PT24H";

        /// <summary>
        /// The Global Blackout duration. ISO 8610 format.
        /// </summary>
        private const string GlobalBlackoutDuration = "PT3H";

        /// <summary>
        /// Gets the next gen ott scheduling information.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <param name="customWorkflowOffsetOptions"><see cref="CustomWorkflowOffsetOptions"/>.</param>
        /// <param name="esniResourcesCreationOptions"><see cref="EsniResourcesCreationOptions"/>.</param>
        /// <param name="aquilaChannelCreationOptions"><see cref="AquilaChannelCreationOptions"/>.</param>
        /// <param name="thirdPartyEndpointCreationOptions"><see cref="ThirdPartyEndpointCreationOptions"/>.</param>
        /// <returns>The scheduling information.</returns>
        public static IEnumerable<IntentMetaData> GetIntentMetaDatas(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] CustomWorkflowOffsetOptions customWorkflowOffsetOptions,
            EsniResourcesCreationOptions esniResourcesCreationOptions,
            [NotNull] AquilaChannelCreationOptions aquilaChannelCreationOptions,
            [NotNull] ThirdPartyEndpointCreationOptions thirdPartyEndpointCreationOptions)
        {
            if (!gmsEntity.IsRelevantForOrchestrationPlatform)
            {
                return Enumerable.Empty<IntentMetaData>();
            }

            var intentMetaDatas = new List<IntentMetaData>();
            var nssMedia = gmsEntity.Media.Where(m => m.IsActiveNssMediaWithActiveSchedules);

            foreach (var media in nssMedia)
            {
                var preferredSchedule = GetPreferredSchedule(media);
                var channelId = gmsEntity.GetChannelId(media);
                var playoutId = nssMedia.First(x => GetPreferredSchedule(x).Operations.Encoder == preferredSchedule.Operations.Encoder).Id == media.Id ?
                    channelId : null;

                var isThirdParty = preferredSchedule.GetValueFromScheduleOrMediaKeyValuePairs(thirdPartyEndpointCreationOptions?.IsNSSThirdPartyKey, media); // null quortex dr radio

                var intentMetadata = new IntentMetaData
                {
                    GameStartTimeUtc = gmsEntity.DateTime.Value.UtcDateTime,
                    ScheduleId = preferredSchedule.Id.ToString(CultureInfo.InvariantCulture),
                    EncoderId = preferredSchedule.Operations.Encoder,
                    ChannelId = channelId,
                    PlayoutId = playoutId,
                    GmsMediaId = media.Id,
                    CustomOffsets = GetCustomOffsets(preferredSchedule, media, customWorkflowOffsetOptions),
                    HasPreGameExperience = preferredSchedule.HasPreGameExperience(media, esniResourcesCreationOptions),
                    HasPostGameExperience = preferredSchedule.HasPostGameExperience(media, esniResourcesCreationOptions),
                    IsThirdPartyProduction = isThirdParty,
                    ThirdPartyUrl = GetQuortexCustomPath(media.ThirdPartyStreamUrls?.FirstOrDefault()?.Url),
                    ThirdPartyPoolUuid = GetPoolIdForThirdPartyMedias(isThirdParty, thirdPartyEndpointCreationOptions),
                    LiveProductionServicesUrls = media.LiveProductionServices,
                };

                if (intentMetadata.CustomOffsets.TryGetValue(NbaWorkflowIds.EventInfrastructureStart, out var infraStartOffset))
                {
                    intentMetadata.CustomOffsets.Add(NbaWorkflowIds.EventInfrastructureSetup, infraStartOffset - TimeSpan.FromMinutes(WorkflowOperationsConstants.MinutesBetweenInfraSetupAndStart));
                }

                intentMetaDatas.Add(intentMetadata);
            }

            return intentMetaDatas;
        }

        /// <summary>
        /// Gets the Live Production Services.
        /// </summary>
        /// <param name="gmsEntity">The gmsEntity.</param>
        /// <param name="mediaId">The MediaId.</param>
        /// <returns>The LiveProductionServices.</returns>
        public static ICollection<LiveProductionServices> GetLiveProductionServicesDetails(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] string mediaId)
        {
            return gmsEntity.Media.FirstOrDefault(x => CompareMediaId(mediaId, x.Id))?.LiveProductionServices;
        }

        /// <summary>
        /// Gets the information of Quortex Endpoints for a given GmsGame.
        /// </summary>
        /// <param name="gmsEntity">The GMS Game.</param>
        /// <param name="thirdPartyEndpointCreationOptions">The ThirdParty endpoint creation options.</param>
        /// <param name="medias"><see cref="MediaInfo"/>s.</param>
        /// <returns>The information to create the channels for this GmsGame.</returns>
        public static IEnumerable<OttEndpointCreationInfo> GetQuortexEndpointsCreationInfo(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] ThirdPartyEndpointCreationOptions thirdPartyEndpointCreationOptions,
            [NotNull] IEnumerable<MediaInfo> medias)
        {
            gmsEntity.Required(nameof(gmsEntity));
            thirdPartyEndpointCreationOptions.Required(nameof(thirdPartyEndpointCreationOptions));
            medias.Required(nameof(medias));

            var endpointCreationInfoList = medias.Select(media => GetQuortexEndpointsCreationInfo(gmsEntity, media, thirdPartyEndpointCreationOptions)).Where(x => x != null).ToArray();

            return endpointCreationInfoList;
        }

        /// <summary>
        /// Gets the information of Aquila Channels for a given GmsGame.
        /// </summary>
        /// <param name="gmsEntity">The GMS Game.</param>
        /// <param name="aquilaChannelCreationOptions">The Aquila channel creation options.</param>
        /// <param name="esniResourcesCreationOptions">The Esni creation options.</param>
        /// <param name="medias"><see cref="MediaInfo"/>s.</param>
        /// <returns>The information to create the channels for this GmsGame.</returns>
        public static IEnumerable<ChannelCreationInfo> GetAquilaChannelsCreationInfo(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] AquilaChannelCreationOptions aquilaChannelCreationOptions,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions,
            [NotNull] IEnumerable<MediaInfo> medias)
        {
            gmsEntity.Required(nameof(gmsEntity));
            aquilaChannelCreationOptions.Required(nameof(aquilaChannelCreationOptions));
            medias.Required(nameof(medias));

            var channelCreationInfoList = medias.Select(media => GetAquilaChannelCreationInfo(gmsEntity, media, aquilaChannelCreationOptions, esniResourcesCreationOptions)).Where(channel => channel != null).ToArray();

            return SetPrimaryFeed(gmsEntity, channelCreationInfoList, aquilaChannelCreationOptions.NSSPrimaryFeedKey);
        }

        /// <summary>
        /// Determines whether the channel id provided is from the primary channel.
        /// </summary>
        /// <param name="gmsEntity">The GMS Game.</param>
        /// <param name="nssPrimaryFeedKey">The NSS primary feed key.</param>
        /// <param name="channelId">The channelId.</param>
        /// <returns>True if the channel id provided is from the primary channel, otherwise false.</returns>
        public static bool IsChannelPrimary(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] string nssPrimaryFeedKey,
            [NotNull] string channelId)
        {
            var primaryChannelId = GetPrimaryMediaName(gmsEntity, nssPrimaryFeedKey);

            return primaryChannelId.EqualsIgnoreCase(channelId);
        }

        /// <summary>
        /// Gets the primary media.
        /// </summary>
        /// <param name="gmsEntity">The GmsEntity with NSS media containing the primary media.</param>
        /// <param name="nssPrimaryFeedKey">The NSS primary feed key.</param>
        /// <returns>The primary media.</returns>
        public static MediaInfo GetPrimaryMedia([NotNull] this GmsEntity gmsEntity, string nssPrimaryFeedKey)
        {
            var nssMedias = gmsEntity.Media.Where(media => media.IsActiveNssMediaWithActiveSchedules);
            var primaryMedia = nssMedias?.FirstOrDefault(x => IsNssPrimaryFeed(x, nssPrimaryFeedKey));
            primaryMedia ??= nssMedias?.FirstOrDefault(x => GetScheduleWithEncoder(x) != null);

            return primaryMedia ?? nssMedias?.First();
        }

        /// <summary>
        /// Determines whether the media should have L2V.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <param name="mediaId">The media identifier.</param>
        /// <returns>
        ///   <c>true</c> if the media has L2V; otherwise, <c>false</c>.
        /// </returns>
        public static bool IsMediaLiveToOnDemand(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] TvpEventCreationOptions tvpEventCreationOptions,
            [NotNull] long mediaId)
        {
            return IsMediaIdPresentInNoVodMediaList(mediaId, tvpEventCreationOptions);
        }

        /// <summary>
        /// Medias that have Scte35 available.
        /// </summary>
        /// <param name="gmsEntity">The <see cref="GmsEntity"/>.</param>
        /// <param name="tvpEventCreationOptions">The <see cref="TvpEventCreationOptions"/>.</param>
        /// <param name="mediaId">The id of the media.</param>
        /// <returns>True or false, depending on whether the value of the KVP with key <see cref="TvpEventCreationOptions.HasInBandScte35Key"/>. If KVP is not present, it returns <see cref="TvpEventCreationOptions.HasInBandScte35DefaultValue"/>.</returns>
        public static bool MediaHasScte35Available(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] TvpEventCreationOptions tvpEventCreationOptions,
            [NotNull] long mediaId)
        {
            var media = gmsEntity.Media.FirstOrDefault(x => x.Id == mediaId);

            media.Required(nameof(media), $"Could not find media with id '{mediaId}'");

            var schedule = GetScheduleWithEncoder(media);

            if (schedule == null)
            {
                schedule = GetScheduleWithoutEncoder(media);
            }

            return GetHasInBandScte35(media, schedule, tvpEventCreationOptions);
        }

        /// <summary>
        /// Gets the information of Entitlements to be created in TVP for a given GmsGame.
        /// </summary>
        /// <param name="gmsEntity">The GMS Game.</param>
        /// <param name="gameDuration">The duration of the game. Used to calculate the EndTime for the live event, as it is not part of the Game Payload.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <param name="dateTimeService">The date time service.</param>
        /// <returns>Data to create entitlements to be created in TVP.</returns>
        public static TvpEventCreationInfo GetTvpEventCreationInfo(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] TimeSpan gameDuration,
            [NotNull] TvpEventCreationOptions tvpEventCreationOptions,
            [NotNull] IDateTime dateTimeService)
        {
            gmsEntity.Required(nameof(gmsEntity));
            tvpEventCreationOptions.Required(nameof(tvpEventCreationOptions));

            if (!gmsEntity.IsRelevantForOrchestrationPlatform)
            {
                return null;
            }

            var gameEasternDateTime = gmsEntity.DateTime.Value.ToEasternTimeZone();
            var tvpEventTeamRoles = new List<TvpEventTeamCreationInfo>();
            var tvpEventStatus = TvpEventStatus.Scheduled;
            var eventScheduleStartUtc = gmsEntity.DateTime.Value - tvpEventCreationOptions.TimeSpanToSubstractToStartTime;
            var eventScheduleEndUtc = gmsEntity.DateTime.Value + gameDuration;
            var gamePackageParentPackages = Enumerable.Empty<string>();
            var seasonId = GetSeasonId(gmsEntity);
            string eventName;
            string eventType;

            if (gmsEntity.IsEvent)
            {
                var gmsEvent = gmsEntity as GmsEvent;
                eventName = gmsEvent.Name;
                eventType = "Special";
                eventScheduleEndUtc = gmsEvent.EndDateTime ?? eventScheduleEndUtc;
            }
            else
            {
                var gmsGame = gmsEntity as GmsGame;
                tvpEventStatus = ConvertScheduleCodeToTvpEventStatus(gmsGame.ScheduleCode);

                if (gmsGame.HasScheduleCodePostponedOrToBeDetermined)
                {
                    // in the case of a Game with TBD/Postponed Schedule Code, set its time to happen 3 years in the future
                    var farFuture = dateTimeService.Now.AddYears(3);
                    eventScheduleStartUtc = farFuture;
                    eventScheduleEndUtc = farFuture + gameDuration;
                }

                eventName = $"{gmsGame.AwayTeam.Abbr} @ {gmsGame.HomeTeam.Abbr} on {gameEasternDateTime:yyyy-MM-dd}";
                eventType = "Game";
                tvpEventTeamRoles = new List<TvpEventTeamCreationInfo>
                {
                    GetTvpEventTeamCreationInfo(gmsGame.HomeTeam, true),
                    GetTvpEventTeamCreationInfo(gmsGame.AwayTeam, false),
                };
                gamePackageParentPackages = GetGamePackageParentPackages(gmsGame, tvpEventCreationOptions);
            }

            var tvpEventCreationInfo = new TvpEventCreationInfo
            {
                EventExternalId = gmsEntity.Id,
                EventName = eventName,
                EventType = eventType,
                ShowType = "Event",
                EventStatus = tvpEventStatus,
                TournamentSeasonId = seasonId,
                EventTeamRoles = tvpEventTeamRoles,
                EventScheduleExternalId = gmsEntity.Id,
                EventScheduleName = eventName,
                EventScheduleStartUtc = eventScheduleStartUtc,
                EventScheduleEndUtc = eventScheduleEndUtc,
                EventScheduleUpid = string.Empty, // Used for SCTE35 markers denoting end of game, we don't use it for now.
                EventLocation = GetEventLocation(gmsEntity.Location),
                Productions = GetTvpProductionCreationInfo(gmsEntity, tvpEventCreationOptions),
                GamePackageParentPackages = gamePackageParentPackages,
            };

            return tvpEventCreationInfo;
        }

        /// <summary>
        /// Gets the TVP event cleanup information.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <returns>TvpEventCleanupInfo.</returns>
        public static TvpEventCleanupInfo GetTvpEventCleanupInfo([NotNull] this GmsEntity gmsEntity)
        {
            gmsEntity.Required(nameof(gmsEntity));
            return new TvpEventCleanupInfo
            {
                EventExternalId = gmsEntity.Id,
            };
        }

        /// <summary>
        /// Gets the team zip update workflow intents.
        /// </summary>
        /// <param name="teamZips">The team zips.</param>
        /// <param name="esniAudienceList">The esni audience list.</param>
        /// <param name="dateTimeService">The date time service.</param>
        /// <returns>
        /// Prisma Setup intent to Upsert Viewing Policies.
        /// </returns>
        /// <exception cref="System.ArgumentNullException">esniViewingPolicyList.</exception>
        public static WorkflowIntent GetTeamZipUpdateWorkflowIntent([NotNull] this GmsTeamZips teamZips, IEnumerable<EsniAudience> esniAudienceList, [NotNull] IDateTime dateTimeService)
        {
            teamZips.Required(nameof(teamZips));
            esniAudienceList.Required(nameof(esniAudienceList));
            dateTimeService.Required(nameof(dateTimeService));
            var workflowIntent = new WorkflowIntent
            {
                ChannelId = teamZips.TeamId,
                LiveEventTime = dateTimeService.Now.UtcDateTime,
                WorkflowId = NbaWorkflowIds.AudienceSetup,
                ActorSpecificDetails = new List<ActorSpecificDetail>
                {
                    ActorSpecificDetail.CreateForPrismaActor(new PrismaAudienceInfo
                    {
                        AudienceIdsToAdd = esniAudienceList.Select(x => x.Id),
                    }),
                },
            };

            var tvpActorSpecificDetail = ActorSpecificDetail.CreateForTvpActor(teamZips);

            workflowIntent.ActorSpecificDetails.Add(tvpActorSpecificDetail);

            return workflowIntent;
        }

        /// <summary>
        /// Given a GMS Entity, it returns an instance of <see cref="PrismaMediaInfo"/> with the details of all the SCTE-224 entities needed to support Blackouts in PRISMA.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="esniResourcesCreationOptions">Configurable options for ESNI Resources provisioning in PRISMA.</param>
        /// <param name="nssPrimaryFeedKey">The NSS primary feed key.</param>
        /// <param name="gameDuration">EventMetadataEnd Offset.</param>
        /// <returns>An instance of <see cref="PrismaMediaInfo"/> with the details of all the SCTE-224 entities needed to support Blackouts in PRISMA.</returns>
        public static PrismaMediaInfo GetPrismaInfo(
            [NotNull] this GmsEntity gmsEntity,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions,
            [NotNull] string nssPrimaryFeedKey,
            TimeSpan gameDuration)
        {
            gmsEntity.Required(nameof(gmsEntity));
            var eventHasPreOrPostGame = gmsEntity.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules).Any(media => media.GetPreferredSchedule().HasPreGameExperience(media, esniResourcesCreationOptions) || media.GetPreferredSchedule().HasPostGameExperience(media, esniResourcesCreationOptions));
            var nssBlackoutKeys = new string[] { esniResourcesCreationOptions.NssBlackoutTeamRsnKey, esniResourcesCreationOptions.NssBlackoutTeamOtaKey };
            if (!gmsEntity.IsRelevantForOrchestrationPlatform || !gmsEntity.HasActiveNssMediasWithActiveSchedules || (!gmsEntity.IncludesBlackoutData(nssBlackoutKeys) && !eventHasPreOrPostGame))
            {
                return null;
            }

            var prismaInfo = GetPrismaBlackoutInfo(gmsEntity, esniResourcesCreationOptions, nssPrimaryFeedKey, gameDuration, nssBlackoutKeys);

            prismaInfo ??= new PrismaMediaInfo();
            prismaInfo.MediasToUpsert ??= new List<PrismaMedia>();

            AppendPrismaDynamicEntitlementToMediaInfo(prismaInfo, gmsEntity, esniResourcesCreationOptions, gameDuration);

            return prismaInfo;
        }

        /// <summary>
        /// Returns the list of ESNI resources created as part of this <see cref="PrismaMediaInfo"/> that needs to be deleted as part of <see cref="NbaWorkflowIds.EventMetadataCleanup"/>.
        /// The list doesn't include the IDs of the reusable Audiences and Viewing Policies (e.g.: country level viewing policies and audiences, or team level audiences).
        /// </summary>
        /// <returns>the list of ESNI resources created as part of this <see cref="PrismaMediaInfo"/> that needs to be deleted as part of <see cref="NbaWorkflowIds.EventMetadataCleanup"/>.</returns>
        /// <param name="prismaMediaInfo">The prisma media info.</param>
        public static PrismaDeleteResourceInfo GetEsniResourcesIdsForCleanup([NotNull] this PrismaMediaInfo prismaMediaInfo)
        {
            prismaMediaInfo.Required(nameof(prismaMediaInfo));

            var esniResourceIds = new List<string>();

            // ESNI Medias
            if (prismaMediaInfo.MediasToUpsert != null)
            {
                esniResourceIds.AddRange(prismaMediaInfo.MediasToUpsert.Select(x => x.Id));

                // ESNI Policies
                var mediaPoints = prismaMediaInfo.MediasToUpsert.SelectMany(x => x.MediaPoint);
                var policies = mediaPoints.Where(x => x.Apply != null).SelectMany(x => x.Apply).Select(x => x.Policy);
                esniResourceIds
                    .AddRange(
                        policies
                        .Where(x => !x.Id.EqualsIgnoreCase(PrismaMediaPoint.GetDefaultPolicy().Id)
                                    && !x.Id.EqualsIgnoreCase(GetPreGamePolicy().Id)
                                    && !x.Id.EqualsIgnoreCase(GetGamePolicy().Id)
                                    && !x.Id.EqualsIgnoreCase(GetPostGamePolicy().Id))
                    .Select(x => x.Id));

                // ESNI Local Viewing Policies
                var localPolicies = policies.Where(x => x.Id.Contains("local", StringComparison.OrdinalIgnoreCase));
                var localViewingPolicies = localPolicies.SelectMany(x => x.ViewingPolicy);
                esniResourceIds.AddRange(localViewingPolicies.Select(x => x.Id));

                // ESNI Local Audiences
                var localAudiences = localViewingPolicies.Select(x => x.Audience);
                esniResourceIds.AddRange(localAudiences.Select(x => x.Id));
            }

            return new PrismaDeleteResourceInfo { EsniResourceIds = esniResourceIds.Distinct() };
        }

        /// <summary>
        /// Updates the Prisma Info in place based on the medias updated.
        /// </summary>
        /// <param name="prismaInfo">The new prismaInfo.</param>
        /// <param name="existingVideoPlatformSchedule">The existing Schedule.</param>
        /// <returns>Prisma Media Info.</returns>
        public static PrismaMediaInfo UpdateEsniIdsForGameUpdate(
            this PrismaMediaInfo prismaInfo,
            VideoPlatformSchedule existingVideoPlatformSchedule)
        {
            var existingPrismaInfo = existingVideoPlatformSchedule.GetActorSpecificDetailsFromSchedule<PrismaMediaInfo>(NbaWorkflowIds.EventMetadataSetup, ActorIds.PrismaMedias);
            var existingCleanupIds = existingPrismaInfo?.GetEsniResourcesIdsForCleanup()?.EsniResourceIds.ToList();
            var existingtoDeleteIds = existingPrismaInfo?.EsniResourceIdsToDelete?.ToList() ?? new List<string>();

            var previousIrrelaventGamePrismaInfo = existingVideoPlatformSchedule.GetActorSpecificDetailsFromSchedule<PrismaDeleteResourceInfo>(NbaWorkflowIds.EventMetadataDelete, ActorIds.PrismaMedias);
            var existingIdsFromEventMetadataDelete = previousIrrelaventGamePrismaInfo?.EsniResourceIds?.ToList() ?? new List<string>();
            existingtoDeleteIds.AddRange(existingIdsFromEventMetadataDelete);

            var currentEsniResourcesIds = prismaInfo?.GetEsniResourcesIdsForCleanup()?.EsniResourceIds.ToList();

            // Append the List of Id's which are to be deleted. (previous + current)
            if (existingCleanupIds != null && existingCleanupIds.Any())
            {
                existingtoDeleteIds.AddRange(existingCleanupIds);
                existingtoDeleteIds = existingtoDeleteIds.Distinct().ToList();
            }

            // Remove the valid Id's which are part of the setup.
            if (currentEsniResourcesIds != null)
            {
                existingtoDeleteIds.RemoveAll(id => currentEsniResourcesIds.Contains(id));
            }

            if (existingtoDeleteIds.Any())
            {
                prismaInfo ??= new PrismaMediaInfo();
                prismaInfo.EsniResourceIdsToDelete = existingtoDeleteIds;
            }

            if (prismaInfo != null)
            {
                prismaInfo.EsniResourceIdsToDelete = prismaInfo.EsniResourceIdsToDelete?.Where(x => !string.Equals(x, "/NBA/policy/event", StringComparison.OrdinalIgnoreCase) && !string.Equals(x, "/NBA/policy/blackout", StringComparison.OrdinalIgnoreCase)).ToList();
            }

            return prismaInfo;
        }

        /// <summary>
        /// Updates the Prisma Info in place based on the medias updated.
        /// </summary>
        /// <typeparam name="T">The type of the actor specific details to return.</typeparam>
        /// <param name="existingVideoPlatformSchedule">The existing Schedule.</param>
        /// <param name="workflowId">The Workflow Id.</param>
        /// <param name="actorId">The Actor Id.</param>
        /// <returns>Actor Specific Details of the Workflow.</returns>
        public static T GetActorSpecificDetailsFromSchedule<T>(
            this VideoPlatformSchedule existingVideoPlatformSchedule,
            string workflowId,
            string actorId)
        {
            var intent = existingVideoPlatformSchedule?.WorkflowIntents?.FirstOrDefault(x => x.WorkflowId == workflowId);
            var actorDetails = intent?.VideoPlatformActorSpecificDetails?.FirstOrDefault(x => x.ActorId == actorId);
            var actorDetailsConverted = actorDetails?.Data != null ? JsonConvert.DeserializeObject<T>(JsonConvert.SerializeObject(actorDetails.Data)) : default;
            return actorDetailsConverted;
        }

        /// <summary>
        /// Gets the value from <see cref="Schedule"/> KeyValuePairs.
        /// If it doesn't find the key, it returns the value from <see cref="MediaInfo"/> KeyValuePairs.
        /// If there is no KVP with that key in neither Schedule nor Media, it returns null.
        /// </summary>
        /// <param name="schedule">The Schedule.</param>
        /// <param name="key">The key.</param>
        /// <param name="media">The MediaInfo.</param>
        /// <returns>The value.</returns>
        public static string GetValueFromScheduleOrMediaKeyValuePairs([NotNull] this Schedule schedule, string key, [NotNull] MediaInfo media)
        {
            return schedule.Operations.KeyValuePairs?.FirstOrDefault(x => x.Key == key)?.Value
                ?? media.KeyValuePairs?.FirstOrDefault(x => x.Key == key)?.Value;
        }

        /// <summary>
        /// Gets the channel identifier.
        /// </summary>
        /// <param name="gmsEntity">The game.</param>
        /// <param name="media">The media.</param>
        /// <returns>The channel ID.</returns>
        public static string GetChannelId([NotNull] this GmsEntity gmsEntity, [NotNull] MediaInfo media)
        {
            return gmsEntity.GetMediaName(gmsEntity, media);
        }

        /// <summary>
        /// Gets the live event identifier from channel identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The GMS Live Event ID (might be a game or a non-game event) for a given channel.</returns>
        public static string GetLiveEventIdFromChannelId([NotNull] string productionId)
        {
            return productionId.Substring(1, GmsGameTransformationConstants.GmsEntityIdLength);
        }

        /// <summary>
        /// Gets the media identifier from a game's channel identifier.
        /// </summary>
        /// <param name="channelId">The channel identifier.</param>
        /// <returns>The GMS Media ID (only for games) for a given channel.</returns>
        public static string GetGameMediaIdFromChannelId([NotNull] string channelId)
        {
            if (channelId.StartsWith("e", StringComparison.OrdinalIgnoreCase))
            {
                return channelId.Substring(12, GmsGameTransformationConstants.GmsMediaIdLength);
            }

            return channelId.Substring(14, GmsGameTransformationConstants.GmsMediaIdLength);
        }

        /// <summary>
        /// Gets a preferred <see cref="Schedule"/> taking precedence a <see cref="Schedule"/> with encoder.
        /// </summary>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <returns>The <see cref="Schedule"/> with most precedence.</returns>
        public static Schedule GetPreferredSchedule([NotNull] this MediaInfo media)
        {
            media.Required(nameof(media));
            var preferredSchedule = GetScheduleWithEncoder(media) ?? GetScheduleWithoutEncoder(media);

            return preferredSchedule;
        }

        /// <summary>
        /// Returns if exists a custom offset for the media for specific nbaWorkflowId if any.
        /// </summary>
        /// <param name="schedule">The <see cref="Schedule"/> to look for the offset key.</param>
        /// <param name="mediaInfo">The <see cref="MediaInfo"/> to look for the offset key alternatively.</param>
        /// <param name="customWorkflowOffsetOptions"><see cref="CustomWorkflowOffsetOptions"/>.</param>
        /// <param name="nbaWorkflowId">The <see cref="NbaWorkflowIds"/> value.</param>
        /// <returns>The custom offset.</returns>
        public static bool HasCustomOffset([NotNull] this Schedule schedule, [NotNull] MediaInfo mediaInfo, [NotNull] CustomWorkflowOffsetOptions customWorkflowOffsetOptions, string nbaWorkflowId)
        {
            var customOffsets = GetCustomOffsets(schedule, mediaInfo, customWorkflowOffsetOptions);
            return customOffsets.ContainsKey(nbaWorkflowId);
        }

        /// <summary>
        /// Returns if the productions has PostGame experience.
        /// </summary>
        /// <param name="productionId">The productionIs.</param>
        /// <param name="gmsGame">the gmsGame.</param>
        /// <returns>True or false production has PostGame experience.</returns>
        public static bool ProductionHasPostGame(string productionId, GmsGame gmsGame)
        {
            var schedule = GetScheduleFromProductionId(productionId, gmsGame);

            var experiences = schedule.Operations.KeyValuePairs?.FirstOrDefault(x => x.Key == NSSAssociatedExperiences)?.Value;

            if (!string.IsNullOrEmpty(experiences))
            {
                return experiences.Contains(PostGameExperience, StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }

        /// <summary>
        /// Return Schedule from productionId.
        /// </summary>
        /// <param name="productionId">The Production Id.</param>
        /// <param name="gmsGame">The GmsGame.</param>
        /// <returns>The Schedule match production Id.</returns>
        public static Schedule GetScheduleFromProductionId(string productionId, [NotNull] GmsGame gmsGame)
        {
            var mediaId = GetGameMediaIdFromChannelId(productionId);
            var media = gmsGame.Media.First(x =>
                CompareMediaId(mediaId, x.Id));
            return media.Schedules.First(x => x.Active);
        }

        /// <summary>
        /// Gets whether the media has associated pre-game experience or not, based on the KVP coming from GMS.
        /// </summary>
        /// <param name="schedule">The <see cref="Schedule"/>.</param>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <param name="esniResourcesCreationOptions">The <see cref="EsniResourcesCreationOptions"/>.</param>
        /// <returns>True if the media has pre-game experience, otherwise false.</returns>
        public static bool HasPreGameExperience([NotNull] this Schedule schedule, [NotNull] MediaInfo media, [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions)
        {
            return GetHasAssociatedExperience(media, schedule, esniResourcesCreationOptions, esniResourcesCreationOptions.NssAssociatedPreGameExperienceValue);
        }

        /// <summary>
        /// Gets whether the media has associated post-game experience or not, based on the KVP coming from GMS.
        /// </summary>
        /// <param name="schedule">The <see cref="Schedule"/>.</param>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <param name="esniResourcesCreationOptions">The <see cref="EsniResourcesCreationOptions"/>.</param>
        /// <returns>True if the media has post-game experience, otherwise false.</returns>
        public static bool HasPostGameExperience([NotNull] this Schedule schedule, [NotNull] MediaInfo media, [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions)
        {
            return GetHasAssociatedExperience(media, schedule, esniResourcesCreationOptions, esniResourcesCreationOptions.NssAssociatedPostGameExperienceValue);
        }

        /// <summary>
        /// Gets the Policy, viewing policy and Audience for Pre-Game.
        /// </summary>
        /// <returns>Default Policy.</returns>
        public static PrismaPolicy GetPreGamePolicy() => new PrismaPolicy()
        {
            Id = "/NBA/policy/pregame",
            ViewingPolicy = new List<PrismaViewingPolicy>()
            {
                new PrismaViewingPolicy()
                {
                    Id = $"/NBA/viewingpolicy/pregame",
                    Audience = new PrismaAudience()
                    {
                        Id = $"/NBA/audience/pregame",
                        Match = PrismaAudienceMatch.ANY,
                    },
                },
            },
        };

        /// <summary>
        /// Gets the Policy, viewing policy and Audience for Post-Game.
        /// </summary>
        /// <returns>Default Policy.</returns>
        public static PrismaPolicy GetPostGamePolicy() => new PrismaPolicy()
        {
            Id = "/NBA/policy/postgame",
            ViewingPolicy = new List<PrismaViewingPolicy>()
            {
                new PrismaViewingPolicy()
                {
                    Id = $"/NBA/viewingpolicy/postgame",
                    Audience = new PrismaAudience()
                    {
                        Id = $"/NBA/audience/postgame",
                        Match = PrismaAudienceMatch.ANY,
                    },
                },
            },
        };

        /// <summary>
        /// Gets the Policy, viewing policy and Audience for Game.
        /// </summary>
        /// <returns>Default Policy.</returns>
        public static PrismaPolicy GetGamePolicy() => new PrismaPolicy()
        {
            Id = "/NBA/policy/event",
            ViewingPolicy = new List<PrismaViewingPolicy>()
            {
                new PrismaViewingPolicy()
                {
                    Id = $"/NBA/viewingpolicy/event",
                    Audience = new PrismaAudience()
                    {
                        Id = $"/NBA/audience/event",
                        Match = PrismaAudienceMatch.ANY,
                    },
                },
            },
        };

        /// <summary>
        /// The method that create blackout service data.
        /// </summary>
        /// <param name="gmsEntity">The Game/Event GmsEntity.</param>
        /// <param name="esniResourcesCreationOptions">The ESNI Resources Creation Options.</param>
        /// <param name="currentBlackoutsIds">List of existing blackouts to update.</param>
        /// <returns>List of blackout info data.</returns>
        public static (Collection<VideoPlatformBlackout>, Collection<VideoPlatformBlackout>) GetBlackoutServiceInfo([NotNull] this GmsEntity gmsEntity, [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions, Collection<string> currentBlackoutsIds)
        {
            var nssBlackoutKeys = new string[] { esniResourcesCreationOptions.NssBlackoutTeamRsnKey, esniResourcesCreationOptions.NssBlackoutTeamOtaKey };

            var blackoutInfo = new Collection<VideoPlatformBlackout>();
            var blackoutInfoToUpdate = new Collection<VideoPlatformBlackout>();

            if (!gmsEntity.IncludesBlackoutData(nssBlackoutKeys))
            {
                return (blackoutInfo, blackoutInfoToUpdate);
            }

            // Creation of one Media and one MediaPoint per NSS Media
            var nssMedias = gmsEntity.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules);

            if (esniResourcesCreationOptions.MediaIdsToIgnore != null)
            {
                nssMedias = nssMedias.Where(x => !esniResourcesCreationOptions.MediaIdsToIgnore.Contains(x.Id));
            }

            var gmsEntityContainsMediaThatExemptWorldBlackouts = esniResourcesCreationOptions.MediaIdsWithoutWorldBlackouts != null && gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.MediaIdsWithoutWorldBlackouts.Contains(x.Id));
            var gmsEntityContainsMediaThatDontCauseWorldBlackouts = esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts != null &&
                                                                    gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts.Contains(x.Id));
            var gmsEntityContainsMediaThatOverwriteDontCauseWorldBlackouts = gmsEntityContainsMediaThatDontCauseWorldBlackouts &&
                                                                             gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts.Contains(x.Id));
            var dontCreateWorldBlackouts = gmsEntityContainsMediaThatDontCauseWorldBlackouts &&
                                           !gmsEntityContainsMediaThatOverwriteDontCauseWorldBlackouts;

            if (!(dontCreateWorldBlackouts || gmsEntityContainsMediaThatExemptWorldBlackouts) && gmsEntity.Media.Any(x => x.IsMediaTypeTV && x.IsRegionalDistribution && x.IsRegionCanada && x.IsActiveMediaWithActiveSchedules))
            {
                GetGlobalBlackout(blackoutInfo, blackoutInfoToUpdate, nssMedias, gmsEntity, currentBlackoutsIds);
            }

            var gmsEntityContainsMediaThatExemptRegionalBlackouts = esniResourcesCreationOptions.MediaIdsWithoutRegionalBlackouts != null && gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.MediaIdsWithoutRegionalBlackouts.Contains(x.Id));

            // Adding Regional (US) Policy, ViewingPolicy and Audience
            if ((!gmsEntityContainsMediaThatExemptRegionalBlackouts) && gmsEntity.Media.Any(x => x.IsMediaTypeTV && x.IsRegionalDistribution && x.IsRegionUS && x.IsActiveMediaWithActiveSchedules))
            {
                GetRegionalBlackout(blackoutInfo, blackoutInfoToUpdate, nssMedias, gmsEntity, currentBlackoutsIds);
            }

            var gmsEntityContainsMediaThatExemptLocalBlackouts = esniResourcesCreationOptions.MediaIdsWithoutLocalBlackouts != null && gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.MediaIdsWithoutLocalBlackouts.Contains(x.Id));

            if (!gmsEntityContainsMediaThatExemptLocalBlackouts)
            {
                GetLocalBlackout(blackoutInfo, blackoutInfoToUpdate, nssMedias, gmsEntity, esniResourcesCreationOptions, currentBlackoutsIds);
            }

            return (blackoutInfo, blackoutInfoToUpdate);
        }

        /// <summary>
        /// Get the pool id for third party media type.
        /// </summary>
        /// <param name="type">third party media type.</param>
        /// <param name="thirdPartyEndpointCreationOptions">ThirdPartyEndpointCreationOptions.</param>
        /// <returns>The pool Uuid.</returns>
        private static string GetPoolIdForThirdPartyMedias(string type, ThirdPartyEndpointCreationOptions thirdPartyEndpointCreationOptions)
        {
            switch (type)
            {
                case "dr":
                    return thirdPartyEndpointCreationOptions.PoolUUIDDR;
                case "radio":
                    return thirdPartyEndpointCreationOptions.PoolUUIDRadio;
                case "quortex":
                    return thirdPartyEndpointCreationOptions.PoolUUID;
                default:
                    return null;
            }
        }

        /// <summary>
        /// Encode the object to base64 string.
        /// </summary>
        /// <param name="obj">The object to encode.</param>
        /// <returns>base64 object encoded.</returns>
        private static string DictionaryToBase64Encoder(object obj)
        {
            if (obj == null)
            {
                return null;
            }

            string json = JsonConvert.SerializeObject(obj, Newtonsoft.Json.Formatting.None);
            var bytes = Encoding.UTF8.GetBytes(json);
            var encoded = Convert.ToBase64String(bytes);
            return encoded;
        }

        /// <summary>
        /// Get global blackout information.
        /// </summary>
        /// <param name="blackoutInfo">the list of blackout data.</param>
        /// <param name="blackoutInfoToUpdate">the list of existing blackout data.</param>
        /// <param name="nssMedias">the nssMedias.</param>
        /// <param name="gmsEntity">the gmsEntity.</param>
        /// <param name="currentBlackoutsIds">List of existing blackoutsIds.</param>
        private static void GetGlobalBlackout(
            Collection<VideoPlatformBlackout> blackoutInfo,
            Collection<VideoPlatformBlackout> blackoutInfoToUpdate,
            IEnumerable<MediaInfo> nssMedias,
            GmsEntity gmsEntity,
            Collection<string> currentBlackoutsIds)
        {
            nssMedias.ForEach(nssMedia =>
            {
                var productionId = gmsEntity.GetMediaName(gmsEntity, nssMedia);
                var policyId = EsniExtensions.GetEsniWorldPolicyId(productionId);
                var blackoutId = $"{productionId}.{EsniMediaNames.CA}";
                var blackout = new VideoPlatformBlackout()
                {
                    BlackoutDecision = "true",
                    Id = blackoutId,
                    Policy = policyId,
                    StartTimeUtc = gmsEntity.DateTime.Value,
                    EndTimeUtc = gmsEntity.DateTime.Value.Add(XmlConvert.ToTimeSpan(GlobalBlackoutDuration)),
                };

                if (currentBlackoutsIds.Contains(blackoutId))
                {
                    blackoutInfoToUpdate.Add(blackout);
                }
                else
                {
                    blackoutInfo.Add(blackout);
                }
            });
        }

        /// <summary>
        /// Get global blackout information.
        /// </summary>
        /// <param name="blackoutInfo">the list of blackout data.</param>
        /// <param name="blackoutInfoToUpdate">the list of existing blackout data.</param>
        /// <param name="nssMedias">the nssMedias.</param>
        /// <param name="gmsEntity">the gmsEntity.</param>param>
        /// <param name="currentBlackoutsIds">List of existing blackoutsIds.</param>
        private static void GetRegionalBlackout(Collection<VideoPlatformBlackout> blackoutInfo, Collection<VideoPlatformBlackout> blackoutInfoToUpdate, IEnumerable<MediaInfo> nssMedias, GmsEntity gmsEntity, Collection<string> currentBlackoutsIds)
        {
            var summerLeagueEndDateTime = new DateTimeOffset(new DateTime(2025, 07, 20));
            var calculatedDuration = (int)(summerLeagueEndDateTime - gmsEntity.DateTime.Value).TotalMinutes;
            var regionalPolicyDuration =
                (gmsEntity.Id.StartsWith("13", true, CultureInfo.InvariantCulture) ||
                 gmsEntity.Id.StartsWith("15", true, CultureInfo.InvariantCulture) ||
                 gmsEntity.Id.StartsWith("16", true, CultureInfo.InvariantCulture)) &&
                calculatedDuration > 0
                    ? $"PT{calculatedDuration}M"
                    : RegionalBlackoutDuration;

            nssMedias.ForEach(nssMedia =>
            {
                var productionId = gmsEntity.GetMediaName(gmsEntity, nssMedia);
                var policyId = EsniExtensions.GetEsniRegionalPolicyId(productionId);
                var blackoutId = $"{productionId}.{EsniMediaNames.US}";
                var blackout = new VideoPlatformBlackout()
                {
                    BlackoutDecision = "true",
                    Id = blackoutId,
                    Policy = policyId,
                    StartTimeUtc = gmsEntity.DateTime.GetValueOrDefault(),
                    EndTimeUtc = gmsEntity.DateTime.GetValueOrDefault().Add(XmlConvert.ToTimeSpan(regionalPolicyDuration)),
                };

                if (currentBlackoutsIds.Contains(blackoutId))
                {
                    blackoutInfoToUpdate.Add(blackout);
                }
                else
                {
                    blackoutInfo.Add(blackout);
                }
            });
        }

        /// <summary>
        /// Get local blackout information.
        /// </summary>
        /// <param name="blackoutInfo">the list of blackout data.</param>
        /// <param name="blackoutInfoToUpdate">the list of existing blackout data.</param>
        /// <param name="nssMedias">the nssMedias.</param>
        /// <param name="gmsEntity">the gmsEntity.</param>
        /// <param name="esniResourcesCreationOptions">The esniResourcesCreationOptions.</param>
        /// <param name="currentBlackoutsIds">List of existing blackoutsIds.</param>
        private static void GetLocalBlackout(Collection<VideoPlatformBlackout> blackoutInfo, Collection<VideoPlatformBlackout> blackoutInfoToUpdate, IEnumerable<MediaInfo> nssMedias, GmsEntity gmsEntity, EsniResourcesCreationOptions esniResourcesCreationOptions, Collection<string> currentBlackoutsIds)
        {
            esniResourcesCreationOptions.Required(nameof(esniResourcesCreationOptions));
            gmsEntity.Required(nameof(gmsEntity));

            var zipCodesAreas = GetZipCodeAreas(gmsEntity, esniResourcesCreationOptions);

            zipCodesAreas.ForEach(zipCodesArea =>
            {
                var blackoutIds = new List<string>();
                nssMedias.ForEach(nssMedia =>
                {
                    var productionId = gmsEntity.GetMediaName(gmsEntity, nssMedia);
                    var blackoutId = $"{productionId}.{EsniMediaNames.US}.{zipCodesArea}";
                    if (!blackoutIds.Contains(blackoutId))
                    {
                        var blackout = new VideoPlatformBlackout()
                        {
                            BlackoutDecision = "true",
                            Id = blackoutId,
                            Policy = EsniExtensions.GetEsniLocalPolicyId(productionId),
                            StartTimeUtc = gmsEntity.DateTime.Value,
                            EndTimeUtc = gmsEntity.DateTime.Value.Add(XmlConvert.ToTimeSpan(LocalBlackoutDuration)),
                        };

                        if (currentBlackoutsIds.Contains(blackoutId))
                        {
                            blackoutInfoToUpdate.Add(blackout);
                        }
                        else
                        {
                            blackoutInfo.Add(blackout);
                        }
                    }
                });
            });
        }

        /// <summary>
        /// Get the zipCodeAreas eg OTA-TeamTriCode.
        /// </summary>
        /// <param name="gmsEntity">The gmsEntity.</param>
        /// <param name="esniResourcesCreationOptions">The esniResourcesCreationOptions.</param>
        /// <returns>market and team tricode.</returns>
        private static IList<string> GetZipCodeAreas(GmsEntity gmsEntity, EsniResourcesCreationOptions esniResourcesCreationOptions)
        {
            if (gmsEntity is GmsGame gmsGame)
            {
                var mediasWithTypeTvRnsOrOtaDistributionHomeOrAwayTeamContext = gmsEntity.Media.Where(x =>
                    x.IsMediaTypeTV &&
                    x.IsRSNOrOTADistribution &&
                    x.IsHomeOrAwayTeamContext &&
                    x.IsRegionUS &&
                    x.IsActiveMediaWithActiveSchedules);
                var zipCodeAreas = mediasWithTypeTvRnsOrOtaDistributionHomeOrAwayTeamContext
                    .Select(x => new
                    {
                        TeamTricode = x.IsHomeTeamContext ?
                            gmsGame.HomeTeam.Abbr.ToUpperInvariant() :
                            gmsGame.AwayTeam.Abbr.ToUpperInvariant(),
                        MarketCode = x.Distribution.Name,
                    })
                    .Select(x =>
                    {
                        return EsniMediaNames.DistributionOTA.EqualsIgnoreCase(x.MarketCode) ?
                            $"OTA-{x.TeamTricode.ToUpperInvariant()}" :
                            $"RSN-{x.TeamTricode.ToUpperInvariant()}";
                    }).ToList();

                return zipCodeAreas;
            }
            else
            {
                var zipCodeAreas = new Collection<string>();
                gmsEntity.Media.ForEach(media =>
                {
                    var preferredSchedule = media.GetPreferredSchedule();

                    var nssBlackoutTeamRnsValue =
                        preferredSchedule.GetValueFromScheduleOrMediaKeyValuePairs(
                            esniResourcesCreationOptions.NssBlackoutTeamRsnKey, media);
                    if (!nssBlackoutTeamRnsValue.IsNullOrEmpty())
                    {
                        zipCodeAreas.Add($"RNS-{nssBlackoutTeamRnsValue}");
                    }

                    var nssBlackoutTeamOtaValue =
                        preferredSchedule.GetValueFromScheduleOrMediaKeyValuePairs(
                            esniResourcesCreationOptions.NssBlackoutTeamOtaKey, media);

                    if (!nssBlackoutTeamOtaValue.IsNullOrEmpty())
                    {
                        zipCodeAreas.Add($"OTA-{nssBlackoutTeamRnsValue}");
                    }
                });

                return zipCodeAreas;
            }
        }

        /// <summary>
        /// Append Pre and Post Game mediaPoints to a PrismaInfo entity for a Game/Event if it has the KVPs.
        /// </summary>
        /// <param name="prismaInfo">The PrismaInfo for the MediaPoints to be added to.</param>
        /// <param name="gmsEntity">The Game/Event GmsEntity.</param>
        /// <param name="esniResourcesCreationOptions">The EsniResourcesCreationOptions.</param>
        /// <param name="gameDuration">The Game Duration.</param>
        private static void AppendPrismaDynamicEntitlementToMediaInfo([NotNull] PrismaMediaInfo prismaInfo, [NotNull] GmsEntity gmsEntity, [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions, TimeSpan gameDuration)
        {
            var activeNssMedias = gmsEntity.Media.Where(media => media.IsActiveNssMediaWithActiveSchedules);
            foreach (var nssMedia in activeNssMedias)
            {
                var productionId = gmsEntity.GetMediaName(gmsEntity, nssMedia);
                var mediaPoints = new List<PrismaMediaPoint>();

                var mediaHasPreGameExperience = nssMedia.GetPreferredSchedule().HasPreGameExperience(nssMedia, esniResourcesCreationOptions);
                var mediaHasPostGameExperience = nssMedia.GetPreferredSchedule().HasPostGameExperience(nssMedia, esniResourcesCreationOptions);
                if (mediaHasPreGameExperience || mediaHasPostGameExperience)
                {
                    mediaPoints.Add(new PrismaMediaPoint
                    {
                        Id = EsniExtensions.GetGameStartMediaPointId(productionId),
                        RelatedProductionId = productionId,
                        MatchTime = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes),
                        Apply = new List<PrismaPolicyApply>() { new PrismaPolicyApply() { Policy = GetGamePolicy() } },
                        Effective = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                        Expires = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                        MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointGameStartMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                    });

                    mediaPoints.Add(new PrismaMediaPoint
                    {
                        Id = EsniExtensions.GetGameEndMediaPointId(productionId),
                        MatchTime = gmsEntity.DateTime.Value.Add(gameDuration),
                        Remove = new List<PrismaPolicyRemove>() { new PrismaPolicyRemove() { Policy = GetGamePolicy() } },
                        Effective = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes),
                        Expires = gmsEntity.DateTime.Value.Add(gameDuration).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                        RelatedProductionId = productionId,
                        MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointGameEndMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                    });

                    if (mediaHasPreGameExperience)
                    {
                        mediaPoints.Add(new PrismaMediaPoint
                        {
                            Id = EsniExtensions.GetPreGameStartMediaPointId(productionId),
                            RelatedProductionId = productionId,
                            MatchTime = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameStartMatchTimeInMinutes),
                            Apply = new List<PrismaPolicyApply>() { new PrismaPolicyApply() { Policy = GetPreGamePolicy() } },
                            Effective = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameStartMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                            Expires = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameStartMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                            MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPreGameStartMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                        });

                        mediaPoints.Add(new PrismaMediaPoint
                        {
                            Id = EsniExtensions.GetPreGameEndMediaPointId(productionId),
                            MatchTime = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameEndMatchTimeOffsetInMinutes),
                            Remove = new List<PrismaPolicyRemove>() { new PrismaPolicyRemove() { Policy = GetPreGamePolicy() } },
                            Effective = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameEndMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                            Expires = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointPreGameEndMatchTimeOffsetInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                            RelatedProductionId = productionId,
                            MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPreGameEndMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                        });
                    }

                    if (mediaHasPostGameExperience)
                    {
                        mediaPoints.Add(new PrismaMediaPoint
                        {
                            Id = EsniExtensions.GetPostGameStartMediaPointId(productionId),
                            RelatedProductionId = productionId,
                            MatchTime = gmsEntity.DateTime.Value.Add(gameDuration),
                            Apply = new List<PrismaPolicyApply>() { new PrismaPolicyApply() { Policy = GetPostGamePolicy() } },
                            Effective = gmsEntity.DateTime.Value.AddMinutes(esniResourcesCreationOptions.NssMediaPointGameStartMatchTimeOffsetInMinutes),
                            Expires = gmsEntity.DateTime.Value.Add(gameDuration).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                            MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPostGameStartMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                        });

                        mediaPoints.Add(new PrismaMediaPoint
                        {
                            Id = EsniExtensions.GetPostGameEndMediaPointId(productionId),
                            MatchTime = gmsEntity.DateTime.Value.Add(gameDuration).AddMinutes(esniResourcesCreationOptions.NssMediaPointPostGameEndMatchTimeInMinutes),
                            Remove = new List<PrismaPolicyRemove>() { new PrismaPolicyRemove() { Policy = GetPostGamePolicy() } },
                            Effective = gmsEntity.DateTime.Value.Add(gameDuration).AddMinutes(esniResourcesCreationOptions.NssMediaPointPostGameEndMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                            Expires = gmsEntity.DateTime.Value.Add(gameDuration).AddMinutes(esniResourcesCreationOptions.NssMediaPointPostGameEndMatchTimeInMinutes).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                            RelatedProductionId = productionId,
                            MatchSignal = new PrismaPolicyMatchSignal() { Assert = new[] { esniResourcesCreationOptions.NssMediaPointPostGameEndMatchSignal }, Match = PrismaMatchSignalMatch.ALL },
                        });
                    }

                    var prismaMediaId = EsniExtensions.GetEsniMediaId(productionId);
                    var existingMedia = prismaInfo.MediasToUpsert.FirstOrDefault(media => media.Id == prismaMediaId);
                    if (existingMedia != null)
                    {
                        existingMedia.MediaPoint = existingMedia.MediaPoint.Concat(mediaPoints).ToList();
                    }
                    else
                    {
                        prismaInfo.MediasToUpsert = prismaInfo.MediasToUpsert.Append(new PrismaMedia
                        {
                            Id = prismaMediaId,
                            GmsMediaId = nssMedia.Id,
                            MediaPoint = mediaPoints,
                            TeamContext = nssMedia.TeamContext,
                        });
                    }
                }
            }
        }

        /// <summary>
        /// Gets all the Prisma Blackout Info.
        /// </summary>
        /// <param name="gmsEntity">The Game/Event GmsEntity.</param>
        /// <param name="esniResourcesCreationOptions">The ESNI Resources Creation Options.</param>
        /// <param name="nssPrimaryFeedKey">The NSSPrimaryFeed Key.</param>
        /// <param name="gameDuration">The Game Duration.</param>
        /// <param name="nssBlackoutKeys">The NSS Blackout Keys.</param>
        /// <returns>Returns the PrismaMediaInfo with the MediaPoints and the MediasToUpsert.</returns>
        private static PrismaMediaInfo GetPrismaBlackoutInfo(GmsEntity gmsEntity, EsniResourcesCreationOptions esniResourcesCreationOptions, string nssPrimaryFeedKey, TimeSpan gameDuration, string[] nssBlackoutKeys)
        {
            if (!gmsEntity.IncludesBlackoutData(nssBlackoutKeys))
            {
                return null;
            }

            // Creation of one Media and one MediaPoint per NSS Media
            var nssMedias = gmsEntity.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules);

            if (esniResourcesCreationOptions.MediaIdsToIgnore != null)
            {
                nssMedias = nssMedias.Where(x => !esniResourcesCreationOptions.MediaIdsToIgnore.Contains(x.Id));
            }

            var prismaInfo = new PrismaMediaInfo
            {
                MediasToUpsert = nssMedias.Select(nssMedia =>
                {
                    var productionId = gmsEntity.GetMediaName(gmsEntity, nssMedia);
                    return new PrismaMedia
                    {
                        Id = EsniExtensions.GetEsniMediaId(productionId),
                        GmsMediaId = nssMedia.Id,
                        MediaPoint = new List<PrismaMediaPoint>
                        {
                            new PrismaMediaPoint
                            {
                                Id = EsniExtensions.GetStartMediaPointId(productionId),
                                RelatedProductionId = productionId,
                                MatchTime = gmsEntity.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointMatchTimeOffset),
                                Apply = new List<PrismaPolicyApply>(),
                                Effective = gmsEntity.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = gmsEntity.DateTime.Value.Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                            },
                            new PrismaMediaPoint
                            {
                                Id = EsniExtensions.GetEndMediaPointId(productionId),
                                MatchTime = gmsEntity.DateTime.Value.Add(gameDuration),
                                Remove = new List<PrismaPolicyRemove>(),
                                Effective = gmsEntity.DateTime.Value.Add(gameDuration).Add(esniResourcesCreationOptions.MediaPointEffectiveTimeOffset),
                                Expires = gmsEntity.DateTime.Value.Add(gameDuration).Add(esniResourcesCreationOptions.MediaPointExpiresTimeOffset),
                                RelatedProductionId = productionId,
                            },
                        },
                        TeamContext = nssMedia.TeamContext,
                    };
                }).ToList(),
            };

            /*
            This section will exempt specified NSS medias and NonNss medias from receiving each kind of blackout, as per definition in the Get-AppSettings.ps1
            */

            var gmsEntityContainsMediaThatExemptWorldBlackouts = esniResourcesCreationOptions.MediaIdsWithoutWorldBlackouts != null && gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.MediaIdsWithoutWorldBlackouts.Contains(x.Id));
            var gmsEntityContainsMediaThatDontCauseWorldBlackouts = esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts != null &&
                                                                    gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.NonNssMediaIdsThatDoNotCreateWorldBlackouts.Contains(x.Id));
            var gmsEntityContainsMediaThatOverwriteDontCauseWorldBlackouts = gmsEntityContainsMediaThatDontCauseWorldBlackouts &&
                                                                             gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.NonNssMediasIdsThatOverwriteDoNotCreateWorldBlackouts.Contains(x.Id));
            var dontCreateWorldBlackouts = gmsEntityContainsMediaThatDontCauseWorldBlackouts &&
                                           !gmsEntityContainsMediaThatOverwriteDontCauseWorldBlackouts;

            // Adding World (CA) Policy, ViewingPolicy and Audience
            if (!(dontCreateWorldBlackouts || gmsEntityContainsMediaThatExemptWorldBlackouts) && gmsEntity.Media.Any(x => x.IsMediaTypeTV && x.IsRegionalDistribution && x.IsRegionCanada && x.IsActiveMediaWithActiveSchedules))
            {
                prismaInfo = prismaInfo.GetRegionalPoliciesCA();
                prismaInfo.GetWorldPoliciesCA(gmsEntity, esniResourcesCreationOptions);
            }

            var gmsEntityContainsMediaThatExemptRegionalBlackouts = esniResourcesCreationOptions.MediaIdsWithoutRegionalBlackouts != null && gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.MediaIdsWithoutRegionalBlackouts.Contains(x.Id));

            // Adding Regional (US) Policy, ViewingPolicy and Audience
            if ((!gmsEntityContainsMediaThatExemptRegionalBlackouts) && gmsEntity.Media.Any(x => x.IsMediaTypeTV && x.IsRegionalDistribution && x.IsRegionUS && x.IsActiveMediaWithActiveSchedules))
            {
                prismaInfo = prismaInfo.GetRegionalPoliciesUS(esniResourcesCreationOptions);
            }

            var gmsEntityContainsMediaThatExemptLocalBlackouts = esniResourcesCreationOptions.MediaIdsWithoutLocalBlackouts != null && gmsEntity.Media.Any(x => x.IsActiveMediaWithActiveSchedules && esniResourcesCreationOptions.MediaIdsWithoutLocalBlackouts.Contains(x.Id));

            if (!gmsEntityContainsMediaThatExemptLocalBlackouts)
            {
                // Adding Local (Home and Away team) Policy, ViewingPolicy and Audience
                if (gmsEntity is GmsGame gmsGame)
                {
                    prismaInfo.SetLocalPoliciesFor(esniResourcesCreationOptions, gmsGame);
                }
                else if (gmsEntity is GmsEvent gmsEvent)
                {
                    prismaInfo.SetLocalPoliciesFor(esniResourcesCreationOptions, gmsEvent);
                }
            }

            // Removing MediasToUpsert not required.
            prismaInfo.MediasToUpsert = prismaInfo.MediasToUpsert.Where(media => media.MediaPoint.First().Apply.Any());

            prismaInfo.AddEventLevelApplyandRemoveDefaults();

            // Adding game level PrismaMedia only if there are blackouts
            if (prismaInfo.MediasToUpsert.Any())
            {
                AddGameLevelPrismaMediaToPrismaMediaInfo(gmsEntity, nssPrimaryFeedKey, prismaInfo);
            }

            return prismaInfo;
        }

        /// <summary>
        /// Gets the game-package parent packages.
        /// </summary>
        /// <param name="gmsGame">The <see cref="GmsGame"/> to look for parent packages.</param>
        /// <param name="tvpEventCreationOptions">The <see cref="TvpEventCreationOptions"/> to get the parent packages.</param>
        /// <returns>The parent packages.</returns>
        private static IEnumerable<string> GetGamePackageParentPackages(GmsGame gmsGame, TvpEventCreationOptions tvpEventCreationOptions)
        {
            var parentPackagesConcatenated = gmsGame.GetValueFromLiveEventKeyValuePairs(tvpEventCreationOptions.GamePackageParentPackagesKey);
            var parentPackages = parentPackagesConcatenated?.SplitAndTrim(tvpEventCreationOptions.ParentPackagesSeparator);

            return parentPackages ?? Enumerable.Empty<string>();
        }

        /// <summary>
        /// Gets the value from the <see cref="GmsEntity.KeyValuePairs"/> for the key specified.
        /// </summary>
        /// <param name="gmsEntity">The <see cref="GmsEntity"/>.</param>
        /// <param name="key">The key to return the value.</param>
        /// <returns>The value or null if not found.</returns>
        private static string GetValueFromLiveEventKeyValuePairs(this GmsEntity gmsEntity, string key)
        {
            return gmsEntity.KeyValuePairs?.FirstOrDefault(x => x.Key == key)?.Value;
        }

        /// <summary>
        /// Gets the custom offset for the media if any.
        /// </summary>
        /// <param name="schedule">The <see cref="Schedule"/> to look for the offset key.</param>
        /// <param name="mediaInfo">The <see cref="MediaInfo"/> to look for the offset key alternatively.</param>
        /// <param name="customOffsetKvpKey">The configurable key for the custom offset KVP.</param>
        /// <returns>The custom offset.</returns>
        private static TimeSpan? GetCustomOffset(Schedule schedule, MediaInfo mediaInfo, string customOffsetKvpKey)
        {
            var offset = schedule.GetValueFromScheduleOrMediaKeyValuePairs(customOffsetKvpKey, mediaInfo);

            if (int.TryParse(offset, NumberStyles.AllowLeadingSign, CultureInfo.InvariantCulture, out var offsetInt))
            {
                return TimeSpan.FromMinutes(offsetInt);
            }

            return null;
        }

        /// <summary>
        /// Gets the the custom offsets for the media.
        /// </summary>
        /// <param name="schedule">The <see cref="Schedule"/> to look for the offset keys.</param>
        /// <param name="mediaInfo">The <see cref="MediaInfo"/> to look for the offset key alternatively.</param>
        /// <param name="customWorkflowOffsetOptions"><see cref="CustomWorkflowOffsetOptions"/>.</param>
        /// <returns>The custom offsets.</returns>
        private static IDictionary<string, TimeSpan?> GetCustomOffsets(Schedule schedule, MediaInfo mediaInfo, CustomWorkflowOffsetOptions customWorkflowOffsetOptions)
        {
            var customOffsets = new Dictionary<string, TimeSpan?>();

            foreach (var customWorkflowOffset in customWorkflowOffsetOptions.CustomWorkflowOffsets)
            {
                var customOffset = GetCustomOffset(schedule, mediaInfo, customWorkflowOffset.KvpKey);
                var isValidWorkflowId = NbaWorkflowIds.IsValidWorkflow(customWorkflowOffset.NbaWorkflowId);
                if (customOffset != null && isValidWorkflowId)
                {
                    customOffsets.Add(customWorkflowOffset.NbaWorkflowId, customOffset);
                }
            }

            return customOffsets;
        }

        /// <summary>
        /// Converts the ScheduleCode to TvpEventStatus.
        /// </summary>
        /// <param name="scheduleCode">The schedule code.</param>
        /// <returns>The TvpEventStatus.</returns>
        /// <exception cref="NotImplementedException">If there is no conversion defined.</exception>
        private static TvpEventStatus ConvertScheduleCodeToTvpEventStatus(string scheduleCode)
        {
            return scheduleCode switch
            {
                ScheduleCode.Ok => TvpEventStatus.Scheduled,
                ScheduleCode.Postponed => TvpEventStatus.Postponed,
                ScheduleCode.ToBeDetermined => TvpEventStatus.ToBeDetermined,
                _ => throw new NotSupportedException($"The {nameof(ScheduleCode)} {scheduleCode} doesn't have a conversion to {nameof(TvpEventStatus)} defined"),
            };
        }

        /// <summary>
        /// Gets the season identifier.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <returns>The season identifier.</returns>
        private static string GetSeasonId(GmsEntity gmsEntity)
        {
            string seasonId = null;

            if (gmsEntity.TournamentSeasonId != null)
            {
                var tournamentSeasonInfo = gmsEntity.TournamentSeasonId.Split('-');

                if (tournamentSeasonInfo.Length > 1)
                {
                    seasonId = tournamentSeasonInfo[1].Length == TournamentSeasonInfoMaxLength ? gmsEntity.TournamentSeasonId : string.Concat(tournamentSeasonInfo[0], "-", tournamentSeasonInfo[1].Substring(TournamentSeasonInfoMaxLength));
                }
                else
                {
                    seasonId = tournamentSeasonInfo.First();
                }
            }

            return seasonId;
        }

        /// <summary>
        /// Determines whether [is media identifier present in no vod media list] [the specified media].
        /// </summary>
        /// <param name="mediaId">The media id.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <returns>
        ///   <c>false</c> if [is media identifier present in no vod media list] [the specified media]; otherwise, <c>GmsGamesLive2VodDefaultValue</c>.
        /// </returns>
        private static bool IsMediaIdPresentInNoVodMediaList(long mediaId, TvpEventCreationOptions tvpEventCreationOptions)
        {
            bool isMediaIdPresentInNoVodMediaList = tvpEventCreationOptions.GmsGamesLive2VodDefaultValue;

            if (!string.IsNullOrEmpty(tvpEventCreationOptions.ListOfNoVodMedias))
            {
                var listOfNoVodMedias = tvpEventCreationOptions.ListOfNoVodMedias.Split(',');
                foreach (var item in listOfNoVodMedias)
                {
                    if (!string.IsNullOrEmpty(item) && long.TryParse(item, out long parsedMediaId) && parsedMediaId.Equals(mediaId))
                    {
                        return false;
                    }
                }
            }

            return isMediaIdPresentInNoVodMediaList;
        }

        /// <summary>
        /// Gets the tvp event location info.
        /// </summary>
        /// <param name="location">The location.</param>
        /// <returns>TvpEventLocationInfo.</returns>
        private static TvpEventLocationInfo GetEventLocation([NotNull] Location location)
        {
            location.Required(nameof(location));
            return new TvpEventLocationInfo
            {
                LocationId = location.LocationId,
                Name = location.Name,
                City = location.City,
                Active = location.Active,
                Capacity = location.Capacity,
                CountryCode = location.CountryCode,
                LocationType = location.LocationType,
                Postal = location.Postal,
                StateOrProvince = location.StateOrProvince,
                Street = location.Street,
                TimeZone = location.TimeZone,
                LastUpdated = location.LastUpdated,
            };
        }

        /// <summary>
        /// Checks whether the media is audio only.
        /// </summary>
        /// <param name="media">The media.</param>
        /// <returns>a bool.</returns>
        private static bool IsMediaAudioOnly([NotNull] MediaInfo media)
        {
            if (!string.IsNullOrEmpty(media.ScoppedResolution))
            {
                return media.ScoppedResolution.StartsWith("Audio", StringComparison.InvariantCultureIgnoreCase);
            }
            else
            {
                return false;
            }
        }

        /// <summary>
        /// Gets the playback restrictions.
        /// </summary>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <param name="tvpEventCreationOptions">The <see cref="TvpEventCreationOptions"/>.</param>
        /// <returns>Collection of playback restrictions.</returns>
        private static Collection<string> GetPlaybackRestrictions([NotNull] MediaInfo media, [NotNull] TvpEventCreationOptions tvpEventCreationOptions)
        {
            var isRadioMedia = IsMediaAudioOnly(media);
            var playbackRestrictions = new Collection<string>();
            if (isRadioMedia)
            {
                playbackRestrictions.Add(tvpEventCreationOptions.AdInsertionPlaybackRestriction);
            }

            return playbackRestrictions;
        }

        /// <summary>
        /// Gets whether the media has SCTE35 conditioning or not, based on the KVP coming from GMS.
        /// </summary>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <param name="schedule">The <see cref="Schedule"/>.</param>
        /// <param name="tvpEventCreationOptions">The <see cref="TvpEventCreationOptions"/>.</param>
        /// <returns>True if the media has SCTE35, otherwise false.</returns>
        private static bool GetHasInBandScte35([NotNull] MediaInfo media, [NotNull] Schedule schedule, [NotNull] TvpEventCreationOptions tvpEventCreationOptions)
        {
            var hasInBandScte35 = tvpEventCreationOptions.HasInBandScte35DefaultValue;

            var hasInBandScte35KVPValue = GetValueFromScheduleOrMediaKeyValuePairs(schedule, tvpEventCreationOptions.HasInBandScte35Key, media);

            if (bool.TryParse(hasInBandScte35KVPValue, out bool kvpValueAsBool))
            {
                hasInBandScte35 = kvpValueAsBool;
            }

            return hasInBandScte35;
        }

        /// <summary>
        /// Gets whether the media has the associated experience value or not, based on the KVP coming from GMS.
        /// </summary>
        /// <param name="media">The <see cref="MediaInfo"/>.</param>
        /// <param name="schedule">The <see cref="Schedule"/>.</param>
        /// <param name="esniResourcesCreationOptions">The <see cref="TvpEventCreationOptions"/>.</param>
        /// <param name="nssAssociatedExperienceValue">The associated experience value.</param>
        /// <returns>True if the media has the associated experience, otherwise false.</returns>
        private static bool GetHasAssociatedExperience([NotNull] MediaInfo media, [NotNull] Schedule schedule, [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions, string nssAssociatedExperienceValue)
        {
            var nssAssociatedExperiencesValue = schedule.GetValueFromScheduleOrMediaKeyValuePairs(esniResourcesCreationOptions.NssAssociatedExperiencesKey, media);
            if (string.IsNullOrWhiteSpace(nssAssociatedExperiencesValue))
            {
                return false;
            }

            return nssAssociatedExperiencesValue
                .SplitAndTrim(esniResourcesCreationOptions.NssAssociatedExperiencesSeparator)
                .Any(x => x.Equals(nssAssociatedExperienceValue, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// Gets the tvp production creation info.
        /// </summary>
        /// <param name="gmsEntity">The GMS Game.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <returns>The production creation information.</returns>
        private static IList<TvpProductionCreationInfo> GetTvpProductionCreationInfo([NotNull] this GmsEntity gmsEntity, [NotNull] TvpEventCreationOptions tvpEventCreationOptions)
        {
            gmsEntity.Required(nameof(gmsEntity));
            tvpEventCreationOptions.Required(nameof(tvpEventCreationOptions));
            var tvpProductionCreationInfo = new List<TvpProductionCreationInfo>();

            if (gmsEntity.HasActiveNssMediasWithActiveSchedules)
            {
                foreach (var media in gmsEntity.Media?.Where(m => m.IsActiveNssMediaWithActiveSchedules))
                {
                    var preferredSchedule = GetPreferredSchedule(media);

                    var isThirdPartyProduction =
                        preferredSchedule?.GetValueFromScheduleOrMediaKeyValuePairs(
                            tvpEventCreationOptions.IsNSSThirdPartyKey,
                            media);

                    var tvpCreationInfo = new TvpProductionCreationInfo
                    {
                        ProductionExternalId = gmsEntity.GetChannelId(media),
                        ProductionName = GetProductionDisplayName(media, preferredSchedule, tvpEventCreationOptions),
                        ProductionRole = preferredSchedule?.TeamContext,
                        ProductionLiveToOnDemand = IsMediaIdPresentInNoVodMediaList(media.Id, tvpEventCreationOptions),
                        ProductionQualityLevel = tvpEventCreationOptions.ProductionQualityLevel,
                        ProductionLabels = GetProductionLabels(gmsEntity.IsEvent, media, preferredSchedule, tvpEventCreationOptions),
                        GeoRestrictionPolicies = GetGeoRestrictionPolicies(media, preferredSchedule, tvpEventCreationOptions),
                        AudioOnly = IsMediaAudioOnly(media),
                        PlaybackRestrictions = GetPlaybackRestrictions(media, tvpEventCreationOptions),
                        HasInBandScte35 = GetHasInBandScte35(media, preferredSchedule, tvpEventCreationOptions),
                        PreGameStartOffSetMins = GetPreGameStartOffset(preferredSchedule),
                        IsLowLatencyEnabled = media.Resolution != null && media.Resolution.EndsWith("_LL", StringComparison.OrdinalIgnoreCase),
                    };

                    if (isThirdPartyProduction != null)
                    {
                        tvpCreationInfo.ThirdPartyPlaybackInfo = CreateThirdPartyPlaybackInfo(media.ThirdPartyStreamUrls, tvpEventCreationOptions.ThirdPartyDRMs);
                    }

                    tvpProductionCreationInfo.Add(tvpCreationInfo);
                }
            }
            else
            {
                // If the game/event does not include any NSS media, we create a dummy production in TVP
                tvpProductionCreationInfo.Add(new TvpProductionCreationInfo
                {
                    ProductionExternalId = gmsEntity.GetDummyMediaName(),
                    ProductionName = gmsEntity.GetDummyMediaName(),
                    ProductionLiveToOnDemand = false,
                    ProductionQualityLevel = tvpEventCreationOptions.ProductionQualityLevel,
                    ProductionLabels = new Collection<Label>(),
                    GeoRestrictionPolicies = new Collection<GeoRestrictionPolicy>(),
                    AudioOnly = false,
                    PlaybackRestrictions = new Collection<string>(),
                    HasInBandScte35 = false,
                    PreGameStartOffSetMins = null,
                });
            }

            return tvpProductionCreationInfo.ToList();
        }

        /// <summary>
        /// Creates the third party playback info.
        /// </summary>
        /// <param name="thirdPartyStreamUrls">the thirdPartyStreamUrls list.</param>
        /// <param name="drms">Third Party DRMs.</param>
        /// <returns>endoced value.</returns>
        private static string CreateThirdPartyPlaybackInfo(ICollection<ThirdPartyStreamUrl> thirdPartyStreamUrls, string drms)
        {
            var quortexDRMs = drms.Split(',').ToArray();
            var thirdPartyPlaybackInfo = new Dictionary<string, object>();
            foreach (var thirdPartyStreamUrl in thirdPartyStreamUrls)
            {
                var base_url = thirdPartyStreamUrl?.Url?.ToString();
                var cdn = new Dictionary<string, ThirdPartyPlaybackInfo[]>()
                {
                    {
                        "cdn", new ThirdPartyPlaybackInfo[]
                        {
                            new ThirdPartyPlaybackInfo()
                            {
                                name = "NBA_3PP_CDNProfile",
                                retrieval_type = "static",
                                base_uri = base_url,
                                priority = 0,
                                threshold = 0,
                            },
                        }
                    },
                };

                var cdns = new Dictionary<string, object>()
                {
                    { "manifest_uri", ManifestUrl },
                    { "cdns", cdn },
                };
                foreach (var drm in quortexDRMs)
                {
                    thirdPartyPlaybackInfo.Add(drm, cdns);
                }
            }

            return DictionaryToBase64Encoder(thirdPartyPlaybackInfo);
        }

        /// <summary>
        /// Gets the PreGame start offset in minutes from NSS-Production-Manifest-Offset KVP.
        /// </summary>
        /// <param name="schedule">Scheduleinfo.</param>
        /// <returns>PreGameOffset in minutes.</returns>
        private static string GetPreGameStartOffset(Schedule schedule)
        {
            var preGameOffset = schedule.Operations.KeyValuePairs?.Where(kvp => kvp.Key.Equals("NSS-Production-Manifest-Offset", StringComparison.OrdinalIgnoreCase));
            if (int.TryParse(preGameOffset?.FirstOrDefault()?.Value, out int value))
            {
                return $"{Math.Abs(value)}";
            }

            return null;
        }

        /// <summary>
        /// Gets the geo location policies.
        /// </summary>
        /// <param name="media">The media.</param>
        /// <param name="schedule">The schedule.</param>
        /// <param name="tvpEventCreationOptions">The TVP event creation options.</param>
        /// <returns>Policies.</returns>
        private static Collection<GeoRestrictionPolicy> GetGeoRestrictionPolicies([NotNull] MediaInfo media, [NotNull] Schedule schedule, [NotNull] TvpEventCreationOptions tvpEventCreationOptions)
        {
            media.Required(nameof(media));
            schedule.Required(nameof(schedule));
            tvpEventCreationOptions.Required(nameof(tvpEventCreationOptions));
            var result = new Collection<GeoRestrictionPolicy>();
            var allowed = GetValueFromScheduleOrMediaKeyValuePairs(schedule, tvpEventCreationOptions.NssGeoAllow, media);
            if (!string.IsNullOrWhiteSpace(allowed))
            {
                var allowedPolicies = allowed.SplitAndTrim(tvpEventCreationOptions.GeoPolicyDeliminator);
                foreach (var policy in allowedPolicies)
                {
                    result.Add(new GeoRestrictionPolicy
                    {
                        ExternalId = GmsGameTransformationConstants.GeoRestriction + policy + GmsGameTransformationConstants.GeoRestrictionAllowed,
                        Name = GmsGameTransformationConstants.GeoRestriction + policy + GmsGameTransformationConstants.GeoRestrictionAllowed,
                        Mode = Domain.Enums.RestrictionMode.Allow.ToEnumString(),
                        Locations = new Collection<string> { policy },
                    });
                }
            }

            var denied = GetValueFromScheduleOrMediaKeyValuePairs(schedule, tvpEventCreationOptions.NssGeoDeny, media);
            if (!string.IsNullOrWhiteSpace(denied))
            {
                var allowedPolicies = denied.SplitAndTrim(tvpEventCreationOptions.GeoPolicyDeliminator);
                foreach (var policy in allowedPolicies)
                {
                    result.Add(new GeoRestrictionPolicy
                    {
                        ExternalId = GmsGameTransformationConstants.GeoRestriction + policy + GmsGameTransformationConstants.GeoRestrictionDenied,
                        Name = GmsGameTransformationConstants.GeoRestriction + policy + GmsGameTransformationConstants.GeoRestrictionDenied,
                        Mode = Domain.Enums.RestrictionMode.Block.ToEnumString(),
                        Locations = new Collection<string> { policy },
                    });
                }
            }

            return result;
        }

        /// <summary>
        /// Get production display name from  media].
        /// </summary>
        /// <param name="media">The media.</param>
        /// <param name="schedule">The schedule.</param>
        /// <param name="tvpEventCreationOptions">The Options.</param>
        /// <returns>
        /// <c>string</c> .
        /// </returns>
        private static string GetProductionDisplayName([NotNull] MediaInfo media, [NotNull] Schedule schedule, [NotNull] TvpEventCreationOptions tvpEventCreationOptions)
        {
            media.Required(nameof(media));
            schedule.Required(nameof(schedule));
            tvpEventCreationOptions.Required(nameof(tvpEventCreationOptions));
            var displayName = GetValueFromScheduleOrMediaKeyValuePairs(schedule, tvpEventCreationOptions.DisplayNameKey, media) ?? media.DisplayName;

            return displayName;
        }

        /// <summary>
        /// Gets the production labels.
        /// </summary>
        /// <param name="isGmsEvent">Whether the GmsEntity is an event or not.</param>
        /// <param name="media">The Media.</param>
        /// <param name="schedule">The schedule.</param>
        /// <param name="tvpEventCreationOptions">The Options.</param>
        /// <returns>A collection of labels.</returns>
        private static Collection<Label> GetProductionLabels(bool isGmsEvent, [NotNull] MediaInfo media, [NotNull] Schedule schedule, [NotNull] TvpEventCreationOptions tvpEventCreationOptions)
        {
            media.Required(nameof(media));
            schedule.Required(nameof(schedule));
            tvpEventCreationOptions.Required(nameof(tvpEventCreationOptions));

            var labels = new Collection<Label>();
            var language = schedule.Language ?? media.DefaultLanguage;

            if (!string.IsNullOrWhiteSpace(language))
            {
                labels.Add(new Label(tvpEventCreationOptions.LanguageKey, language.Replace(" ", string.Empty, StringComparison.InvariantCulture)));
            }

            if (tvpEventCreationOptions.MediaScheduleKVPToLabelMapping != null)
            {
                foreach (var keyValuePairToLabelMapping in tvpEventCreationOptions.MediaScheduleKVPToLabelMapping)
                {
                    var labelValue = GetValueFromScheduleOrMediaKeyValuePairs(schedule, keyValuePairToLabelMapping.KeyValuePairKey, media);

                    if (!string.IsNullOrWhiteSpace(labelValue))
                    {
                        // We remove blanks because TVP doesn't accept them as part of a label value
                        labels.Add(new Label(keyValuePairToLabelMapping.TvpProductionLabelName, labelValue.Replace(" ", string.Empty, StringComparison.InvariantCulture)));
                    }
                }
            }

            if (!isGmsEvent && media.Name == tvpEventCreationOptions.NbaTvMediaName)
            {
                labels.Add(new Label(tvpEventCreationOptions.ExemptNationalBlackoutPolicyLabelKey, tvpEventCreationOptions.ExemptNationalBlackoutPolicyLabelValue));
                labels.Add(new Label(tvpEventCreationOptions.LiftBlackoutSubscriptionLabelKey, tvpEventCreationOptions.LiftBlackoutSubscriptionLabelValue));
            }

            return labels;
        }

        /// <summary>
        /// Gets the TVP event team creation information.
        /// </summary>
        /// <param name="team">The team.</param>
        /// <param name="isHome">if set to <c>true</c> [is home].</param>
        /// <returns>TvpEventTeamCreationInfo.</returns>
        private static TvpEventTeamCreationInfo GetTvpEventTeamCreationInfo([NotNull] this Team team, bool isHome)
        {
            team.Required(nameof(team));
            return new TvpEventTeamCreationInfo
            {
                ExternalId = team.Id.ToString(CultureInfo.InvariantCulture),
                Name = team.Name,
                CallLetters = team.Abbr,
                Description = $"Teams entity for {team.Name}",
                Conference = team.Conference,
                Division = team.Division,
                RoleType = isHome ? "home" : "away",
            };
        }

        /// <summary>
        /// Given a Prisma Media Info, it returns an instance of <see cref="PrismaMediaInfo"/> with the details of Viewing Policies at World Level (Canada).
        /// </summary>
        /// <param name="prismaInfo">The Prisma Media Info.</param>
        /// <returns>An instance of <see cref="PrismaMediaInfo"/> with the details added related to the Canada Region.</returns>
        private static PrismaMediaInfo GetRegionalPoliciesCA([NotNull] this PrismaMediaInfo prismaInfo)
        {
            prismaInfo.Required(nameof(prismaInfo));
            foreach (var media in prismaInfo.MediasToUpsert)
            {
                var mediaPoint = media.MediaPoint.First();
                var prismaApply = new PrismaPolicyApply
                {
                    Priority = 2,
                    Policy = new PrismaPolicy
                    {
                        Id = EsniExtensions.GetEsniWorldPolicyId(mediaPoint.RelatedProductionId.ToLowerInvariant()),
                        ViewingPolicy = new List<PrismaViewingPolicy>
                        {
                            new PrismaViewingPolicy
                            {
                                Id = $"/NBA/viewingpolicy/{EsniMediaNames.CA}",
                                ActionContent = EsniMediaNames.BlackoutActionContent,
                                Audience = new PrismaAudience
                                {
                                    Id = $"/NBA/audience/{EsniMediaNames.CA}",
                                    Match = PrismaAudienceMatch.ANY,
                                    ISO3166CountryCodes = new[] { EsniMediaNames.CA.ToUpperInvariant() },
                                },
                            },
                        },
                    },
                };
                mediaPoint.Apply.Add(prismaApply);
            }

            return prismaInfo;
        }

        /// <summary>
        /// Given a Prisma Media Info, it returns an instance of <see cref="PrismaMediaInfo"/> with Hybrid implementation of blackouts.
        /// </summary>
        /// <param name="prismaInfo">The Prisma Media Info.</param>
        /// <param name="gmsEntity">The gms entity.</param>
        /// <param name="esniResourcesCreationOptions">/// Configurable options for ESNI Resources provisioning in PRISMA.</param>
        /// <returns>An instance of <see cref="PrismaMediaInfo"/> with the details added related to the Canada Region.</returns>
        private static PrismaMediaInfo GetWorldPoliciesCA(
            [NotNull] this PrismaMediaInfo prismaInfo,
            [NotNull] GmsEntity gmsEntity,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions)
        {
            prismaInfo.Required(nameof(prismaInfo));
            gmsEntity.Required(nameof(gmsEntity));
            esniResourcesCreationOptions.Required(nameof(esniResourcesCreationOptions));
            var applyMatchSignal = new PrismaPolicyMatchSignal
            {
                Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=16]" },
                Match = PrismaMatchSignalMatch.ANY,
            };

            var removeMatchSignal = new PrismaPolicyMatchSignal
            {
                Assert = new[] { "/SpliceInfoSection/SegmentationDescriptor[@segmentationTypeId=17]" },
                Match = PrismaMatchSignalMatch.ANY,
            };

            foreach (var media in prismaInfo.MediasToUpsert)
            {
                var startMediaPoint = media.MediaPoint.First();
                startMediaPoint.MatchSignal = applyMatchSignal;

                var endMediaPoint = media.MediaPoint.ElementAt(1);
                endMediaPoint.MatchSignal = removeMatchSignal;
                endMediaPoint.Remove.Add(new PrismaPolicyRemove
                {
                    Policy = new PrismaPolicy
                    {
                        XlinkHref = EsniExtensions.GetEsniWorldPolicyId(startMediaPoint.RelatedProductionId.ToLowerInvariant()),
                    },
                });
            }

            return prismaInfo;
        }

        /// <summary>
        /// Given a Prisma Media Info, it returns an instance of <see cref="PrismaMediaInfo"/> with the details of Viewing Policies at World Level (Canada).
        /// </summary>
        /// <param name="prismaInfo">The Prisma Media Info.</param>
        /// <param name="esniResourcesCreationOptions">/// Configurable options for ESNI Resources provisioning in PRISMA.</param>
        /// <returns>An instance of <see cref="PrismaMediaInfo"/> with the details added related to the Canada Region.</returns>
        private static PrismaMediaInfo GetRegionalPoliciesUS(
            [NotNull] this PrismaMediaInfo prismaInfo,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions
            )
        {
            prismaInfo.Required(nameof(prismaInfo));
            esniResourcesCreationOptions.Required(nameof(esniResourcesCreationOptions));

            foreach (var media in prismaInfo.MediasToUpsert)
            {
                var mediaPoint = media.MediaPoint.First();
                var summerLeagueEndDateTime = new DateTimeOffset(new DateTime(2025, 07, 20));
                int calculatedDuration = (int)(summerLeagueEndDateTime - mediaPoint.MatchTime).TotalHours;
                var regionalPolicyDuration =
                    (mediaPoint.RelatedProductionId.StartsWith("g13", true, CultureInfo.InvariantCulture) ||
                    mediaPoint.RelatedProductionId.StartsWith("g15", true, CultureInfo.InvariantCulture) ||
                    mediaPoint.RelatedProductionId.StartsWith("g16", true, CultureInfo.InvariantCulture)) &&
                    calculatedDuration > 0
                    ? $"PT{calculatedDuration}H"
                    : esniResourcesCreationOptions.RegionalPolicyDuration;
                var prismaApply = new PrismaPolicyApply
                {
                    Priority = 1,
                    Duration = regionalPolicyDuration,
                    Policy = new PrismaPolicy
                    {
                        Id = EsniExtensions.GetEsniRegionalPolicyId(mediaPoint.RelatedProductionId.ToLowerInvariant()),
                        ViewingPolicy = new List<PrismaViewingPolicy>
                            {
                                new PrismaViewingPolicy
                                {
                                    Id = $"/NBA/viewingpolicy/{EsniMediaNames.US}",
                                    ActionContent = EsniMediaNames.BlackoutActionContent,
                                    Audience = new PrismaAudience
                                    {
                                        Id = $"/NBA/audience/{EsniMediaNames.US}",
                                        Match = PrismaAudienceMatch.ANY,
                                        ISO3166CountryCodes = new[] { EsniMediaNames.US.ToUpperInvariant(), EsniMediaNames.PR.ToUpperInvariant(), EsniMediaNames.VI.ToUpperInvariant(), EsniMediaNames.GU.ToUpperInvariant() },
                                    },
                                },
                            },
                    },
                };
                mediaPoint.Apply.Add(prismaApply);
            }
            return prismaInfo;
        }
 
        /// <summary>
        /// Set local policies to <see cref="PrismaMediaInfo"/> for <see cref="GmsGame"/> with the details of Viewing Policies at World Level (Canada).
        /// </summary>
        /// <param name="prismaInfo">The Prisma Media Info.</param>
        /// <param name="esniResourcesCreationOptions">Configurable options for ESNI Resources provisioning in PRISMA.</param>
        /// <param name="gmsGame">Gms Game.</param>
        private static void SetLocalPoliciesFor(
            [NotNull] this PrismaMediaInfo prismaInfo,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions,
            GmsGame gmsGame)
        {
            prismaInfo.Required(nameof(prismaInfo));
            esniResourcesCreationOptions.Required(nameof(esniResourcesCreationOptions));
            gmsGame.Required(nameof(gmsGame));

            var mediasWithTypeTvRnsOrOtaDistributionHomeOrAwayTeamContext = gmsGame.Media.Where(x =>
                x.IsMediaTypeTV &&
                x.IsRSNOrOTADistribution &&
                x.IsHomeOrAwayTeamContext &&
                x.IsRegionUS &&
                x.IsActiveMediaWithActiveSchedules);
            if (!mediasWithTypeTvRnsOrOtaDistributionHomeOrAwayTeamContext.Any())
            {
                return;
            }

            var audiences = mediasWithTypeTvRnsOrOtaDistributionHomeOrAwayTeamContext
                .Select(x => new
                {
                    TeamTricode = x.IsHomeTeamContext ?
                        gmsGame.HomeTeam.Abbr.ToLowerInvariant() :
                        gmsGame.AwayTeam.Abbr.ToLowerInvariant(),
                    MarketCode = x.Distribution.Name,
                })
                .Select(x =>
                {
                    return EsniMediaNames.DistributionOTA.EqualsIgnoreCase(x.MarketCode) ?
                        EsniExtensions.GetOtaLocalAudienceHref(x.TeamTricode) :
                        EsniExtensions.GetRsnLocalAudienceHref(x.TeamTricode);
                });

            foreach (var media in prismaInfo.MediasToUpsert)
            {
                SetLocalPolicyApplyWithAudiences(media, audiences, esniResourcesCreationOptions.LocalPolicyDuration);
            }
        }

        /// <summary>
        /// Set local policies to <see cref="PrismaMediaInfo"/> for <see cref="GmsEvent"/>.
        /// </summary>
        /// <param name="prismaInfo">The <see cref="PrismaMediaInfo"/>.</param>
        /// <param name="esniResourcesCreationOptions">The <see cref="EsniResourcesCreationOptions"/>.</param>
        /// <param name="gmsEvent">The <see cref="GmsEvent"/>.</param>
        private static void SetLocalPoliciesFor(
            [NotNull] this PrismaMediaInfo prismaInfo,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions,
            GmsEvent gmsEvent)
        {
            prismaInfo.Required(nameof(prismaInfo));
            esniResourcesCreationOptions.Required(nameof(esniResourcesCreationOptions));
            gmsEvent.Required(nameof(gmsEvent));

            var audiences = new List<string>();
            var nssMedias = gmsEvent.Media.Where(x => x.IsActiveNssMediaWithActiveSchedules);

            nssMedias.ForEach(media =>
            {
                var preferredSchedule = media.GetPreferredSchedule();

                var nssBlackoutTeamRsnValue = preferredSchedule.GetValueFromScheduleOrMediaKeyValuePairs(esniResourcesCreationOptions.NssBlackoutTeamRsnKey, media);
                if (!string.IsNullOrWhiteSpace(nssBlackoutTeamRsnValue))
                {
                    var rsnAudiences = nssBlackoutTeamRsnValue
                        .SplitAndTrim(esniResourcesCreationOptions.NssBlackoutTeamSeparator)
                        .Select(x => EsniExtensions.GetRsnLocalAudienceHref(x.ToLowerInvariant()));
                    audiences.AddRange(rsnAudiences);
                }

                var nssBlackoutTeamOtaValue = preferredSchedule.GetValueFromScheduleOrMediaKeyValuePairs(esniResourcesCreationOptions.NssBlackoutTeamOtaKey, media);
                if (!string.IsNullOrWhiteSpace(nssBlackoutTeamOtaValue))
                {
                    var otaAudiences = nssBlackoutTeamOtaValue
                        .SplitAndTrim(esniResourcesCreationOptions.NssBlackoutTeamSeparator)
                        .Select(x => EsniExtensions.GetOtaLocalAudienceHref(x.ToLowerInvariant()));
                    audiences.AddRange(otaAudiences);
                }
            });

            if (audiences.Any())
            {
                foreach (var media in prismaInfo.MediasToUpsert)
                {
                    SetLocalPolicyApplyWithAudiences(media, audiences.Distinct(), esniResourcesCreationOptions.LocalPolicyDuration);
                }
            }
        }

        /// <summary>
        /// Set the local policy apply with the audiences to PRISMA start media point.
        /// </summary>
        /// <param name="media">The <see cref="PrismaMedia"/>.</param>
        /// <param name="audiences">The audiences.</param>
        /// <param name="localPolicyDuration">The local policy duration.</param>
        private static void SetLocalPolicyApplyWithAudiences(PrismaMedia media, IEnumerable<string> audiences, string localPolicyDuration)
        {
            var startMediaPoint = media.MediaPoint.First(x => x.Id == EsniExtensions.GetStartMediaPointId(x.RelatedProductionId));
            var productionId = startMediaPoint.RelatedProductionId.ToLowerInvariant();

            var prismaApply = new PrismaPolicyApply
            {
                Priority = 0,
                Duration = localPolicyDuration,
                Policy = new PrismaPolicy
                {
                    Id = EsniExtensions.GetEsniLocalPolicyId(productionId),
                    ViewingPolicy = new List<PrismaViewingPolicy>
                        {
                            new PrismaViewingPolicy
                            {
                                Id = EsniExtensions.GetEsniLocalViewingPolicyId(productionId),
                                ActionContent = EsniMediaNames.BlackoutActionContent,
                                Audience = new PrismaAudience
                                {
                                    Id = EsniExtensions.GetEsniLocalAudienceId(productionId),
                                    Match = PrismaAudienceMatch.ANY,
                                    Audience = audiences.Select(x => new PrismaAudience { XlinkHref = x }).ToList(),
                                },
                            },
                        },
                },
            };

            startMediaPoint.Apply.Add(prismaApply);
        }

        /// <summary>
        /// Gets the encoder.
        /// </summary>
        /// <param name="media">The media.</param>
        /// <returns>The encoder details.</returns>
        private static string GetEncoder([NotNull] MediaInfo media)
        {
            media.Required(nameof(media));
            return media.Schedules.FirstOrDefault(x => x.Active && !string.IsNullOrEmpty(x.Operations.Encoder))?.Operations.Encoder
                ?? media.Schedules.FirstOrDefault(x => x.Active && string.IsNullOrEmpty(x.Operations.Encoder))?.Operations.Encoder;
        }

        /// <summary>
        /// Gets the Quortex Endpoints creation information.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="media">The media.</param>
        /// <param name="thirdPartyEndpointCreationOptions">The ThirdParty endpoint creation options.</param>
        /// <returns>An AquilaChannelCreation info for the given GmsGame and Media.</returns>
        private static OttEndpointCreationInfo GetQuortexEndpointsCreationInfo(
            [NotNull] GmsEntity gmsEntity,
            [NotNull] MediaInfo media,
            [NotNull] ThirdPartyEndpointCreationOptions thirdPartyEndpointCreationOptions)
        {
            gmsEntity.Required(nameof(gmsEntity));
            media.Required(nameof(media));
            thirdPartyEndpointCreationOptions.Required(nameof(thirdPartyEndpointCreationOptions));
            var schedule = media.GetPreferredSchedule();
            var isThirdPartyProduction =
                schedule?.GetValueFromScheduleOrMediaKeyValuePairs(
                    thirdPartyEndpointCreationOptions.IsNSSThirdPartyKey,
                    media);
            var input = GetEncoder(media);
            var processing = schedule?.Resolution;
            var target = media.ThirdPartyStreamUrls?.FirstOrDefault()?.Name;
            var customPath = GetQuortexCustomPath(media.ThirdPartyStreamUrls?.FirstOrDefault()?.Url);

            var isValidURl = !string.IsNullOrEmpty(input) && !string.IsNullOrEmpty(processing) &&
                             !string.IsNullOrEmpty(target);

            if (isThirdPartyProduction != null && isValidURl)
            {
                return new OttEndpointCreationInfo()
                {
                    EventId = gmsEntity.Id,
                    PoolUuid = GetPoolIdForThirdPartyMedias(isThirdPartyProduction, thirdPartyEndpointCreationOptions),
                    InputUuid = input,
                    ProcessingUuid = processing,
                    TargetUuid = target,
                    Uuid = null,
                    CustomPath = customPath,
                    Enabled = true,
                    ChannelId = gmsEntity.GetChannelId(media),
                };
            }

            return null;
        }
        /// <summary>
        /// Gets the custom path for Quortex OTT endpoints.
        /// </summary>
        /// <param name="url">The Url.</param>
        /// <returns>Custom Path.</returns>
        private static string GetQuortexCustomPath(Uri url)
        {
            var pool = $"/{url?.AbsolutePath.Split('/')[1]}/";
            return url?.AbsolutePath.Replace(pool, "") + $"/{ManifestUrl}";
        }

        /// <summary>
        /// Gets the Aquila channel creation information.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="media">The media.</param>
        /// <param name="aquilaChannelCreationOptions">The Aquila channel creation options.</param>
        /// <param name="esniResourcesCreationOptions">The EsniResources Creation Options.</param>
        /// <returns>An AquilaChannelCreation info for the given GmsGame and Media.</returns>
        private static ChannelCreationInfo GetAquilaChannelCreationInfo(
            [NotNull] GmsEntity gmsEntity,
            [NotNull] MediaInfo media,
            [NotNull] AquilaChannelCreationOptions aquilaChannelCreationOptions,
            [NotNull] EsniResourcesCreationOptions esniResourcesCreationOptions)
        {
            gmsEntity.Required(nameof(gmsEntity));
            media.Required(nameof(media));
            aquilaChannelCreationOptions.Required(nameof(aquilaChannelCreationOptions));
            var schedule = media.GetPreferredSchedule();
            var isThirdPartyProduction =
                schedule?.GetValueFromScheduleOrMediaKeyValuePairs(
                    aquilaChannelCreationOptions.IsNSSThirdPartyKey,
                    media);
            if (isThirdPartyProduction != null)
            {
                return null;
            }

            string mediaResolution;
            if (gmsEntity.IsEvent)
            {
                mediaResolution = media.ScoppedResolution == null ? "Event_null" : $"Event_{media.ScoppedResolution}";
            }
            else
            {
                var isMediaDynamicallyEntitled = schedule != null && (schedule.HasPreGameExperience(media, esniResourcesCreationOptions) ||
                                                                      schedule.HasPostGameExperience(media, esniResourcesCreationOptions));
                if (!string.IsNullOrEmpty(aquilaChannelCreationOptions.DynamicEntitlementsCustomAquilaTemplate) && isMediaDynamicallyEntitled)
                {
                    mediaResolution = aquilaChannelCreationOptions.DynamicEntitlementsCustomAquilaTemplate;
                }
                else
                {
                    mediaResolution = media.ScoppedResolution == null ? "Game_null" : $"Game_{media.ScoppedResolution}";
                }
            }

            var regions = GetRegions(media, aquilaChannelCreationOptions.NSSGeoredundantRegions, aquilaChannelCreationOptions.NSSGeoRedundantRegionsSeparator);
            return new ChannelCreationInfo
            {
                ChannelId = gmsEntity.GetChannelId(media),
                ChannelName = gmsEntity.GetChannelId(media),
                TimeshiftDuration = aquilaChannelCreationOptions.TimeshiftDurationInHours,
                MediaResolution = mediaResolution,
                EncoderId = GetEncoder(media),
                EventId = gmsEntity.Id,
                PrimaryFeed = false,
                Regions = regions,
            };
        }

        /// <summary>
        /// Gets a list of valid regions from the NSS Georedundant Regions.
        /// </summary>
        /// <param name="media">The media.</param>
        /// <param name="nssGeoredundantRegionsKey">The NSS Georedundant regions key.</param>
        /// <param name="nssGeoredundantRegionsSeparator">The NSS Georedundant regions separator.</param>
        /// <returns>The regions.</returns>
        private static IEnumerable<string> GetRegions(MediaInfo media, string nssGeoredundantRegionsKey, string nssGeoredundantRegionsSeparator)
        {
            var schedule = GetScheduleWithEncoder(media);
            var nssGeoredundantRegions = schedule?.GetValueFromScheduleOrMediaKeyValuePairs(nssGeoredundantRegionsKey, media);

            if (!string.IsNullOrWhiteSpace(nssGeoredundantRegions))
            {
                var regions = nssGeoredundantRegions.Split(nssGeoredundantRegionsSeparator).Select(x => x.Trim());
                return regions;
            }

            return default;
        }

        /// <summary>
        /// Gets the schedule with encoder.
        /// </summary>
        /// <param name="media">The media.</param>
        /// <returns>The Schedule info.</returns>
        private static Schedule GetScheduleWithEncoder(MediaInfo media)
        {
            media.Required(nameof(media));
            return media.Schedules.FirstOrDefault(x => x.Active && !string.IsNullOrEmpty(x.Operations.Encoder));
        }

        /// <summary>
        /// Gets the schedule without encoder.
        /// </summary>
        /// <param name="media">The media.</param>
        /// <returns>The Schedule info.</returns>
        private static Schedule GetScheduleWithoutEncoder(MediaInfo media)
        {
            media.Required(nameof(media));
            return media.Schedules.FirstOrDefault(x => x.Active && string.IsNullOrEmpty(x.Operations.Encoder));
        }

        /// <summary>
        /// Given a Prisma Media Info, it returns an instance of <see cref="PrismaMediaInfo"/> with the details of Default Apply and Remove at Event Level.
        /// </summary>
        /// <param name="prismaInfo">The Prisma Media Info.</param>
        /// <returns>An instance of <see cref="PrismaMediaInfo"/> with the details added related to the Default Apply and Remove.</returns>
        private static PrismaMediaInfo AddEventLevelApplyandRemoveDefaults([NotNull] this PrismaMediaInfo prismaInfo)
        {
            var mediaPoints = prismaInfo.MediasToUpsert.SelectMany(media => media.MediaPoint);
            mediaPoints.Where(mediaPoint => mediaPoint.Id.EndsWith("/start", StringComparison.InvariantCultureIgnoreCase)).ForEach(mediaPoint => mediaPoint.AddDefaultApply());
            mediaPoints.Where(mediaPoint => mediaPoint.Id.EndsWith("/end", StringComparison.InvariantCultureIgnoreCase)).ForEach(mediaPoint => mediaPoint.AddDefaultRemove());
            return prismaInfo;
        }

        /// <summary>
        /// Adds the game level PrismaMedia to the PrismaMediaInfo.
        /// </summary>
        /// <param name="gmsEntity">The GMS entity.</param>
        /// <param name="nssPrimaryFeedKey">The NSS primary feed key.</param>
        /// <param name="prismaMediaInfo">The PrismaMediaInfo.</param>
        private static void AddGameLevelPrismaMediaToPrismaMediaInfo(GmsEntity gmsEntity, string nssPrimaryFeedKey, PrismaMediaInfo prismaMediaInfo)
        {
            var mediaName = GetPrimaryMediaName(gmsEntity, nssPrimaryFeedKey);
            var primaryPrismaMedia = prismaMediaInfo.MediasToUpsert.SingleOrDefault(x => x.Id.EndsWith(mediaName, StringComparison.InvariantCultureIgnoreCase));
            primaryPrismaMedia ??= prismaMediaInfo.MediasToUpsert.First();
            var gameLevelPrismaMedia = JsonConvert.DeserializeObject<PrismaMedia>(JsonConvert.SerializeObject(primaryPrismaMedia));
            gameLevelPrismaMedia.Id = ChangeIdInPrismaPath(gmsEntity.Id, gameLevelPrismaMedia.Id);

            foreach (var mediaPoint in gameLevelPrismaMedia.MediaPoint)
            {
                mediaPoint.Id = ChangeIdInPrismaPath(gmsEntity.Id, mediaPoint.Id);
            }

            prismaMediaInfo.MediasToUpsert = prismaMediaInfo.MediasToUpsert.Append(gameLevelPrismaMedia).ToList();
        }

        /// <summary>
        /// Changes identifier in Prisma path.
        /// </summary>
        /// <param name="newId">The new identifier.</param>
        /// <param name="path">The Prisma path.</param>
        /// <returns>The Prisma path with a new identifier.</returns>
        private static string ChangeIdInPrismaPath(string newId, string path)
        {
            const int idSectionIndex = 3;
            const string pathSeparator = "/";
            var pathSections = path.Split(pathSeparator);
            pathSections[idSectionIndex] = newId;

            return string.Join(pathSeparator, pathSections);
        }

        /// <summary>
        /// Gets a value indicating whether the media is the NSS primary feed.
        /// </summary>
        /// <param name="mediaInfo">The MediaInfo.</param>
        /// <param name="nssPrimaryFeedKey">The NSS primary feed key.</param>
        /// <returns>True if the media is the NSS primery feed; otherwise, false.</returns>
        private static bool IsNssPrimaryFeed([NotNull] MediaInfo mediaInfo, string nssPrimaryFeedKey)
        {
            return mediaInfo.Schedules.Any(
                x => x.Operations?.KeyValuePairs?.Any(
                    x => x.Key == nssPrimaryFeedKey
                    && bool.TryParse(x.Value, out var value)
                    && value)
                ?? false);
        }

        /// <summary>
        /// Sets the PrimaryFeed.
        /// </summary>
        /// <param name="gmsEntity">The GmsEntity.</param>
        /// <param name="channelCreationInfos">The ChannelCreationInfos.</param>
        /// <param name="nssPrimaryFeedKey">The NSS primary feed key.</param>
        /// <returns>ChannelCreationInfo with PrimaryFeed set.</returns>
        private static IEnumerable<ChannelCreationInfo> SetPrimaryFeed([NotNull] GmsEntity gmsEntity, IEnumerable<ChannelCreationInfo> channelCreationInfos, [NotNull] string nssPrimaryFeedKey)
        {
            var primaryChannelId = GetPrimaryMediaName(gmsEntity, nssPrimaryFeedKey);
            var primaryCreationInfo = channelCreationInfos.SingleOrDefault(x => x.ChannelId == primaryChannelId);

            if (primaryCreationInfo != null)
            {
                primaryCreationInfo.PrimaryFeed = true;
            }

            return channelCreationInfos;
        }

        /// <summary>
        /// Gets the primary <see cref="MediaInfo"/> name.
        /// </summary>
        /// <param name="gmsEntity">The <see cref="GmsEntity"/>.</param>
        /// <param name="nssPrimaryFeedKey">The NSS primary feed key.</param>
        /// <returns>The primary <see cref="MediaInfo"/> name.</returns>
        private static string GetPrimaryMediaName([NotNull] GmsEntity gmsEntity, [NotNull] string nssPrimaryFeedKey)
        {
            var primaryMedia = gmsEntity.GetPrimaryMedia(nssPrimaryFeedKey);
            var mediaName = gmsEntity.GetMediaName(gmsEntity, primaryMedia);

            return mediaName;
        }

        /// <summary>
        /// CompareMediaId.
        /// </summary>
        /// <param name="gmsMediaId">The gmsMediaId.</param>
        /// <param name="mediaId">MediaId.</param>
        /// <returns>true or false if medias match.</returns>
        private static bool CompareMediaId(string gmsMediaId, double mediaId)
        {
            NumberFormatInfo provider = new NumberFormatInfo();
            provider.NumberDecimalSeparator = ".";
            provider.NumberGroupSeparator = ",";
            var doubleMedia = Convert.ToDouble(gmsMediaId, provider);
            var comp = doubleMedia == mediaId;
            return comp;
        }
    }

    #pragma warning restore CA1502
}