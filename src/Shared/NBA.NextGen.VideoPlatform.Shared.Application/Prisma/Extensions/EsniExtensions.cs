// "//-----------------------------------------------------------------------".
// <copyright file="EsniExtensions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Application.Prisma.Extensions
{
    using System;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;

    /// <summary>
    /// The ESNI resource extension methods.
    /// </summary>
    public static class EsniExtensions
    {
        /// <summary>
        /// Gets the PRISMA pre-game start media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The start pre-game media point identifier for the given production id.</returns>
        public static string GetPreGameStartMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/pregamestart";
        }

        /// <summary>
        /// Gets the PRISMA pre-game end media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The pre-game end media point identifier for the given production id.</returns>
        public static string GetPreGameEndMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/pregameend";
        }

        /// <summary>
        /// Gets the PRISMA game start media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The game start media point identifier for the given production id.</returns>
        public static string GetGameStartMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/gamestart";
        }

        /// <summary>
        /// Gets the PRISMA game end media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The game end media point identifier for the given production id.</returns>
        public static string GetGameEndMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/gameend";
        }

        /// <summary>
        /// Gets the PRISMA post-game start media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The start post-game media point identifier for the given production id.</returns>
        public static string GetPostGameStartMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/postgamestart";
        }

        /// <summary>
        /// Gets the PRISMA post-game end media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The post-game end media point identifier for the given production id.</returns>
        public static string GetPostGameEndMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/postgameend";
        }

        /// <summary>
        /// Gets the PRISMA start media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The start media point identifier for the given production id.</returns>
        public static string GetStartMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/start";
        }

        /// <summary>
        /// Gets the PRISMA end media point identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The end media point identifier for the given production id.</returns>
        public static string GetEndMediaPointId(string productionId)
        {
            return $"/NBA/mediapoint/{productionId}/end";
        }

        /// <summary>
        /// Gets the PRISMA ESNI media identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The ESNI media identifier for the given production id.</returns>
        public static string GetEsniMediaId(string productionId)
        {
            return $"/NBA/media/{productionId}";
        }

        /// <summary>
        /// Gets the PRISMA local policy identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The ESNI local policy identifier for the given production id.</returns>
        public static string GetEsniLocalPolicyId(string productionId)
        {
            return $"/NBA/policy/local/{productionId}";
        }

        /// <summary>
        /// Gets the PRISMA regional policy identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The ESNI regional policy identifier for the given production id.</returns>
        public static string GetEsniRegionalPolicyId(string productionId)
        {
            return $"/NBA/policy/regional/{productionId}";
        }

        /// <summary>
        /// Gets the PRISMA world policy identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The ESNI world policy identifier for the given production id.</returns>
        public static string GetEsniWorldPolicyId(string productionId)
        {
            return $"/NBA/policy/world/{productionId}";
        }

        /// <summary>
        /// Gets the PRISMA local viewing policy identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The local viewing policy identifier for the given production id.</returns>
        public static string GetEsniLocalViewingPolicyId(string productionId)
        {
            return $"/NBA/viewingpolicy/local/{productionId}";
        }

        /// <summary>
        /// Gets the PRISMA local audience identifier.
        /// </summary>
        /// <param name="productionId">The production identifier.</param>
        /// <returns>The local audience identifier for the given production id.</returns>
        public static string GetEsniLocalAudienceId(string productionId)
        {
            return $"/NBA/audience/local/{productionId}";
        }

        /// <summary>
        /// Gets the PRISMA RSN local audience href.
        /// </summary>
        /// <param name="teamTricode">The team tricode.</param>
        /// <returns>The RSN local audience for the given team tricode.</returns>
        public static string GetRsnLocalAudienceHref(string teamTricode)
        {
            return $"/NBA/audience/us/{teamTricode}/rsn";
        }

        /// <summary>
        /// Gets the PRISMA OTA local audience href.
        /// </summary>
        /// <param name="teamTricode">The team tricode.</param>
        /// <returns>The OTA local audience for the given team tricode.</returns>
        public static string GetOtaLocalAudienceHref(string teamTricode)
        {
            return $"/NBA/audience/us/{teamTricode}/ota";
        }

        /// <summary>
        /// Converts the <see cref="TimeSpan"/> to ISO 8601 duration format, more info in the wiki https://en.wikipedia.org/wiki/ISO_8601#Durations.
        /// </summary>
        /// <param name="value">The value to convert.</param>
        /// <returns>The ESNI world policy identifier for the given production id.</returns>
        public static string ConvertTimeSpanToIso8601Format(TimeSpan value)
        {
            if (value.Ticks > 0)
            {
                var hours = $"{(int)value.TotalHours}H";
                var minutes = value.Minutes > 0
                    ? $"{value.Minutes}M"
                    : string.Empty;
                return $"PT{hours}{minutes}";
            }
            else
            {
                throw new ArgumentException($"The value must be a positive {nameof(TimeSpan)}");
            }
        }

        /// <summary>
        /// Convert ISO 8601 format to <see cref="TimeSpan"/>.
        /// </summary>
        /// <param name="iso8601value">The ISO 8601 value.</param>
        /// <returns>The <see cref="TimeSpan"/>.</returns>
        public static TimeSpan ConvertIso8601FormatToHours([NotNull] string iso8601value)
        {
            const string prefix = "PT";
            var stringHours = string.Empty;

            try
            {
                if (iso8601value.StartsWith(prefix, StringComparison.Ordinal))
                {
                    var startIndex = prefix.Length;
                    stringHours = iso8601value[startIndex..iso8601value.IndexOf("H", StringComparison.Ordinal)];
                }

                return TimeSpan.FromHours(int.Parse(stringHours, CultureInfo.InvariantCulture));
            }
            catch
            {
                throw new ArgumentException($"The ISO 8601 value must have the next format PT[hours]H, for example PT12H");
            }
        }
    }
}
