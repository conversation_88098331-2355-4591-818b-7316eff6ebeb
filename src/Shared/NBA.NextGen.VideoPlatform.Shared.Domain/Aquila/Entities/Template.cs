// "//-----------------------------------------------------------------------".
// <copyright file="Template.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities
{
    /// <summary>
    /// Source.
    /// </summary>
    public class Template : AquilaEntity
    {
        /// <summary>
        /// Gets or sets the config.
        /// </summary>
        /// <value>
        /// The config.
        /// </value>
        public object Config { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the quota name.
        /// </summary>
        /// <value>
        /// The Quota Name.
        /// </value>
        public string QuotaName { get; set; }

        /// <summary>
        /// Gets or sets the resources.
        /// </summary>
        /// <value>
        /// The resources.
        /// </value>
        public object Resources { get; set; }

        /// <summary>
        /// Gets or sets the status.
        /// </summary>
        /// <value>
        /// The Status.
        /// </value>
        public string Status { get; set; }
    }
}
