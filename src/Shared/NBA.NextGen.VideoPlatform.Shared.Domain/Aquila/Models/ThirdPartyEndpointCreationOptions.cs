// "//-----------------------------------------------------------------------".
// <copyright file="ThirdPartyEndpointCreationOptions.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models
{
    /// <summary>
    /// Configurable options for channel creation in Aquila.
    /// </summary>
    public class ThirdPartyEndpointCreationOptions
    {
        /// <summary>
        /// Gets or sets the timeshift duration in hours.
        /// </summary>
        /// <value>
        /// The timeshift duration in hours.
        /// </value>
        public int TimeshiftDurationInHours { get; set; }

        /// <summary>
        /// Gets or sets the NSS Primary Feed key.
        /// </summary>
        /// <value>
        /// The NSS Primary Feed key.
        /// </value>
        public string NSSPrimaryFeedKey { get; set; }

        /// <summary>
        /// Gets or sets the Key to verify is not a third party production.
        /// </summary>
        /// The Custom Aquila Template.
        public string IsNSSThirdPartyKey { get; set; }

        /// <summary>
        /// Gets or sets the PoolUUID for the enviroment.=.
        /// </summary>
        /// The Custom Aquila Template.
        public string PoolUUID { get; set; }

        /// <summary>
        /// Gets or sets the PoolUUID for the enviroment.=.
        /// </summary>
        /// The Custom Aquila Template.
        public string PoolUUIDDR { get; set; }

        /// <summary>
        /// Gets or sets the PoolUUID for the enviroment.=.
        /// </summary>
        /// The Custom Aquila Template.
        public string PoolUUIDRadio { get; set; }
    }
}
