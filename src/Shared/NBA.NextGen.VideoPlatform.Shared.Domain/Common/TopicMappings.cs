// "//-----------------------------------------------------------------------".
// <copyright file="TopicMappings.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Common
{
    using System;
    using System.Collections.Concurrent;
    using System.Diagnostics.CodeAnalysis;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Health;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;

    /// <summary>
    /// Mapping of topics for messages.
    /// </summary>
    public static class TopicMappings
    {
        /// <summary>Gets the map.</summary>
        /// <value>The map.</value>
        private static ConcurrentDictionary<Type, string> Map { get; } = new ConcurrentDictionary<Type, string>
        {
            [typeof(GmsUpdatedEvent)] = TopicNames.GmsWatchdogTopic,
            [typeof(AquilaUpdatedEvent)] = TopicNames.AquilaWatchdogTopic,
            [typeof(ScheduleChangedEvent)] = TopicNames.VideoPlatform,
            [typeof(InfrastructureStateChangedEvent)] = TopicNames.VideoPlatform,
            [typeof(ServiceHealthChangedEvent)] = TopicNames.VideoPlatformHealth,
        };

        /// <summary>
        /// Get topic name.
        /// </summary>
        /// <param name="eventDto">
        /// The event.
        /// </param>
        /// <returns>
        /// The topic name.
        /// </returns>
        public static string GetTopicNameForEvent([NotNull] object eventDto) => Map[eventDto.GetType()];
    }
}
