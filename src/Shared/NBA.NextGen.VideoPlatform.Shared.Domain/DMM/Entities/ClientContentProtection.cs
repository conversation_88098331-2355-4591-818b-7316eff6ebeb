// "//-----------------------------------------------------------------------".
// <copyright file="ClientContentProtection.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities
{
    using System.Collections.Generic;

    /// <summary>
    /// Content Protection clients.
    /// </summary>
    public class ClientContentProtection
    {
        /// <summary>
        /// Gets or sets or set the Name.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets or set the streams.
        /// </summary>
        public IList<StreamContentProtection> Streams { get; set; }
    }
}