// "//-----------------------------------------------------------------------".
// <copyright file="StreamLiveProduction.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.DMM.Entities
{
    using System.Diagnostics.CodeAnalysis;

    /// <summary>
    /// Stream Live Production.
    /// </summary>
    public class StreamLiveProduction
    {
        /// <summary>
        /// Gets or sets sets the stream id.
        /// </summary>
        public string StreamId { get; set; }

        /// <summary>
        /// Gets or sets sets the input.
        /// </summary>
        public string Input { get; set; }

        /// <summary>
        /// Gets or sets sets the Primary stream url.
        /// </summary>
        [SuppressMessage("Design", "CA1056:URI-like properties should not be strings", Justification = "Serialized Name")]
        public string PrimaryStreamUrl { get; set; }

        /// <summary>
        /// Gets or sets sets the backup stream url.
        /// </summary>
        [SuppressMessage("Design", "CA1056:URI-like properties should not be strings", Justification = "Serialized Name")]
        public string BackupStreamUrl { get; set; }

        /// <summary>
        /// Gets or sets sets the unique name.
        /// </summary>
        public string UniqueName { get; set; }

        /// <summary>
        /// Gets or sets sets the display name.
        /// </summary>
        public string DisplayName { get; set; }
    }
}