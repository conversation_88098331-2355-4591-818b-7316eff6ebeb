// "//-----------------------------------------------------------------------".
// <copyright file="RestrictionMode.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Enums
{
    using System.Runtime.Serialization;

    /// <summary>
    /// Restriction Mode.
    /// </summary>
    public enum RestrictionMode
    {
        /// <summary>
        /// The allow.
        /// </summary>
        [EnumMember(Value = "Allow")]
        Allow,

        /// <summary>
        /// The deny.
        /// </summary>
        [EnumMember(Value = "Block")]
        Block,
    }
}
