// "//-----------------------------------------------------------------------".
// <copyright file="GmsEntitlementRules.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Models
{
    using System;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;

    /// <summary>
    /// The EntitlementRule.
    /// </summary>
    public class GmsEntitlementRules : VideoPlatformEntity<string>
    {
        /// <summary>
        /// Gets or sets the GMS entity identifier that was updated.
        /// </summary>
        /// <value>
        /// GMS entity identifier (game or event "id").
        /// </value>
        public string RuleId { get; set; }

        /// <summary>
        /// Gets or sets the name of the property.
        /// </summary>
        /// <value>
        /// The name of the property.
        /// </value>
        public string Parameter { get; set; }

        /// <summary>
        /// Gets or sets the property level.
        /// </summary>
        /// <value>
        /// The property level.
        /// </value>
        public string Value { get; set; }

        /// <summary>
        /// Gets or sets the name of the pass.
        /// </summary>
        /// <value>
        /// The name of the pass.
        /// </value>
        public string Entitlement { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this instance is special.
        /// </summary>
        /// <value>
        ///   <c>true</c> if this instance is special; otherwise, <c>false</c>.
        /// </value>
        public bool ApplyToAllMedia { get; set; }

        /// <summary>
        /// Gets a value indicating whether gets whether rule is Media Level.
        /// </summary>
        public bool IsMediaRule => this.Parameter.StartsWith(GmsGameTransformationConstants.MediaRule, StringComparison.Ordinal);

        /// <summary>
        /// Gets a value indicating whether gets whether rule is Game Level.
        /// </summary>
        public bool IsGameRule => this.Parameter.StartsWith(GmsGameTransformationConstants.GameRule, StringComparison.Ordinal);

        /// <summary>
        /// Gets a value indicating whether gets whether rule is Event Level.
        /// </summary>
        public bool IsEventRule => this.Parameter.StartsWith(GmsGameTransformationConstants.EventRule, StringComparison.Ordinal);

        /// <summary>
        /// Gets or sets a value indicating whether to override the default packages creation (game subscription and team packages).
        /// </summary>
        /// <value>
        ///   <c>true</c> for overriding the default packages creation; otherwise, <c>false</c>.
        /// </value>
        public bool OverrideDefaultPackages { get; set; }
    }
}
