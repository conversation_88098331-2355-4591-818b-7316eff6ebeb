// "//-----------------------------------------------------------------------".
// <copyright file="ScheduleChannelValidationEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;

    /// <summary>
    /// The model for the <see cref="ScheduleChannelValidationEvent"/>.
    /// </summary>
    public class ScheduleChannelValidationEvent : ChannelCreationInfo
    {
        /// <summary>
        /// Gets or sets a value indicating whether source properly configured.
        /// </summary>
        public bool SourceProperlyConfigured { get; set; }

        /// <summary>
        /// Gets or Sets the Source Status Message of event.
        /// </summary>
        public string SourceStatusMessage { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether template properly configured.
        /// </summary>
        public bool TemplateProperlyConfigured { get; set; }

        /// <summary>
        /// Gets or sets the Template Status Message.
        /// </summary>
        public string TemplateStatusMessage { get; set; }
    }
}
