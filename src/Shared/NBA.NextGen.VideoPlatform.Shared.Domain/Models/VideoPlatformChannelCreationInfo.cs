namespace NBA.NextGen.VideoPlatform.Shared.Domain.Models
{
    using System.Diagnostics.CodeAnalysis;
    using MongoDB.Bson;
    using MongoDB.Bson.Serialization.Attributes;

    [ExcludeFromCodeCoverage]
    [BsonNoId]
    [BsonIgnoreExtraElements]
    public class VideoPlatformChannelCreationInfo
    {
        [BsonElement("Id")]
        public string Id { get; set; }

        public string LiveEventId { get; set; }

        public bool PrimaryFeed { get; set; }

        public bool HasInBandScte35 { get; set; }

        public bool LiveToOnDemand { get; set; }
    }
}
