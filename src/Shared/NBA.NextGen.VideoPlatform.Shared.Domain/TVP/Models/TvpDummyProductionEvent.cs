// "//-----------------------------------------------------------------------".
// <copyright file="TvpDummyProductionEvent.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    /// <summary>
    /// The payload of the event sent to MCD to notify about changes on dummy productions.
    /// </summary>
    public class TvpDummyProductionEvent
    {
        /// <summary>
        /// The action for dummy production creation.
        /// </summary>
        public const string DummyProductionCreationAction = "Creation";

        /// <summary>
        /// The action for dummy production deletion.
        /// </summary>
        public const string DummyProductionDeletionAction = "Deletion";

        /// <summary>
        /// Gets or sets the action.
        /// </summary>
        /// <value>
        /// The action.
        /// </value>
        public string Action { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this <see cref="TvpDummyProductionEvent"/> is success.
        /// </summary>
        /// <value>
        ///   <c>true</c> if success; otherwise, <c>false</c>.
        /// </value>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the live event identifier.
        /// </summary>
        /// <value>
        /// The live event identifier.
        /// </value>
        public string LiveEventId { get; set; }

        /// <summary>
        /// Gets or sets the production identifier.
        /// </summary>
        /// <value>
        /// The production identifier.
        /// </value>
        public string ProductionId { get; set; }
    }
}
