// "//-----------------------------------------------------------------------".
// <copyright file="TvpEventUpdateInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models
{
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;

    /// <summary>
    /// TvpEventUpdateInfo.
    /// </summary>
    public class TvpEventUpdateInfo
    {
        /// <summary>
        /// Gets or sets the external identifier.
        /// </summary>
        /// <value>
        /// The external identifier.
        /// </value>
        public string ExternalId { get; set; }

        /// <summary>
        /// Gets or sets the name.
        /// </summary>
        /// <value>
        /// The name.
        /// </value>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the short name.
        /// </summary>
        /// <value>
        /// The short name.
        /// </value>
        public string ShortName { get; set; }

        /// <summary>
        /// Gets or sets the description.
        /// </summary>
        /// <value>
        /// The description.
        /// </value>
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the name of the sort.
        /// </summary>
        /// <value>
        /// The name of the sort.
        /// </value>
        public string SortName { get; set; }

        /// <summary>
        /// Gets or sets the type of the event.
        /// </summary>
        /// <value>
        /// The type of the event.
        /// </value>
        public string EventType { get; set; }

        /// <summary>
        /// Gets or sets the SeasonId.
        /// </summary>
        /// <value>
        /// The Id of the tournament season.
        /// </value>
        public string TournamentSeasonId { get; set; }

        /// <summary>
        /// Gets or sets the event status.
        /// </summary>
        /// <value>
        /// The event status.
        /// </value>
        public TvpEventStatus? EventStatus { get; set; }

        /// <summary>
        /// Gets or sets the location external identifier.
        /// </summary>
        /// <value>
        /// The location external identifier.
        /// </value>
        public string LocationExternalId { get; set; }

        /// <summary>
        /// Gets or sets the teams role.
        /// </summary>
        /// <value>
        /// The teams role.
        /// </value>
        public IList<TvpTeamRole> TeamsRole { get; set; }

        /// <summary>
        /// Gets or sets the team members.
        /// </summary>
        /// <value>
        /// The team members.
        /// </value>
        public object TeamMembers { get; set; }

        /// <summary>
        /// Gets or sets the type of the show.
        /// </summary>
        /// <value>
        /// The type of the show.
        /// </value>
        public string ShowType { get; set; }
    }
}
