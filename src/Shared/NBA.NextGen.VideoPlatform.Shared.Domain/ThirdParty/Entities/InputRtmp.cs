// "//-----------------------------------------------------------------------".
// <copyright file="InputRtmp.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities
{
    using System.Collections.Generic;

    /// <summary>
    /// The InputRtmp object.
    /// </summary>
    public class InputRtmp
    {
        /// <summary>
        /// Gets or sets the address.
        /// </summary>
        public string Address { get; set; }

        /// <summary>
        /// Gets or sets the RtmpStreamOverrides.
        /// </summary>
        public IEnumerable<RtmpStreamOverride> RtmpStreamOverrides { get; set; }
    }
}