// "//-----------------------------------------------------------------------".
// <copyright file="ThirdPartyChannelState.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Enums
{
    using System.Runtime.Serialization;

    /// <summary>
    /// ThirdParty channel states.
    /// </summary>
    public enum ThirdPartyChannelState
    {
        /// <summary>
        /// Active state.
        /// </summary>
        [EnumMember(Value = "active")]
        Active,

        /// <summary>
        /// Configuring state.
        /// </summary>
        [EnumMember(Value = "configuring")]
        Configuring,

        /// <summary>
        /// Disabled state.
        /// </summary>
        [EnumMember(Value = "disabled")]
        Disabled,

        /// <summary>
        /// Warning state.
        /// </summary>
        [EnumMember(Value = "warning")]
        Warning,
    }
}