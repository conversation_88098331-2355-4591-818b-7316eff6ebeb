// "//-----------------------------------------------------------------------".
// <copyright file="PlayoutClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.Playout.Services
{
    using System.Net.Http;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Vendor.Api.Playout;
    using NBA.NextGen.VideoPlatform.Shared.Application.Playout.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;

    /// <summary>
    /// The playout client service.
    /// </summary>
    public class PlayoutClientService : IPlayoutClientService
    {
        /// <summary>
        /// The playout client.
        /// </summary>
        private readonly IPlayoutClient playoutClient;

        /// <summary>
        /// The APIM options.
        /// </summary>
        private readonly IOptionsMonitor<ApiManagementOptions> apimOptions;

        /// <summary>
        /// Initializes a new instance of the <see cref="PlayoutClientService"/> class.
        /// </summary>
        /// <param name="playoutClient">The playout client.</param>
        /// <param name="apimOptions">The APIM options.</param>
        public PlayoutClientService(IPlayoutClient playoutClient, IOptionsMonitor<ApiManagementOptions> apimOptions)
        {
            this.playoutClient = playoutClient;
            this.apimOptions = apimOptions;

            this.playoutClient.SetPrepareRequestAction((httpRequestMessage) =>
            {
                httpRequestMessage.Headers.Add("Ocp-Apim-Subscription-Key", this.apimOptions.CurrentValue.SubscriptionKey);
                if (this.apimOptions.CurrentValue.EnableMocking)
                {
                    httpRequestMessage.Headers.Add("x-mock-data", "test");
                }

                return Task.CompletedTask;
            });
        }

        /// <inheritdoc/>
        public async Task<AssetPlayout> GetPlayoutAsync(string assetId, string playoutId)
        {
            return await this.playoutClient.GetAssetPlayoutByAssetIdAndPlayoutIdAsync(assetId, playoutId).ConfigureAwait(false);
        }

        /// <inheritdoc/>
        public async Task StartPlayoutAsync(string assetId, StartAssetPlayoutCommand command)
        {
            await this.playoutClient.StartAssetPlayoutAsync(assetId, command).ConfigureAwait(false);
        }

        /// <summary>
        /// Stops the playout asynchronous.
        /// </summary>
        /// <param name="assetId">The asset identifier.</param>
        /// <param name="playoutId">The playout identifier.</param>
        /// <returns>representing the asynchronous operation.</returns>
        public async Task StopPlayoutAsync(string assetId, string playoutId)
        {
            await this.playoutClient.DeleteAssetPlayoutAsync(assetId, playoutId).ConfigureAwait(false);
        }
    }
}
