// "//-----------------------------------------------------------------------".
// <copyright file="QuortexClientService.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Shared.Infrastructure.ThirdParty.Services
{
    using System;
    using System.Collections.Generic;
    using System.Collections.ObjectModel;
    using System.Diagnostics.CodeAnalysis;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Microsoft.Extensions.Options;
    using NBA.NextGen.Shared.Application.Common;
    using NBA.NextGen.Shared.Domain.Common;
    using NBA.NextGen.Vendor.Api.Quortex;
    using NBA.NextGen.VideoPlatform.Shared.Application.ThirdParty.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Configuration;
    using NBA.NextGen.VideoPlatform.Shared.Domain.ThirdParty.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Infrastructure.Configuration;

    /// <summary>
    /// Sources.
    /// </summary>
    public class QuortexClientService : IQuortexClientService
    {
        /// <summary>
        /// The thirdParty client.
        /// </summary>
        private readonly IQuortexClient quortexClient;

        /// <summary>
        /// The apim options.
        /// </summary>
        private readonly IOptionsMonitor<ApiManagementOptions> apimOptions;

        /// <summary>
        /// The thirdParty options.
        /// </summary>
        private readonly IOptionsMonitor<QuortexOptions> quortexOptions;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly ILogger<QuortexClientService> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="QuortexClientService" /> class.
        /// </summary>
        /// <param name="quortexClient">The thirdParty client factory.</param>
        /// <param name="apimOptions">The apim options.</param>
        /// <param name="quortexOptions">The thirdParty options.</param>
        /// <param name="mapper">The mapper.</param>
        /// <param name="logger">The logger.</param>
        public QuortexClientService([NotNull] IClientFactory clientFactory, IOptionsMonitor<ApiManagementOptions> apimOptions, IOptionsMonitor<QuortexOptions> quortexOptions, IMapper mapper, ILogger<QuortexClientService> logger)
        {
            clientFactory.Required(nameof(clientFactory));
            this.quortexClient = clientFactory.CreateClient(false);
            this.apimOptions = apimOptions;
            this.quortexOptions = quortexOptions;
            this.mapper = mapper;
            this.logger = logger;
            this.quortexClient.SetPrepareRequestAction((httpRequestMessage) =>
            {
                httpRequestMessage.Headers.Add("Ocp-Apim-Subscription-Key", this.apimOptions.CurrentValue.SubscriptionKey);
                if (this.apimOptions.CurrentValue.EnableMocking)
                {
                    httpRequestMessage.Headers.Add("x-mock-data", "test");
                }

                return Task.CompletedTask;
            });
        }

        /// <summary>
        /// Get an ott endpoint.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <param name="uuid">The Uuid.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<OttEndpointUpdate> GetOttEndpointAsync(string poolUuid, string uuid)
        {
            return await this.quortexClient.PoolsottendpointsretrieveAsync(poolUuid, uuid).ConfigureAwait(false);
        }

        /// <summary>
        /// List inputs.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<Collection<StreamInput>> ListInputsAsync()
        {
            var inputs = await this.quortexClient.InputslistAsync().ConfigureAwait(false);
            var streamInputs = new Collection<StreamInput>();
            foreach (var input in inputs.Results)
            {
                var convertedInput = this.mapper.Map<StreamInput>(input);
                streamInputs.Add(convertedInput);
            }

            return streamInputs;
        }

        /// <summary>
        /// List all endpoints.
        /// </summary>
        /// <param name="poolUuid">Pool UUID.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<Collection<Domain.ThirdParty.Entities.OttEndpoint>> ListAllEndpointsAsync(string poolUuid)
        {
            var endpointList = await this.quortexClient.PoolsottendpointslistAsync(poolUuid).ConfigureAwait(false);
            var endpoints = new Collection<Domain.ThirdParty.Entities.OttEndpoint>();
            foreach (var endpoint in endpointList.Results)
            {
                var convertedEndpoint = this.mapper.Map<Domain.ThirdParty.Entities.OttEndpoint>(endpoint);
                endpoints.Add(convertedEndpoint);
            }

            return endpoints;
        }

        /// <summary>
        /// Get an ott endpoint.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <param name="uuid">The Uuid.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task DeleteOttEndpointAsync(string poolUuid, string uuid)
        {
            await this.quortexClient.PoolsottendpointsdestroyAsync(poolUuid, uuid).ConfigureAwait(false);
        }

        /// <summary>
        /// Get an ott endpoint.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <param name="ottEndpointRequest">the OttEndpoint request.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<NBA.NextGen.Vendor.Api.Quortex.OttEndpoint> CreateOttEndpointAsync(string poolUuid, OttEndpointRequest ottEndpointRequest)
        {
            return await this.quortexClient.PoolsottendpointscreateAsync(poolUuid, ottEndpointRequest).ConfigureAwait(false);
        }

        /// <summary>
        /// Get an ott endpoint.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <param name="uuid">The Uuid.</param>
        /// <param name="ottEndpointUpdateRequest">the update request.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<OttEndpointUpdate> UpdateOttEndpointAsync(string poolUuid, string uuid, OttEndpointUpdateRequest ottEndpointUpdateRequest)
        {
            return await this.quortexClient.PoolsottendpointspartialupdateAsync(poolUuid, uuid, ottEndpointUpdateRequest).ConfigureAwait(false);
        }

        /// <summary>
        /// get List of endpoints.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<PaginatedOttEndpointList> GetOttEndpointListAsync(string poolUuid)
        {
            return await this.quortexClient.PoolsottendpointslistAsync(poolUuid).ConfigureAwait(false);
        }

        /// <summary>
        /// get List of Processing.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<PaginatedProcessingList> GetProcessingListAsync(string poolUuid)
        {
            return await this.quortexClient.PoolsprocessingslistAsync(poolUuid).ConfigureAwait(false);
        }

        /// <summary>
        /// get List of Targets.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<PaginatedTargetList> GetTargetsListAsync(string poolUuid)
        {
            return await this.quortexClient.PoolstargetslistAsync(poolUuid).ConfigureAwait(false);
        }

        /// <summary>
        /// get List of Targets.
        /// </summary>
        /// <param name="poolUuid">The Pool Uuid.</param>
        /// <returns>A <see cref="Task{TResult}"/> representing the result of the asynchronous operation.</returns>
        public async Task<PaginatedInputList> GetInputsListAsync(string poolUuid)
        {
            return await this.quortexClient.PoolsinputslistAsync(poolUuid).ConfigureAwait(false);
        }

        /// <inheritdoc/>
        public Task<HealthStatusResult> GetHealthStatusAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            throw new NotImplementedException();
        }
    }
}
