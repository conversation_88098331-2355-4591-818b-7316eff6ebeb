# Operational State

## How to use this POSTMAN collection?

This POSTMAN collections allows you to get and set the operational state of a TVP Production.

To run it, you will need to set up a [POSTMAN environment](https://learning.postman.com/docs/sending-requests/managing-environments/) that include two variables:

- **apim-base-url**: the base URL of the Videoplatform APIM instance to be used. This will determine, for example, if you are hitting PRODB or PRODC. At the moment of writing this document, these are the following APIM base URLs:
   - DEVINT/PRODC: https://apim-videoplatform-hkjkip.azure-api.net
   - QA/PRODB: https://apim-videoplatform-nmfoam.azure-api.net
- **apim-subscription-key**: the APIM subscription key to get authorized to perform the API calls. You can get them through [the Azure portal](https://docs.microsoft.com/en-us/azure/api-management/api-management-subscriptions). If you don't have permissions to get them, reach out to WS1 leads to get them.

In addition to the environments, you will also need to be connected to NBA VPN. You can find more data about how to connect to the VPN in [this article in the ADO Wiki](https://dev.azure.com/nbadev/DTC/_wiki/wikis/DTC.wiki/793/Connecting-to-the-runtime-environments-via-VPN).