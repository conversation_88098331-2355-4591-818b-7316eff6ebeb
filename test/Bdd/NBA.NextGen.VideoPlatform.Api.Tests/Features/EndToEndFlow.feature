@owner=v-avasudevan @videoplatform @testplan=7081 @testsuite=18251 @parallel=false @endtoend
Feature: EndToEnd
End to End orchestration complete flow - Starts from GMS game to CosmosDB, Schedule creation, validations and all workflows validations using stub

@testcase=18280 @testsuite=18251 @version=18 @priority=1 @before=ClearEventsData() @before=GetRequiredOffsetsAsync() @before=GetRandomVideoPlatformSourceFromCosmosAsync() @after=DeleteTestDataFromBlob() @after=ClearEventsData() @stub @game @priority1
Scenario: Game Complete End To End Orchestration Flow
Given I create a GMS "Game" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
| TV            | United States   | Regional              | Any         | True          | True                  |
| TV            | United States   | RSN                   | Home        | True          | True                  |
When I trigger the GMS "GmsGame" polling function
Then I verify that the "gms.GameUpdated" event is published with CorrelationId
When I trigger the GMS "GmsTeamZip" polling function
And I create a team zips document in database
And I trigger Audience function by passing above team information
And I verify that the "RequestAcknowledgement" event is published with CorrelationId
And I verify that the "ScheduleChanged" event is published with CorrelationId
Then I validate if valid gms entitlements are created
And I validate if following actor details in respective workflow intents are correct
| WorkFlowIntent           | Actor  | CanBePresent |
| EventMetadataSetup       | Tvp    | true         |
| EventMetadataSetup       | Prisma | true         |
| EventInfrastructureSetup | Aquila | true         |
| EventInfrastructureSetup | Tvp    | true         |
| EventMetadataStart       | Tvp    | true         |
| EventMetadataEnd         | Tvp    | true         |
| EventMetadataCleanup     | Tvp    | true         |
| EventMetadataCleanup     | Prisma | true         |
And I validate if EventInfrastructureStart, EventInfrastructureStop, EventReachedTipoffTime and EventInfrastructureCleanup workflow intents of each schedule per NSS media of game are correct
And I validate if valid audiences are created for RSN and OTA media distributions of the team
And I validate if valid esni medias with blackouts are created for NSS medias of above "Game"
And I validate if a schedule got created with audience ids in setup workflow as part of prisma actor details
And I add Channel details in Aquila stub

When I update the time of "EventMetadataSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Init"

When I update the time of "EventInfrastructureSetup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Tvp" actor for "EventInfrastructureSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Created"

When I update the time of "EventMetadataStart" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId

And I get existing playout data from stub
When I update the time of "EventInfrastructureStart" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
When I ensure the state of the Channel is changed to "started" from "stopped"in Aquila
And I ensure the state of the Channel is changed to "started" from "started" in Aquila with instance state as "started"
And I should get "InfrastructureStateChanged" event with state as "started" from "Aquila" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "started" from "Tvp" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Playout actor with CorrelationId
And Playout started state is made to "true"
And I should get "InfrastructureStateChanged" event with state as "started" from "Playout" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Started"
And I should get new playout in stub

When I update the time of "EventReachedTipoffTime" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventReachedTipoffTime" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Broadcast"

When I update the time of "EventMetadataEnd" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataEnd" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId

When I update the time of "EventInfrastructureEnd" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Stopped" from "Tvp" actor for "EventInfrastructureEnd" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Playout actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "stopped" from "Playout" actor for "EventInfrastructureEnd" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And Playout is removed from stub
And I verify channel operational state is "Over"

When I update the time of "EventMetadataCleanup" workflow in "Game" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Prisma" actor for "EventMetadataCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId

When I update the time of "EventInfrastructureCleanup" workflow in "Media" level schedule to run immediately 
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
When I ensure the state of the Channel is changed to "stopped" from "started"in Aquila
And I should get "InfrastructureStateChanged" event with state as "deprovisioned" from "Aquila" actor for "EventInfrastructureCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Deleted"

@testcase=21998 @testsuite=21995 @version=7 @priority=1 @before=ClearEventsData() @after=DeleteTestDataFromBlob() @after=ClearEventsData() @stub @event @priority1
Scenario: Event Complete End To End Orchestration Flow
Given I create a GMS event in GMS
When I trigger the GMS "GmsEvent" polling function
Then I verify that the "gms.EventUpdated" event is published
And I verify that the "RequestAcknowledgement" event is published
And I verify that the "ScheduleChanged" event is published
And I add TVP stub details in Tvp for event
And I add Channel details in Aquila stub

When I update the time of "EventMetadataSetup" workflow in "Event" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Init"

When I update the time of "EventInfrastructureSetup" workflow in "Event" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Aquila" actor for "EventInfrastructureSetup" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "provisioned" from "Tvp" actor for "EventInfrastructureSetup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Created"

When I update the time of "EventMetadataStart" workflow in "Event" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Tvp" actor for "EventMetadataStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId

And I get existing playout data from stub
When I update the time of "EventInfrastructureStart" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
And I ensure the state of the Channel is changed to "started" from "stopped"in Aquila
And I ensure the state of the Channel is changed to "started" from "started" in Aquila with instance state as "started"
And I should get "InfrastructureStateChanged" event with state as "started" from "Aquila" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "started" from "Tvp" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Prisma actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Configured" from "Prisma" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Playout actor with CorrelationId
And Playout started state is made to "true"
And I should get "InfrastructureStateChanged" event with state as "started" from "Playout" actor for "EventInfrastructureStart" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Started"
And I should get new playout in stub

When I update the time of "EventMetadataEnd" workflow in "Event" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataEnd" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId

When I update the time of "EventInfrastructureEnd" workflow in "Media" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "Stopped" from "Tvp" actor for "EventInfrastructureEnd" workflow with CorrelationId
And I should get "RequestAcknowledgement" event from Playout actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "stopped" from "Playout" actor for "EventInfrastructureEnd" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And Playout is removed from stub

When I update the time of "EventMetadataCleanup" workflow in "Event" level schedule to run immediately
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from TVP actor with CorrelationId
And I should get "InfrastructureStateChanged" event with state as "configured" from "Tvp" actor for "EventMetadataCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId

When I update the time of "EventInfrastructureCleanup" workflow in "Media" level schedule to run immediately 
Then I should get "RequestAcknowledgement" event from the Orchestrator with CorrelationId
And I should get "WorkflowStateChanged" event with "Inprogress" status from the Orchestrator with CorrelationId
And I should get "RequestAcknowledgement" event from Aquila actor with CorrelationId
And I ensure the state of the Channel is changed to "stopped" from "started"in Aquila
Then I should get "InfrastructureStateChanged" event with state as "deprovisioned" from "Aquila" actor for "EventInfrastructureCleanup" workflow with CorrelationId
And I should get "WorkflowStateChanged" event with "completed" status from the Orchestrator with CorrelationId
And I verify channel operational state is "Over"