@owner=v-sagaikwad @videoplatform @testplan=7081 @testsuite=16354 @tvporchestration @livenessreadiness @parallel=false
Feature: HealthChecksOrchestratorMicroservices
Health checks on the Orchestrator's microservices

@testcase=49331 @testsuite=49330 @priority=2 @priority2 @liveness 
Scenario Outline: Check liveness of Orchestrator Microservices
	Given I Check liveness of <service>
	Then I should get liveness <status> with response as <response>
	Examples: 
	| service | status | response |
	| vdaquac | OK     | Healthy  |
	| vdaquwd | OK     | Healthy  |
	| vdpoact | OK     | Healthy  |
	| vdpriac | OK     | Healthy  |
	| vdtvpac | OK     | Healthy  |
	| vdgmsin | OK     | Healthy  |
	| vdgmswd | OK     | Healthy  |
	| vdorche | OK     | Healthy  |
	| vdposer | OK     | Healthy  |
	| vdsched | OK     | Healthy  |
	| vdscser | OK     | Healthy  |
	| vdsmmkr | OK     | Healthy  |

@testcase=49332 @testsuite=49330 @priority=2 @priority2 @readiness 
Scenario Outline: Check readiness of Orchestrator Microservices
	Given I Check readiness of <service>
	Then I should get readiness <status> with response as <response>
	Examples: 
	| service | status | response |
	| vdaquac | OK     | Healthy  |
	| vdaquwd | OK     | Healthy  |
	| vdpoact | OK     | Healthy  |
	| vdpriac | OK     | Healthy  |
	| vdtvpac | OK     | Healthy  |
	| vdgmsin | OK     | Healthy  |
	| vdgmswd | OK     | Healthy  |
	| vdorche | OK     | Healthy  |
	| vdposer | OK     | Healthy  |
	| vdscser | OK     | Healthy  |
	| vdsmmkr | OK     | Healthy  |
	| vdsched | OK     | Healthy  |
