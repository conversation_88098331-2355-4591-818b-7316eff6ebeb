@owner=v-sagaik<PERSON>d @videoplatform @livetovod @testplan=7081 @testsuite=20879 @parallel=false
Feature: LiveToVod
Based on GMS Preview data, the media name property, set the live-to-vod flag

@testcase=20889 @version=4 @priority=1 @bvt @before=ClearEventsData() @after=DeleteTestDataFromBlob() @priority1
Scenario Outline: Check ProductionLiveToOnDemand flag is set correctly based on GMS interpreter configuration
When I create a "GmsGame" in GMS with "1" "NSS" Media Type with scheduled time and encoder as "1" and media id as "<media>".
And I trigger the GMS "Game" polling function
Then I verify that the "gms.GameUpdated" event is published
And I verify value is set to "<flag>"
Examples:
| media  | flag  |
| random | true  |
| fixed  | false |

@testcase=45832 @version=1 @priority=2 @bvt @before=ClearEventsData() @priority2 @testsuite=45831 @45125ac01
Scenario Outline: Validate LiveToOnDemand property is added to TvpProductionStateUpdated Event
When I create a "<EntityName>" in GMS with "1" "NSS" Media Type with scheduled time and encoder as "1" and media id as "<Media>".
And I trigger the GMS "<EntityType>" polling function
Then I verify whether value of ProductionLiveToOnDemand property of production in schedule is set to "<LiveToOnDemandValue>"
And I update TVP production operational state to "Verified"
And I verify channel operational state is "Verified"
And I should get "TvpProductionStateUpdate" event with "Verified" state and LiveToOnDemand property with value "<LiveToOnDemandValue>"
Examples:
| EntityType | EntityName | Media       | LiveToOnDemandValue |
| Game       | GmsGame    |  random     | true                |
| Event      | GmsEvent   |  random     | true                |
| Game       | GmsGame    |  fixed      | false               |
| Event      | GmsEvent   |  fixed      |false                |

@testcase=58496 @testsuite=58495 @priority=2 @before=ClearEventsData() @priority2 @57490ac01 @57490ac02
Scenario Outline: Verify reingestion of all the games and events of the day work if game/event is scheduled for today
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I trigger the GMS "<EntityName>" polling function
And I verify that the "VideoPlatformChannelsSetup" event is published
When I update "<EntityName>" DateTime to today eod
And I trigger GMS watchdog "ReingestLiveEventsForTodayAsync" function
Then I verify that the "VideoPlatformChannelsSetup" event is published
And I verify "VideoPlatformSchedule" are recreated
And I verify "GmsEntitlement" are recreated
And I verify "VideoPlatformChannel" are recreated
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |

@testcase=58497 @testsuite=58495 @version=1 @priority=2 @before=ClearEventsData() @priority2 @57490ac01 @57490ac02 @manual
Scenario Outline: Verify reingestion of all the games and events of the day doesn't work if game/event is not scheduled for today
Given I create a GMS "<EntityType>" in GMS with following medias
| MediaTypeName | MediaRegionName | MediaDistributionName | TeamContext | IsMediaActive | IsMediaScheduleActive |
| NSS           | Any             | Any                   | Any         | True          | True                  |
And I trigger the GMS "<EntityName>" polling function
And I verify that the "VideoPlatformChannelsSetup" event is published
When I get old entity creation time
And I trigger GMS watchdog "ReingestLiveEventsForTodayAsync" function
Then I verify "VideoPlatformSchedule" are not recreated
And I verify "GmsEntitlement" are not recreated
And I verify "VideoPlatformChannel" are not recreated
Examples:
| EntityType | EntityName |
| Game       | GmsGame    |
| Event      | GmsEvent   |