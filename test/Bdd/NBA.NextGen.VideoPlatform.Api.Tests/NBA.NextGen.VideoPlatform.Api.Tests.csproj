<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <DebugType>pdbonly</DebugType>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="TestData\Input\Credentials.xlsx" />
  </ItemGroup>

  <ItemGroup>

  </ItemGroup>

  <ItemGroup>
    <Content Include="TestData\Input\Credentials.xlsx">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <SpecFlowGeneratorPlugins Include="$(_BddCoreGeneratorPluginPath)" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Data.AppConfiguration" Version="1.1.0-beta.3" />
    <PackageReference Include="Azure.Data.Tables" Version="12.0.0" />
    <PackageReference Include="Azure.Storage.Files.Shares" Version="12.7.0" />
    <PackageReference Include="Bdd.Core" Version="3.1.3-preview" />
    <PackageReference Include="Bdd.Core.Generator.SpecFlowPlugin" Version="3.1.3-preview" />
    <PackageReference Include="Bdd.Core.Api" Version="3.1.3-preview" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="FluentAssertions.Json" Version="6.1.0" />
    <PackageReference Include="Microsoft.Azure.Cosmos" Version="3.19.0" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.9.4" />
    <PackageReference Include="Microsoft.Azure.Management.CosmosDB.Fluent" Version="1.37.1" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Core" Version="3.0.27" />
    <PackageReference Include="Microsoft.Azure.WebJobs.Extensions.EventGrid" Version="3.0.0-beta.3" />
    <PackageReference Include="NUnit3TestAdapter" Version="4.0.0">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="SpecFlow.NUnit" Version="3.9.74" />
    <PackageReference Include="StackExchange.Redis" Version="2.2.4" />
    <PackageReference Include="System.Collections" Version="4.3.0" />
    <PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>

  <ItemGroup>
    <Folder Include="TestData\Before\" />
    <Folder Include="TestData\After\" />
    <Folder Include="TestData\Output\" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Properties\AssemblyInfo.cs">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <None Update="appSettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="NBA.NextGen.VideoPlatform.Api.Tests.dev2.runsettings">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="NBA.NextGen.VideoPlatform.Api.Tests.devint2.runsettings">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="NBA.NextGen.VideoPlatform.Api.Tests.qa2.runsettings">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="NLog.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="Scripts\AddLeagueToGmsWatchDog.ps1">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="SpecFlow.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\AquilaStub\Channels.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\GmsEvent.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\GmsGame.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\GmsStub\Event.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\GmsStub\EventSchedules.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\GmsStub\Game.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\GmsStub\GameSchedules.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\MediaKindSeedData.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\MediaKindSeedData.csv">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\PostBody.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\Prisma\TeamZipsAndEsniResources.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\dev2\VideoPlatformPlayoutAsset.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\devint2\GmsEntitlementRules.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\devint2\VideoPlatformChannel.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\devint2\VideoPlatformPlayoutAsset.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\devint2\VideoPlatformSource.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\devint2\VideoPlatformTemplate.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\devint2\VideoPlatformWorkflow.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\dev2\GmsEntitlementRules.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\dev2\VideoPlatformChannel.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\dev2\VideoPlatformSource.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\dev2\VideoPlatformTemplate.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\dev2\VideoPlatformWorkflow.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\VideoPlatformPlayoutAsset.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\StreamMarkerRequest.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\GmsEntitlementRules.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\VideoPlatformChannel.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\VideoPlatformSource.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\VideoPlatformTemplate.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\SeedData\VideoPlatformWorkflow.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\ServiceBusMessageSchemas.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\Schedule.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\TVPStub.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\VideoPlatformSchedule.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\VideoPlatformSource.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="TestData\Input\VideoPlatformTemplate.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <PropertyGroup>
    <ShowTrace>true</ShowTrace>
    <OverwriteReadOnlyFiles>true</OverwriteReadOnlyFiles>
    <ForceGeneration>true</ForceGeneration>
    <VerboseOutput>true</VerboseOutput>
    <EnforceCodeStyleInBuild>true</EnforceCodeStyleInBuild>
  </PropertyGroup>

  <!--<Import Project="%userprofile%\.nuget\packages\SpecFlow\2.4.0\tools\TechTalk.SpecFlow.tasks" Condition="Exists('%userprofile%\.nuget\packages\SpecFlow\2.4.0\tools\TechTalk.SpecFlow.tasks')" />
  <Import Project="%userprofile%\.nuget\packages\SpecFlow\2.4.0\tools\TechTalk.SpecFlow.targets" Condition="Exists('%userprofile%\.nuget\packages\SpecFlow\2.4.0\tools\TechTalk.SpecFlow.targets')" />-->

  <Target Name="AfterUpdateFeatureFilesInProject">
    <ItemGroup>
      <Compile Include="Features\**\*.feature.cs" />
    </ItemGroup>
  </Target>

</Project>