// --------------------------------------------------------------------------------------------------------------------
// "//-----------------------------------------------------------------------".
// <copyright file="AssemblyInfo.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
// --------------------------------------------------------------------------------------------------------------------

using System;
using System.Resources;

using NUnit.Framework;

using TechTalk.SpecFlow;

[assembly: Parallelizable(ParallelScope.Fixtures)] // NUnit
[assembly: LevelOfParallelism(2)]
[assembly: CLSCompliant(false)]
[assembly: NeutralResourcesLanguage("en-US")]
