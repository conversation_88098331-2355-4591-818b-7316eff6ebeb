// "//-----------------------------------------------------------------------".
// <copyright file="AquilaActorSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;
    using AventStack.ExtentReports.Utils;
    using Bdd.Core.DataSources;
    using Bdd.Core.Utils;

    using Flurl;
    using Flurl.Http;
    using NBA.NextGen.VideoPlatform.Api.Tests.DataSources;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    /// <summary>
    /// AquilaActorSteps step-definition class.
    /// </summary>
    [Binding]
    public class AquilaActorSteps : ApiStepDefinitionBase
    {
        private readonly ServiceBusDataOperations serviceBusOps;
        private readonly JsonDataSource jsonDataSource;
        private readonly BlobStorageDataSource blobStorage;
        private readonly EntityData entityData;
        private readonly ApplicationInsightsDataSource appInsights;
        private dynamic response;
        private string channelId;
        private string desiredState;
        private string currentState;
        private string channelName;
        private CosmosDBDataSource cosmosDBDataSource;

        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaActorSteps"/> class.
        /// </summary>
        /// <param name="jsonDataSource">jsonDataSource.</param>
        /// <param name="serviceBusOps">serviceBusOps.</param>
        /// <param name="blobStorage">blobStorage.</param>
        /// <param name="entityData">entityData.</param>
        /// <param name="appInsights">appInsights.</param>
        /// <param name="cosmosDBDataSource">CosmosDbDatasource.</param>
        public AquilaActorSteps(JsonDataSource jsonDataSource, ServiceBusDataOperations serviceBusOps, BlobStorageDataSource blobStorage, EntityData entityData, ApplicationInsightsDataSource appInsights, CosmosDBDataSource cosmosDBDataSource)
        {
            this.jsonDataSource = jsonDataSource;
            this.serviceBusOps = serviceBusOps;
            this.blobStorage = blobStorage;
            this.entityData = entityData;
            this.appInsights = appInsights;
            this.cosmosDBDataSource = cosmosDBDataSource;
        }

        /// <summary>
        /// Sends a get request to aquilla channel workflow function by specifying invalid function key.
        /// </summary>
        /// <returns>Task.</returns>
        [When(@"I trigger Aquila actor ChannelWorkflow function with invalid key")]
        public async Task WhenITriggerAquilaActorChannelWorkflowFunctionWithInvalidKey()
        {
            object invalidKey = UtilityMethods.GetRandomString();
            var header = new Dictionary<string, object>() { { "x-functions-key", invalidKey } };
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.AquilaActorFunctionAppUrl), header).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if response has 401 status code.
        /// </summary>
        [Then(@"I should get unauthorized message for Aquila actor ChannelWorkflow function")]
        public void ThenIShouldGetUnauthorizedMessageForAquilaActorChannelWorkflowFunction()
        {
            this.VerifyThat(() => Assert.AreEqual("401", this.response.StatusCode.ToString()));
        }

        /// <summary>
        /// sends a get request to aquila channel workflow function with specified desired state and channel id.
        /// </summary>
        /// <param name="desiredState">desired state.</param>
        /// <returns>Task.</returns>
        [When(@"I trigger Aquila actor ChannelWorkflow function with incorrect ""(.*)"" and Channel ID")]
        [When(@"I trigger Aquila actor ChannelWorkflow function with ""(.*)""and Channel ID")]
        public async Task WhenITriggerAquilaActorChannelWorkflowFunctionWithAndChannelID(string desiredState)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            this.desiredState = desiredState;
            this.ScenarioContext["eventTimeStamp"] = DateTime.UtcNow.AddSeconds(-1).Ticks;
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.AquilaActorFunctionAppUrl).SetQueryParam("channelId", this.channelId).SetQueryParam("desiredState", desiredState), this.AquilaactorFunctionHeader).ConfigureAwait(false);
            this.VerifyThat(() => Assert.AreEqual(Constants.NoContent, this.response.StatusCode.ToString()));
            await Task.Delay(1000).ConfigureAwait(false);
        }

        /// <summary>
        /// given i have a channel id and its specified state from aquila.
        /// </summary>
        /// <param name="state">state.</param>
        /// <returns>Task.</returns>
        [Given(@"I have a Channel ID and its ""(.*)"" from Aquila")]
        public async Task GivenIHaveAChannelIDAndItsFromAquila(string state)
        {
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeUpdate"))
            {
                this.ScenarioContext["blobBeforeUpdate"] = jsonContent;
                this.ScenarioContext["entityUpdated"] = "Channel";
            }

            this.currentState = state;
            JObject json = JObject.Parse(jsonContent);
            JArray channels = (JArray)json["channels"];
            int index = UtilityMethods.GetRandomNumber(0, channels.Count);
            for (int i = 0; i < channels.Count; i++)
            {
                if (channels[i]["state"].ToString() == state)
                {
                    this.channelId = channels[i]["id"].ToString();
                    if (i % (index + 1) == 0)
                    {
                        break;
                    }
                }
            }

            this.ScenarioContext["tableName"] = Constants.AquilaActorTableStorageName;
            this.ScenarioContext["partitionKey"] = $"AquilaActor{this.channelId}";
            this.ScenarioContext["rowKey"] = string.Empty;
            this.ScenarioContext["service"] = "Aquila";
            this.ScenarioContext["channelId"] = this.channelId;
        }

        [StepDefinition(@"I create Channel with ""(.*)"" and add Channel ID to start and stop in Aquila")]
        public async Task WhenICreateChannelWithAddTheChannelIDToStartAndStopInAquila(string state)
        {
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeUpdate"))
            {
                this.ScenarioContext["blobBeforeUpdate"] = jsonContent;
                this.ScenarioContext["entityUpdated"] = "Channel";
            }

            JObject json = JObject.Parse(jsonContent);
            JArray channels = (JArray)json["channels"];
            this.channelId = Guid.NewGuid().ToString();
            Console.WriteLine($"Channel: {this.channelId}");
            string channel = @"{'template':'" + Guid.NewGuid() + "','publishPoint':'ProfileEdit','backupProgramNumber':0,'name':'" + this.channelId.ToString().Split('-')[0] + "','programNumber':0,'monitor':false,'id':'" + this.channelId + "','source':'" + Guid.NewGuid() + "','state':'" + state + "','timeStateLastChanged':'" + DateTime.UtcNow.ToString("s") + "Z" + "','region':'eastus2','backupSource':''}";
            channels.Add(JObject.Parse(channel));

            JArray startArray = (JArray)json["start"];
            JArray stopArray = (JArray)json["stop"];
            startArray[0]["id"] = this.channelId;
            stopArray[0]["id"] = this.channelId;
            jsonContent = json.ToString();
            await this.UploadStringContentToBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
            await Task.Delay(60000).ConfigureAwait(false);
            this.ScenarioContext["tableName"] = Constants.AquilaActorTableStorageName;
            this.ScenarioContext["partitionKey"] = $"AquilaActor{this.channelId}";
            this.ScenarioContext["rowKey"] = string.Empty;
            this.ScenarioContext["service"] = "Aquila";
            this.ScenarioContext["channelId"] = this.channelId;
        }

        /// <summary>
        /// Adds the channel id to start & stop in aquila by using blob operations to read & modify.
        /// </summary>
        /// <returns>Task.</returns>
        [When(@"I add the Channel ID to start and stop in Aquila")]
        public async Task WhenIAddTheChannelIDToStartAndStopInAquila()
        {
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName).ConfigureAwait(false);
            JObject json = JObject.Parse(jsonContent);
            JArray startArray = (JArray)json["start"];
            JArray stopArray = (JArray)json["stop"];
            startArray[0]["id"] = this.channelId;
            stopArray[0]["id"] = this.channelId;
            jsonContent = json.ToString();

            await this.UploadStringContentToBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        /// <summary>
        /// update the state of the channel to specified state using blob operations.
        /// </summary>
        /// <param name="state">state.</param>
        /// <returns>Task.</returns>
        [When(@"I update the state of the Channel to ""(.*)"" in Aquila")]
        public async Task WhenIUpdateTheStateOfTheChannelToInAquila(string state)
        {
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName).ConfigureAwait(false);
            JObject json = JObject.Parse(jsonContent);
            JArray channels = (JArray)json["channels"];

            for (int i = 0; i < channels.Count; i++)
            {
                if (channels[i]["id"].ToString() == this.channelId)
                {
                    channels[i]["state"] = state;
                    break;
                }
            }

            jsonContent = json.ToString();
            await this.UploadStringContentToBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
        }

        /// <summary>
        /// validates if specified event is published for updated channel in event grid.
        /// </summary>
        /// <param name="eventName">event name.</param>
        /// <returns>The Task.</returns>
        [Then(@"I verify ""(.*)"" event is published for updated Channel in Event Grid")]
        public async Task ThenIVerifyEventIsPublishedForUpdatedChannelInEventGrid(string eventName)
        {
            int count = 4;
            int published = await this.ReadEventsAndCheckIfEventIsPublished(eventName, this.ScenarioContext["eventTimeStamp"]).ConfigureAwait(false);
            while (count > 0 && published != 1)
            {
                published = await this.ReadEventsAndCheckIfEventIsPublished(eventName, this.ScenarioContext["eventTimeStamp"]).ConfigureAwait(false);
                count = count - 1;
            }

            Assert.AreEqual(1, published, $"{eventName} published count is not matching");
        }

        /// <summary>
        /// validates if specified event is not published for updated channel in event grid.
        /// </summary>
        /// <param name="eventName">event name.</param>
        /// <returns>The Task.</returns>
        [Then(@"I verify ""(.*)"" event is not published for updated Channel in Event Grid")]
        public async Task ThenIVerifyEventIsNotPublishedForUpdatedChannelInEventGrid(string eventName)
        {
            int published = await this.ReadEventsAndCheckIfEventIsPublished(eventName, this.ScenarioContext["eventTimeStamp"]).ConfigureAwait(false);
            Assert.AreEqual(0, published);
        }

        /// <summary>
        /// Drop channel Id Request in aquila service bus queue.
        /// </summary>
        /// <param name="workflow">workflow.</param>
        /// <returns>Task.</returns>
        [When(@"I send ""(.*)"" workflow request message to Aquila actor")]
        public async Task WhenIDropAChannelRequestWorkflowMessageInIscrAquilaChannelsServiceBusQueue(string workflow)
        {
            if (workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureStart)) || workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureCleanup)))
            {
                if (Configs.RunWithMocking)
                {
                    string arrayName = workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureStart)) ? "start" :
                        "stop";
                    await this.AddChannelToStartOrStopInAquilaStub(arrayName).ConfigureAwait(false);
                }
            }

            JObject channelRequestJsonString = new JObject();
            var testDataJsonTotal = await this.jsonDataSource.ReadAsync<JObject>(this.ServiceBusMessageSchemasJsonFilePath.GetFullPath()).ConfigureAwait(false);
            var testDataJson = testDataJsonTotal["ChannelSetupOrCleanupRequest"].ToString();
            this.channelId = this.ScenarioContext["channelId"].ToString();
            if (workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureSetup)))
            {
                this.ScenarioContext["mkAquilaBaseEndpointUrl"] = Configs.MKAquilaBaseEndpointUrl;
                this.ScenarioContext["subscriptionKey"] = this.ApimHeader;

                await this.DropSetupChannelMessageInServiceBusQueue(this.channelId).ConfigureAwait(false);
            }
            else if (workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureCleanup)))
            {
                string requestId = Guid.NewGuid().ToString();
                string longRunningOpId = Guid.NewGuid().ToString();
                string infrastructureId = this.ScenarioContext["channelId"].ToString();

                var actorData = JObject.Parse("{ChannelId:'" + this.channelId + "'}");
                var testDataJsonString = JObject.Parse(testDataJson.ToString());
                var actorDataJsonString = testDataJsonString["ActorSpecificDetail"];
                actorDataJsonString["Data"] = actorData;
                testDataJson = testDataJsonString.ToString();
                channelRequestJsonString = JObject.Parse(testDataJson.ToString().ReplaceIgnoreCase("{requestId}", requestId).ReplaceIgnoreCase("{longrunningoperationId}", longRunningOpId).ReplaceIgnoreCase("{infraId}", infrastructureId).ReplaceIgnoreCase("{externalInfraId}", infrastructureId).ReplaceIgnoreCase("{workflowId}", nameof(Workflows.EventInfrastructureCleanup)).ReplaceIgnoreCase("{desiredState}", Constants.ChannelCleanupDesiredState).ReplaceIgnoreCase("{correlationId}", this.ScenarioContext["correlationId"].ToString()));

                await this.serviceBusOps.WriteAllAsync(Configs.AquilaInfrastructureStateChangeRequestQueue, channelRequestJsonString).ConfigureAwait(false);
                await Task.Delay(5000).ConfigureAwait(false);

                if (Configs.RunWithMocking)
                {
                    await this.ChangeChannelState("stopped").ConfigureAwait(false);
                    await this.ExecuteChannelPollingFunction().ConfigureAwait(false);
                }
            }
            else if (workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureStart)))
            {
                string requestId = Guid.NewGuid().ToString();
                string longRunningOpId = Guid.NewGuid().ToString();
                string infrastructureId = this.ScenarioContext["channelId"].ToString();

                var actorData = JObject.Parse("{ChannelId:'" + this.channelId + "'}");
                var testDataJsonString = JObject.Parse(testDataJson.ToString());
                var actorDataJsonString = testDataJsonString["ActorSpecificDetail"];
                actorDataJsonString["Data"] = actorData;
                testDataJson = testDataJsonString.ToString();
                channelRequestJsonString = JObject.Parse(testDataJson.ToString().ReplaceIgnoreCase("{requestId}", requestId).ReplaceIgnoreCase("{longrunningoperationId}", longRunningOpId).ReplaceIgnoreCase("{infraId}", infrastructureId).ReplaceIgnoreCase("{externalInfraId}", infrastructureId).ReplaceIgnoreCase("{workflowId}", nameof(Workflows.EventInfrastructureStart)).ReplaceIgnoreCase("{desiredState}", Constants.ChannelStartDesiredState).ReplaceIgnoreCase("{correlationId}", this.ScenarioContext["correlationId"].ToString()));

                await this.serviceBusOps.WriteAllAsync(Configs.AquilaInfrastructureStateChangeRequestQueue, channelRequestJsonString).ConfigureAwait(false);
                await Task.Delay(5000).ConfigureAwait(false);

                if (Configs.RunWithMocking)
                {
                    await this.ChangeChannelState("started").ConfigureAwait(false);
                    await this.ExecuteChannelPollingFunction().ConfigureAwait(false);
                }
            }
        }

        /// <summary>
        /// Verify channel status in MK Aquila.
        /// </summary>
        /// <param name="operation">operation.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I verify whether the channnel is ""(.*)"" in Aquila")]
        public async Task ThenIVerifyWhetherTheChannnelIsInAquila(string operation)
        {
            if (!Configs.RunWithMocking)
            {
                this.channelId = this.ScenarioContext["channelId"].ToString();
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl, $"{KeyVaultSecrets.MKHubAccountId}/channels/{this.channelId}", $"?{this.ApimHeader}");
                var response = await this.SendGetRequestAsync(url).ConfigureAwait(false);

                if (operation.EqualsIgnoreCase("created"))
                {
                    this.VerifyThat(() => Assert.AreEqual(response.StatusCode.ToString(), Constants.HttpOKResponse, "The channel is not created in MK Aquila"));
                }
                else if (operation.EqualsIgnoreCase("deleted"))
                {
                    await Task.Delay(180000).ConfigureAwait(false);
                    this.VerifyThat(() => Assert.AreEqual(response.StatusCode.ToString(), Constants.HttpBadRequestErrorResponse, "The channel is not deleted in MK Aquila"));
                }
                else if (operation.EqualsIgnoreCase("updated"))
                {
                    var responseContent = await ((HttpResponseMessage)response).Content.ReadAsStringAsync().ConfigureAwait(false);
                    this.VerifyThat(() => Assert.AreEqual(response.StatusCode.ToString(), Constants.HttpOKResponse, "The channel is not created in MK Aquila"));
                    JObject channels = JObject.Parse(responseContent);
                    this.VerifyThat(() => Assert.AreEqual(channels["name"].ToString(), this.channelName, "The channel is not updated in MK Aquila"));
                }
                else if (operation.EqualsIgnoreCase("started"))
                {
                    await Task.Delay(480000).ConfigureAwait(false);
                    var responseContent = await ((HttpResponseMessage)response).Content.ReadAsStringAsync().ConfigureAwait(false);
                    this.VerifyThat(() => Assert.AreEqual(response.StatusCode.ToString(), Constants.HttpOKResponse, "The channel is not created in MK Aquila"));
                    JObject channels = JObject.Parse(responseContent);
                    this.VerifyThat(() => Assert.AreEqual("started", channels["state"].ToString(), "The channel is not started in MK Aquila"));
                }
            }
        }

        /// <summary>
        /// Drop an existing dynamic channel Id request in aquila service bus queue.
        /// </summary>
        /// <param name="workflow">workflow.</param>
        /// <returns>Task.</returns>
        [When(@"I send ""(.*)"" channel request workflow message to Aquila actor with same channel id and different payload")]
        public async Task WhenIDropAChannelRequestWorkflowMessageInIscrAquilaChannelsServiceBusQueueWithSameChannelIdAndDifferentPayload(string workflow)
        {
            if (workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureSetup)))
            {
                await this.DropSetupChannelMessageInServiceBusQueue(this.channelId).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Verify channel is not created in MK Aquila.
        /// </summary>
        /// <param name="operation">operation.</param>
        /// <returns>Task.</returns>
        [Then(@"I verify whether the channnel is not ""(.*)"" in Aquila")]
        public async Task ThenIVerifyWhetherTheChannnelIsNotInAquila(string operation)
        {
            if (operation.EqualsIgnoreCase("created"))
            {
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl, $"{KeyVaultSecrets.MKHubAccountId}/channels/{this.ScenarioContext["channelId"].ToString()}", $"?{this.ApimHeader}");
                var response = await this.SendGetRequestAsync(url).ConfigureAwait(false);
                this.VerifyThat(() => Assert.AreNotEqual(response.StatusCode.ToString(), Constants.HttpOKResponse, "The channel is created in MK Aquila"));
            }
        }

        /// <summary>
        /// Drop invalid channel Ids aquila service bus queue.
        /// </summary>
        /// <param name="operation">operation.</param>
        /// <param name="channelId">channelId.</param>
        /// <returns>Task.</returns>
        [When(@"I send ""(.*)"" workflow request message to Aquila actor with channelId as ""(.*)""")]
        public async Task WhenIDropAChannelRequestWorkflowMessageInIscrAquilaChannelsServiceBusQueueWithChannelIdAs(string operation, string channelId)
        {
            if (operation.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureSetup)))
            {
                this.channelId = channelId;
                this.ScenarioContext["channelId"] = this.channelId;
                this.ScenarioContext["gameId"] = UtilityMethods.GetRandomString().ToLower();
                this.ScenarioContext["correlationId"] = this.ScenarioContext["gameId"].ToString() + UtilityMethods.GetRandomNumber(1000, 10000);
                await this.DropSetupChannelMessageInServiceBusQueue(channelId).ConfigureAwait(false);
            }
        }

        [StepDefinition(@"I add Channel details in Aquila stub")]
        [StepDefinition(@"I have channel present in Aquila")]
        public async Task ThenIAddChannelDetailsInAquilaStub()
        {
            var blobName = $"Clients/Aquila/Channels/data.json";
            string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobStorage, Configs.StubContainer, blobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeCreation"))
            {
                this.ScenarioContext["blobBeforeCreation"] = jsonContent;
                this.ScenarioContext["entityCreated"] = nameof(CosmosContainers.Channel);
                this.ScenarioContext["entityPartitionKeyValue"] = nameof(CosmosContainers.Channel);
            }

            JObject content = JObject.Parse(jsonContent);
            JArray entities = JArray.Parse(content["channels"].ToString());
            JObject entityObject = this.entityData.GetEntityData(nameof(CosmosContainers.Channel));

            this.ScenarioContext["entityId"] = this.ScenarioContext["channelId"];
            entityObject["id"] = this.ScenarioContext["channelId"].ToString();
            entityObject["state"] = "stopped";
            entityObject["name"] = UtilityMethods.GetRandomString();
            entities.Add(entityObject);
            content["channels"] = entities;
            jsonContent = content.ToString();

            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
            this.ScenarioContext["tableName"] = Constants.AquilaActorTableStorageName;
            this.ScenarioContext["partitionKey"] = $"AquilaActor{this.ScenarioContext["channelId"].ToString()}";
            this.ScenarioContext["rowKey"] = string.Empty;
            this.ScenarioContext["service"] = "Aquila";
        }

        /// <summary>
        /// Create random gameid and channelid.
        /// </summary>
        /// <returns>Task.</returns>
        [Given(@"I have a gameId and channelId")]
        public async Task GivenIHaveAChannelId()
        {
            if (this.ScenarioContext.ContainsKey("GameInfo"))
            {
                var gameInfo = this.ScenarioContext.Get<Dictionary<string, string>>("GameInfo");
                this.ScenarioContext["gameId"] = gameInfo["GameId"];
                this.ScenarioContext["channelId"] = gameInfo["ChannelId"];
                this.ScenarioContext["correlationId"] = $"{gameInfo["GameId"]}{UtilityMethods.GetRandomNumber(0, 1000).ToString()}";
            }
            else
            {
                this.ScenarioContext["gameId"] = this.GetUniqueGameId();
                this.ScenarioContext["channelId"] = $"g{this.ScenarioContext["gameId"].ToString() + UtilityMethods.GetRandomString().ToLower()}";
                this.ScenarioContext["correlationId"] = this.ScenarioContext["gameId"] + UtilityMethods.GetRandomNumber(0, 1000).ToString();
            }

            if (Configs.RunWithMocking)
            {
                await this.AddAquilaStub().ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Create random entity and channelid.
        /// </summary>
        /// <param name="entity">entity.</param>
        [Given(@"I have a ""(.*)"" Id and channelId")]
        public void GivenIHaveAAndChannelId(string entity)
        {
            this.ScenarioContext["entityId"] = this.GetUniqueGameId();
            if (entity.EqualsIgnoreCase(nameof(CosmosContainers.GmsGame)))
            {
                this.ScenarioContext["channelId"] = $"g{this.ScenarioContext["entityId"].ToString()}{UtilityMethods.GetRandomString().ToLower()}";
            }
            else if (entity.EqualsIgnoreCase(nameof(CosmosContainers.GmsEvent)))
            {
                this.ScenarioContext["channelId"] = $"e{this.ScenarioContext["entityId"].ToString()}{UtilityMethods.GetRandomString().ToLower()}";
            }

            this.ScenarioContext["correlationId"] = this.ScenarioContext["entityId"] + UtilityMethods.GetRandomNumber(0, 1000).ToString();
        }

        /// <summary>
        /// Send Request to Aquila actor with empty field.
        /// </summary>
        /// <param name="workflow">workflow.</param>
        /// <param name="field">field.</param>
        /// <returns>Task.</returns>
        [When(@"I send a ""(.*)"" channel request workflow message in Aquila actor with ""(.*)"" value as empty")]
        public async Task WhenISendAChannelRequestWorkflowMessageInAquilaActorWithValueAsEmpty(string workflow, string field)
        {
            Logger.Info(workflow);
            string fieldValue;

            switch (field)
            {
                case Constants.Template:
                    {
                        fieldValue = "TemplateName";
                        break;
                    }

                case Constants.MainSource:
                    {
                        fieldValue = "MainSourceName";
                        break;
                    }

                case Constants.BackupSource:
                    {
                        fieldValue = "BackupSourceName";
                        break;
                    }

                default:
                    {
                        fieldValue = string.Empty;
                        break;
                    }
            }

            var channelRequestJsonString = new JObject();
            var testDataJsonTotal = await this.jsonDataSource.ReadAsync<JObject>(this.ServiceBusMessageSchemasJsonFilePath.GetFullPath()).ConfigureAwait(false);
            var testDataJson = testDataJsonTotal["ChannelSetupOrCleanupRequest"].ToString();

            string requestId = Guid.NewGuid().ToString();
            this.channelName = this.ScenarioContext["channelId"].ToString();
            string longRunningOpId = Guid.NewGuid().ToString();
            string infrastructureId = this.ScenarioContext["channelId"].ToString();

            var actorData = JArray.Parse(testDataJsonTotal["AquilaActorData"].ToString().Replace("{ChannelId}", this.ScenarioContext["channelId"].ToString(), StringComparison.OrdinalIgnoreCase).Replace("{ChannelName}", this.channelName, StringComparison.OrdinalIgnoreCase));
            if (!field.EqualsIgnoreCase(Constants.Template))
            {
                actorData[0]["Sources"][0][fieldValue] = string.Empty;
            }
            else
            {
                actorData[0][fieldValue] = string.Empty;
            }

            var testDataJsonString = JObject.Parse(testDataJson.ToString());
            var actorDataJsonString = testDataJsonString["ActorSpecificDetail"];
            actorDataJsonString["Data"] = actorData;
            testDataJson = testDataJsonString.ToString();
            channelRequestJsonString = JObject.Parse(testDataJson.ToString().Replace("{requestId}", requestId, StringComparison.OrdinalIgnoreCase).Replace("{longrunningoperationId}", longRunningOpId, StringComparison.OrdinalIgnoreCase).Replace("infraId}", this.ScenarioContext["entityId"].ToString(), StringComparison.OrdinalIgnoreCase).Replace("{externalInfraId}", this.ScenarioContext["entityId"].ToString(), StringComparison.OrdinalIgnoreCase).Replace("{workflowId}", nameof(Workflows.EventInfrastructureSetup), StringComparison.OrdinalIgnoreCase).Replace("{desiredState}", Constants.ChannelSetupDesiredState, StringComparison.OrdinalIgnoreCase).Replace("{correlationId}", this.ScenarioContext["correlationId"].ToString(), StringComparison.Ordinal));

            await this.serviceBusOps.WriteAllAsync(Configs.AquilaInfrastructureStateChangeRequestQueue, channelRequestJsonString).ConfigureAwait(false);
            await Task.Delay(8000).ConfigureAwait(false);
        }

        /// <summary>
        /// Reads message from AppInsights.
        /// </summary>
        /// <returns>Task.</returns>
        [Then(@"I validate whether expected error message is logged")]
        public async Task IValidateWhetherExpectedErrorMessageIsLogged()
        {
            await Task.Delay(120000).ConfigureAwait(false);

            // var result = await this.appInsights.ReadAllAsync<JObject>("union isfuzzy=true exceptions | where outerMessage has 'Activity function 'CreateChannelEvent' failed: Sequence contains no matching' and timestamp >ago(5m)").ConfigureAwait(false);
            Dictionary<string, string> queries = new Dictionary<string, string>();
            queries.Add("$search", "CreateChannelEvent");
            queries.Add("timespan", "PT4M");
            var result = await this.appInsights.ReadInternalAsync<JObject>("events/exceptions", queries).ConfigureAwait(false);
            Assert.True(result.Last.ToString().ContainsIgnoreCase(string.Format(ExceptionMessages.AquilaChannelCreationFailureMsgInAppInsights, this.ScenarioContext["channelId"].ToString(), this.ScenarioContext["channelId"].ToString(), this.ScenarioContext["InvalidTemplate"].ToString())));
        }

        /// <summary>
        /// Drop invalid message to aquila service bus queue.
        /// </summary>
        /// <param name="workflow">workflow.</param>
        /// <returns>Task.</returns>
        [When(@"I send ""(.*)"" workflow request message to Aquila actor with invalid channel details")]
        public async Task WhenISendWorkflowRequestMessageToAquilaActorWithInvalidChannelDetails(string workflow)
        {
            if (workflow.EqualsIgnoreCase(nameof(Workflows.EventInfrastructureSetup)))
            {
                var channelRequestJsonString = new JObject();
                var testDataJsonTotal = await this.jsonDataSource.ReadAsync<JObject>(this.ServiceBusMessageSchemasJsonFilePath.GetFullPath()).ConfigureAwait(false);
                var testDataJson = testDataJsonTotal["ChannelSetupOrCleanupRequest"].ToString();

                string requestId = Guid.NewGuid().ToString();
                this.channelName = this.ScenarioContext["channelId"].ToString();
                string longRunningOpId = Guid.NewGuid().ToString();
                string infrastructureId = this.ScenarioContext["channelId"].ToString();
                this.ScenarioContext["InvalidTemplate"] = "NBA_Ca_1080i";

                var actorData = JArray.Parse(testDataJsonTotal["AquilaActorData"].ToString().Replace("{ChannelId}", this.ScenarioContext["channelId"].ToString(), StringComparison.OrdinalIgnoreCase).Replace("{ChannelName}", this.ScenarioContext["channelId"].ToString(), StringComparison.OrdinalIgnoreCase).ToString().Replace("NBA_1080i_Event", this.ScenarioContext["InvalidTemplate"].ToString(), StringComparison.Ordinal));
                var testDataJsonString = JObject.Parse(testDataJson.ToString());
                var actorDataJsonString = testDataJsonString["ActorSpecificDetail"];
                actorDataJsonString["Data"] = actorData;
                testDataJson = testDataJsonString.ToString();
                channelRequestJsonString = JObject.Parse(testDataJson.ToString().ReplaceIgnoreCase("{requestId}", requestId).ReplaceIgnoreCase("{longrunningoperationId}", longRunningOpId).ReplaceIgnoreCase("{infraId}", this.ScenarioContext["gameId"].ToString()).ReplaceIgnoreCase("{externalInfraId}", this.ScenarioContext["gameId"].ToString()).ReplaceIgnoreCase("{workflowId}", nameof(Workflows.EventInfrastructureSetup)).ReplaceIgnoreCase("{desiredState}", Constants.ChannelSetupDesiredState).ReplaceIgnoreCase("{correlationId}", this.ScenarioContext["correlationId"].ToString()));

                await this.serviceBusOps.WriteAllAsync(Configs.AquilaInfrastructureStateChangeRequestQueue, channelRequestJsonString).ConfigureAwait(false);
                await Task.Delay(8000).ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Read critical event from Appinsights when Aquila template/mainsource is empty.
        /// </summary>
        /// <returns>Task.</returns>
        [Then(@"I validate whether a critical event with expected error message is logged")]
        public async Task ThenIValidateWhetherACriticalEventWithExpectedErrorMessageIsLogged()
        {
            await Task.Delay(180000).ConfigureAwait(false);

            string expectedErrorMsg = string.Empty;
            string query = string.Format(AppInsightsQueries.GetCriticalEvent, this.ScenarioContext["channelId"].ToString());

            var result = await this.appInsights.ReadAsync<JObject>(query).ConfigureAwait(false);
            var errorMsgBody = JArray.Parse(result["tables"][0]["rows"].ToString()).Children();
            var actualErrorMsg = errorMsgBody.First<JToken>()[1].ToString();

            if (this.ScenarioContext.ScenarioInfo.Title.ContainsIgnoreCase(Constants.Template))
            {
                expectedErrorMsg = string.Format(ExceptionMessages.AquilaEmptyTemplateErrorMsg, this.ScenarioContext["channelId"].ToString());
            }
            else if (this.ScenarioContext.ScenarioInfo.Title.ContainsIgnoreCase(Constants.MainSource))
            {
                expectedErrorMsg = string.Format(ExceptionMessages.AquilaEmptyMainSourceErrorMsg, this.ScenarioContext["channelId"].ToString());
            }

            this.VerifyThat(() => Assert.AreEqual(expectedErrorMsg, actualErrorMsg, "No critical event with expected error message is logged."));
        }

        /// <summary>
        /// Read warning log from Appinsights when Aquila backupsource is empty.
        /// </summary>
        /// <returns>Task.</returns>
        [Then(@"I validate whether expected error message as warning is logged")]
        public async Task ThenIValidateWhetherExpectedErrorMessageAsWarningIsLogged()
        {
            await Task.Delay(180000).ConfigureAwait(false);
            string query = string.Format(AppInsightsQueries.GetWarningEvent, this.ScenarioContext["channelId"].ToString());
            var result = await this.appInsights.ReadAsync<JObject>(query).ConfigureAwait(false);
            var errorMsgBody = JArray.Parse(result["tables"][0]["rows"].ToString()).Children();
            var actualErrorMsg = errorMsgBody.First<JToken>()[1].ToString();
            string expectedErrorMsg = string.Format(ExceptionMessages.AquilaEmptyBackupSourceErrorMsg, this.ScenarioContext["channelId"].ToString());
            this.VerifyThat(() => Assert.AreEqual(expectedErrorMsg, actualErrorMsg, "No warning with expected error message is logged."));
        }

        /// <summary>
        /// Delete Aquila channel from MK aquila.
        /// </summary>
        /// <returns>Task.</returns>
        [Then(@"I delete Aquila channel from MK Aquila")]
        public async Task ThenIDeleteAquilaChannelFromMKAquila()
        {
            await Task.Delay(1000).ConfigureAwait(false);
            if (!Configs.RunWithMocking)
            {
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl.ToString(), $"{KeyVaultSecrets.MKHubAccountId}/channels/{this.ScenarioContext["channelId"].ToString()}", $"?{this.ApimHeader}");

                var response = await url.DeleteAsync().ConfigureAwait(false);
            }
        }

        /// <summary>
        /// Verify channel state.
        /// </summary>
        /// <param name="state">state.</param>
        /// /// <returns>Task.</returns>
        [StepDefinition(@"I verify channel operational state is ""(.*)""")]
        public async Task ThenIVerifyChannelStateIs(string state)
        {
            var containerName = this.GetGmsContainerNameOrPartitionKeyValue(nameof(CosmosContainers.VideoPlatformChannel));
            var field = "OperationalState";

            var documentId = this.ScenarioContext["channelId"];
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, field, documentId), null, Configs.CosmosDbName, containerName).ConfigureAwait(false);
            if (!itemData.ToList().Any())
            {
                itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, field, documentId), null, Configs.CosmosDbName, containerName).ConfigureAwait(false);
            }

            if (!state.EqualsIgnoreCase("init"))
            {
                this.VerifyThat(() => Assert.True(state.EqualsIgnoreCase(itemData.ToList()[0].ToDictionary()[field].ToString()), $"OperationalState is not updated to {state}"));
            }
            else
            {
                var value = itemData.ToList()[0].ToDictionary()[field]?.ToString() ?? string.Empty;
                var actualState = value.IsNullOrEmpty() ? "Init" : itemData.ToList()[0].ToDictionary()[field].ToString();
                this.VerifyThat(() => actualState.ToString().EqualsIgnoreCase("Init"));
            }
        }

        /// <summary>
        /// Verify channel state.
        /// </summary>
        /// <param name="isOpen">isOpen.</param>
        /// /// <returns>Task.</returns>
        [Then(@"I verify channel Scte35ListeningWindow is ""([^""]*)""")]
        public async Task ThenIVerifyChannelScteListeningWindowIs(bool isOpen)
        {
            var containerName = this.GetGmsContainerNameOrPartitionKeyValue(nameof(CosmosContainers.VideoPlatformChannel));
            var field = "Scte35ListeningWindowEnabled";

            var documentId = this.ScenarioContext["channelId"];
            var itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, field, documentId), null, Configs.CosmosDbName, containerName).ConfigureAwait(false);
            if (!itemData.ToList().Any())
            {
                itemData = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetFieldValueInADocumentById, field, documentId), null, Configs.CosmosDbName, containerName).ConfigureAwait(false);
            }

            bool currentState = itemData.ToList()[0].ToDictionary()[field] is null ? false : bool.Parse(itemData.ToList()[0].ToDictionary()[field].ToString());

            this.VerifyThat(() => Assert.AreEqual(isOpen, currentState, $"Scte35ListeningWindowEnabled is not updated to {isOpen}"));
        }

        [Then(@"I validate whether the created Aquila channel is not created in Aquila")]
        public async Task ThenIValidateWhetherTheCreatedAquilaChannelIsNotCreatedInAquila()
        {
            if (!Configs.RunWithMocking)
            {
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl, $"{KeyVaultSecrets.MKHubAccountId}/channels/{this.ScenarioContext["channelId"]}", $"?{this.ApimHeader}");
                var response = await this.SendGetRequestAsync(url).ConfigureAwait(false);
                this.VerifyThat(() => Assert.AreNotEqual(Constants.HttpOKResponse, response.StatusCode.ToString(), "The channel is created in MK Aquila"));
            }
        }

        [Then(@"I verify channel contains multi region instances")]
        public async Task ThenIVerifyChannelContainsMultiRegionInstances()
        {
            if (!Configs.RunWithMocking)
            {
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl, $"{KeyVaultSecrets.MKHubAccountId}/channels/{this.ScenarioContext["channelId"]}", $"?{this.ApimHeader}");
                var response = await this.SendGetRequestAsync(url).ConfigureAwait(false);
                this.VerifyThat(() => Assert.AreNotEqual(Constants.HttpOKResponse, response.StatusCode.ToString(), "The channel is created in MK Aquila"));
                List<Dictionary<string, object>> instances = JObject.Parse(response)["instances"].Value<JArray>().ToObject<List<Dictionary<string, object>>>();
                this.VerifyThat(() => Assert.AreEqual(1, instances.Count, $"Offer count not matched for subscription {instances}"));
            }
        }

        [StepDefinition(@"I add ""(.*)"" media resolution as given below")]
        public async Task WhenIAddMediaResolutionAsGivenBelow(string entity, Table mediaResolutions)
        {
            JObject updatedGmsEntityJson = (JObject)this.ScenarioContext["updatedGmsEntityJson"];
            var resolutions = mediaResolutions?.Rows.Select(x => (Media: int.Parse(x["index"]), Resolution: x["resolution"]));
            foreach (var resolution in resolutions)
            {
                updatedGmsEntityJson["media"][resolution.Media]["resolution"] = resolution.Resolution;
            }

            await this.UpsertObjectToListAndUpdateStub(entity, updatedGmsEntityJson, isUpdate: true).ConfigureAwait(false);
        }

            /// <summary>
            /// Drop an existing dynamic channel Id request in aquila service bus queue.
            /// </summary>
            /// <param name="channelId">channelId.</param>
            /// <returns>Task.</returns>
        private async Task DropSetupChannelMessageInServiceBusQueue(string channelId)
        {
            var channelRequestJsonString = new JObject();
            var testDataJsonTotal = await this.jsonDataSource.ReadAsync<JObject>(this.ServiceBusMessageSchemasJsonFilePath.GetFullPath()).ConfigureAwait(false);
            var testDataJson = testDataJsonTotal["ChannelSetupOrCleanupRequest"].ToString();

            string requestId = Guid.NewGuid().ToString();
            this.channelName = UtilityMethods.GetRandomString().ToLower();
            string longRunningOpId = Guid.NewGuid().ToString();

            var actorData = JArray.Parse(testDataJsonTotal["AquilaActorData"].ToString().Replace("{ChannelId}", channelId, StringComparison.OrdinalIgnoreCase).Replace("{ChannelName}", this.channelName, StringComparison.OrdinalIgnoreCase).ToString());
            var testDataJsonString = JObject.Parse(testDataJson.ToString());
            var actorDataJsonString = testDataJsonString["ActorSpecificDetail"];
            actorDataJsonString["Data"] = actorData;
            testDataJson = testDataJsonString.ToString();
            channelRequestJsonString = JObject.Parse(testDataJson.ToString().ReplaceIgnoreCase("{requestId}", requestId).ReplaceIgnoreCase("{longrunningoperationId}", longRunningOpId).ReplaceIgnoreCase("{infraId}", this.ScenarioContext["gameId"].ToString()).ReplaceIgnoreCase("{externalInfraId}", this.ScenarioContext["gameId"].ToString()).ReplaceIgnoreCase("{workflowId}", nameof(Workflows.EventInfrastructureSetup)).ReplaceIgnoreCase("{desiredState}", Constants.ChannelSetupDesiredState).ReplaceIgnoreCase("{correlationId}", this.ScenarioContext["correlationId"].ToString()).ReplaceIgnoreCase("{GameId}", this.ScenarioContext["gameId"].ToString()));

            await this.serviceBusOps.WriteAllAsync(Configs.AquilaInfrastructureStateChangeRequestQueue, channelRequestJsonString).ConfigureAwait(false);
            await Task.Delay(8000).ConfigureAwait(false);
        }

        /// <summary>
        /// Reads the events from blob storage & checks if specified event is published.
        /// </summary>
        /// <param name="eventName">event name.</param>
        /// <param name="ticks">ticks.</param>
        /// <returns>Int.</returns>
        private async Task<int> ReadEventsAndCheckIfEventIsPublished(string eventName, object ticks = null)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            string jsonContent = string.Empty;
            if (ticks != null)
            {
                jsonContent = await this.GetEventsContentFromBlobAsync(ticks).ConfigureAwait(false);
            }
            else
            {
                jsonContent = await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
            }

            JArray events = JArray.Parse(jsonContent);
            return events.Count(json => json["subject"].ToString() == $"aquila.{eventName}" && json["data"]["Id"].ToString() == this.channelId);
        }

        /// <summary>
        /// Add Aquila stub.
        /// </summary>
        /// <returns>Task.</returns>
        private async Task AddAquilaStub()
        {
            var blobName = this.AquilaChannelStubBlobName;
            string jsonContent = await UtilityMethods.GetStringContentFromBlobAsync(this.blobStorage, Configs.StubContainer, blobName).ConfigureAwait(false);
            if (!this.ScenarioContext.ContainsKey("blobBeforeCreation"))
            {
                this.ScenarioContext["blobBeforeCreation"] = jsonContent;
                this.ScenarioContext["entityCreated"] = nameof(CosmosContainers.Channel);
                this.ScenarioContext["entityPartitionKeyValue"] = nameof(CosmosContainers.Channel);
            }

            JObject content = JObject.Parse(jsonContent);
            JArray entities = JArray.Parse(content["channels"].ToString());
            JObject entityObject = this.entityData.GetEntityData(nameof(CosmosContainers.Channel));

            this.ScenarioContext["entityId"] = this.ScenarioContext["channelId"];
            entityObject["id"] = this.ScenarioContext["channelId"].ToString();
            entityObject["state"] = "stopped";
            entityObject["name"] = UtilityMethods.GetRandomString();
            entities.Add(entityObject);
            content["channels"] = entities;
            jsonContent = content.ToString();

            await UtilityMethods.UploadStringContentToBlobAsync(this.blobStorage, Configs.StubContainer, blobName, jsonContent).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
            this.ScenarioContext["tableName"] = Constants.AquilaActorTableStorageName;
            this.ScenarioContext["partitionKey"] = $"AquilaActor{this.ScenarioContext["channelId"].ToString()}";
            this.ScenarioContext["rowKey"] = string.Empty;
            this.ScenarioContext["service"] = Constants.Aquila;
        }

        /// <summary>
        /// Change Aquila channel state start/stop in stub.
        /// </summary
        /// <param name="desiredState">desiredState.</param>
        /// <returns>Task.</returns>
        private async Task ChangeChannelState(string desiredState)
        {
            string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName).ConfigureAwait(false);
            var json = JObject.Parse(jsonContent);
            var channels = JArray.Parse(json["channels"].ToString()).Children();

            var channelBody = channels.SingleOrDefault(x => x["id"].ToString().Equals(this.ScenarioContext["channelId"].ToString(), StringComparison.Ordinal));
            channelBody["state"] = desiredState;

            channels.SingleOrDefault(x => x["id"].ToString().Equals(this.ScenarioContext["channelId"].ToString(), StringComparison.Ordinal)).Replace(channelBody);
            json["channels"].Replace(JArray.Parse(JsonConvert.SerializeObject(channels)));
            await this.UploadStringContentToBlobAsync(Configs.StubContainer, this.AquilaChannelStubBlobName, json.ToString()).ConfigureAwait(false);
            await UtilityMethods.RestartJsonServerAsync(Configs.RestartJsonServerUrl).ConfigureAwait(false);
            await Task.Delay(3000).ConfigureAwait(false);
        }

        /// <summary>
        /// Execute Aquila polling function.
        /// </summary
        /// <param name="desiredState">desiredState.</param>
        /// <returns>Task.</returns>
        private async Task ExecuteChannelPollingFunction()
        {
            this.ScenarioContext["service"] = Constants.Aquila;
            string function = "aquilachannelpollingfunction";
            JObject body = JObject.Parse("{ \"input\" : \"\"}");
            this.response = await this.SendPostRequestAsync(Url.Combine(Configs.AquilaFunctionAppUrl + function), body, this.AquilaFunctionHeader).ConfigureAwait(false);
            Assert.AreEqual(Constants.Accepted, this.response.StatusCode.ToString(), "Aquila channel polling function failed");
            await Task.Delay(1000).ConfigureAwait(false);
        }
    }
}