// "//-----------------------------------------------------------------------".
// <copyright file="CaptureGMSSchedulesSnapshotSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;

    using Bdd.Core.DataSources;

    using FluentAssertions.Json;

    using Flurl;

    using global::Bdd.Core.Utils;

    using Microsoft.WindowsAzure.Storage.Blob;

    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;

    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    [Binding]
    public class CaptureGMSSchedulesSnapshotSteps : ApiStepDefinitionBase
    {
        private readonly BlobStorageDataSource blobDataSource;
        private string baseUrl;
        private string functionAppUrl;
        private dynamic blobs;
        private JObject expectedresponse;
        private string leagueName;
        private string blobFileName = string.Empty;
        private CloudBlobContainer blobContainer;

        /// <summary>
        /// Initializes a new instance of the <see cref="CaptureGMSSchedulesSnapshotSteps"/> class.
        /// </summary>
        /// <param name="blobDataSource">blobstorage.</param>
        public CaptureGMSSchedulesSnapshotSteps(BlobStorageDataSource blobDataSource)
        {
            this.blobDataSource = blobDataSource;
        }

        /// <summary>
        /// Get gms real game content.
        /// </summary>
        /// <param name="season">season.</param>
        /// <param name="league">league.</param>
        /// <returns>Task.</returns>
        [When(@"I get games data for season ""([^""]*)"" and league ""([^""]*)""")]
        public async Task WhenISendAPOSTRequestToEndpointToFetchTheRealGameContent(string season, string league)
        {
            if (!Configs.RunWithMocking)
            {
                this.baseUrl = Configs.GetAllMediaUrl;
                string queryparams = $"?leagueId={league}&season={season}";
                string completeUrl = this.baseUrl + queryparams;
                var response = await this.SendGetRequestAsync(Url.Combine(completeUrl), this.ApimHeaders).ConfigureAwait(false);
                var responseContent = await ((HttpResponseMessage)response).Content.ReadAsStringAsync().ConfigureAwait(false);
                this.expectedresponse = JObject.Parse(responseContent);
            }
            else
            {
                var blobName = $"Clients/Gms/Games/data.json";
                string jsonContent = await this.GetStringContentFromBlobAsync(Configs.StubContainer, blobName).ConfigureAwait(false);
                JObject content = JObject.Parse(jsonContent);
                var seasons = (JArray)content["games"];
                this.expectedresponse = (JObject)seasons.SingleOrDefault(x => x["id"].ToString().EqualsIgnoreCase($"{season}{league}"));
            }

            this.leagueName = this.expectedresponse["league"]["name"].ToString();
        }

        /// <summary>
        /// trigger function.
        /// </summary>
        /// <param name="function"> function.</param>
        /// <param name="noOfLeagues"> noOfLeagues.</param>
        /// <returns>Task.</returns>
        [When(@"I trigger the ""(.*)"" function for ""(.*)"" leagues")]
        public async Task WhenITriggerTheFunctionWithLeagueID(string function, int noOfLeagues)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            JObject body = JObject.Parse("{ \"input\" : \"\"}");
            this.functionAppUrl = Configs.GmsFunctionAppUrl;
            while (noOfLeagues > 0)
            {
                var response = await this.SendPostRequestAsync(Url.Combine(this.functionAppUrl, function), body, this.GmsFunctionHeader).ConfigureAwait(false);
                Assert.AreEqual(Constants.Accepted, response?.StatusCode.ToString(), $"{function} function failed");
                noOfLeagues--;
            }
        }

        /// <summary>
        /// This is to validate the new blob file created with gms game schedules.
        /// </summary>
        /// <param name="season">season.</param>
        /// <param name="league">league.</param>
        /// <returns>Task.</returns>
        [Then(@"I validate new file is captured in storage for season ""(.*)"" and league ""(.*)""")]
        public async Task ThenIValidateNewFileIsCapturedInBlobStorage(string season, string league)
        {
            bool flag = false;
            Logger.Info(league);
            IList<CloudBlockBlob> fileNames = new List<CloudBlockBlob>();
            this.blobContainer = this.blobDataSource.Read(Constants.Gamesnapshot, Constants.GamesnapshotStorage);
            this.blobs = (await this.blobDataSource.ReadAll(this.blobContainer).ConfigureAwait(false)).Where(x => (DateTime.UtcNow - x.Properties.Created.Value.UtcDateTime).TotalMinutes < 5).OrderByDescending(y => y.Properties.Created);
            if (this.leagueName == "NBA")
            {
                fileNames = (this.blobs as IEnumerable<CloudBlockBlob>).ToList().Where(x => x.Name.ContainsIgnoreCase("NationalBasketballAssociation") && x.Name.ContainsIgnoreCase(season)).ToList();
            }
            else if (this.leagueName == "INT")
            {
                fileNames = (this.blobs as IEnumerable<CloudBlockBlob>).ToList().Where(x => x.Name.ContainsIgnoreCase("International") && x.Name.ContainsIgnoreCase(season)).ToList();
            }

            foreach (var blob in fileNames)
            {
                var blobFileContent = await this.blobDataSource.ReadAsync(Constants.Gamesnapshot, blob.Name, keyPrefix: Constants.GamesnapshotStorage).ConfigureAwait(false);
                var blobJsonContent = UtilityMethods.StreamToString(blobFileContent);
                var actualresponse = JObject.Parse(blobJsonContent.ReplaceIgnoreCase("{logo|tnt.png,logos|tnt_s.png}", "{logo|tnt.png}{logos|tnt_s.png}"));

                if (JObject.DeepEquals(this.expectedresponse, actualresponse))
                {
                    flag = true;
                    this.blobFileName = blob.Name;
                    break;
                }
            }

            Assert.True(flag, "The snapshot is not captureed correctly");
        }

        /// <summary>
        /// This is to delete the created blob file.
        /// </summary>
        /// <returns>Task.</returns>
        [Then(@"I delete the created file from storage")]
        public async Task ThenIDeleteTheCreatedBlobFile()
        {
            await this.blobDataSource.DeleteAsync(Constants.Gamesnapshot, this.blobFileName, Constants.GamesnapshotStorage).ConfigureAwait(false);
        }

        /// <summary>
        /// This is to clean up the data before triggering the function.
        /// </summary>
        /// <returns>Task.</returns>
        [StepDefinition(@"I clean up data in storage before triggering the event")]
        public async Task WhenICleanUpBlobDataBeforeTriggeringTheEvent()
        {
            this.blobContainer = this.blobDataSource.Read(Constants.Gamesnapshot, Constants.GamesnapshotStorage);

            BlobContinuationToken fileToken = null;
            do
            {
                var result = await this.blobContainer.ListBlobsSegmentedAsync(fileToken).ConfigureAwait(false);
                fileToken = result.ContinuationToken;
                foreach (var item in result.Results.Where(r => r.GetType() == typeof(CloudBlockBlob)).ToList())
                {
                    await ((CloudBlockBlob)item).DeleteIfExistsAsync().ConfigureAwait(false);
                }
            }
            while (fileToken != null);
        }
    }
}
