// "//-----------------------------------------------------------------------".
// <copyright file="ChannelValidationSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;
    using Bdd.Core.DataSources;
    using Bdd.Core.Utils;

    using Flurl;

    using NBA.NextGen.VideoPlatform.Api.Tests.DataSources;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    public class ChannelValidationSteps : ApiStepDefinitionBase
    {
        private readonly JsonDataSource jsonDataSource;
        private readonly CosmosDBDataSource cosmosDBDataSource;
        private string encoderId;
        private string resolution;
        private string entityId;
        private string channelId;
        private string sourceFlag;
        private string templateFlag;
        private string sourceStatusMsg;
        private string templateStatusMsg;

        /// <summary>
        /// Initializes a new instance of the <see cref="ChannelValidationSteps"/> class.
        /// </summary>
        /// <param name="jsonDataSource">jsonDataSource.</param>
        /// <param name="cosmosDBDataSource">cosmosDBDataSource.</param>
        public ChannelValidationSteps(JsonDataSource jsonDataSource, CosmosDBDataSource cosmosDBDataSource)
        {
            this.jsonDataSource = jsonDataSource;
            this.cosmosDBDataSource = cosmosDBDataSource;
        }

        // <summary>
        // Create schedule with live event time less than 48 hours.
        // <param name="condition">condition.</param>
        // <param name="entity">entity.</param>
        // <param name="hours">hours.</param>
        // <returns>Task.</returns>
        [Given(@"I have a schedule with ""(.*)"" for a ""(.*)"" with game/event date is within (.*) hours")]
        public async Task GivenIHaveAScheduleWithForAWithGameEventDateIsWithinHours(string condition, string entity, int hours)
        {
            Logger.Info(hours);
            var schedule = await this.jsonDataSource.ReadAsync<JObject>(this.SchedulePath.GetFullPath()).ConfigureAwait(false);
            schedule["RequestorLiveEventId"] = this.GetUniqueGameId();
            schedule["id"] = $" {schedule["RequestorLiveEventId"].ToString()}schedule";
            this.entityId = schedule["RequestorLiveEventId"].ToString();
            string entityName = entity.ReplaceIgnoreCase(Constants.Gms, string.Empty);
            schedule["RequestorEventType"] = entityName;
            schedule["WorkflowIntents"][1]["LiveEventTime"] = DateTime.UtcNow.AddHours(47).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture);
            string character = entity.EqualsIgnoreCase(nameof(CosmosContainers.GmsGame)) ? "g" : "e";
            this.channelId = $"{character}{this.entityId}{UtilityMethods.GetRandomString()}";
            schedule["WorkflowIntents"][1]["ChannelId"] = this.entityId;
            schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["ChannelId"] = this.channelId;
            schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["ChannelName"] = this.channelId;
            schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["EventId"] = this.entityId;
            this.ScenarioContext["scheduleId"] = schedule["id"].ToString();
            switch (condition)
            {
                case "null encoder value":
                    {
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["EncoderId"] = null;
                        this.sourceFlag = "false";
                        this.templateFlag = "true";
                        this.sourceStatusMsg = string.Format(ChannelValidationStatusMessages.NullOrEmptySourceValueMsg, this.channelId, entityName, this.entityId);

                        this.templateStatusMsg = Constants.HttpOKResponse;
                        break;
                    }

                case "Empty encoder value":
                    {
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["EncoderId"] = string.Empty;
                        this.sourceFlag = "false";
                        this.templateFlag = "true";
                        this.sourceStatusMsg = string.Format(ChannelValidationStatusMessages.NullOrEmptySourceValueMsg, this.channelId, entityName, this.entityId);
                        this.templateStatusMsg = Constants.HttpOKResponse;
                        break;
                    }

                case "encoder value which does not have mapped value in VideoPlatformSource":
                    {
                        string encoderId = UtilityMethods.GetRandomString();
                        var schedules = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, encoderId), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSource)).ConfigureAwait(false);
                        Assert.True(schedules.ToList().Count == 0, $"The VideoplatformSource document for {encoderId} is present");
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["EncoderId"] = encoderId;

                        this.sourceFlag = "false";
                        this.templateFlag = "true";
                        this.sourceStatusMsg = string.Format(ChannelValidationStatusMessages.NoVideoPlaformSourceMappingMsg, this.channelId, entityName, this.entityId, encoderId);
                        this.templateStatusMsg = Constants.HttpOKResponse;

                        break;
                    }

                case "encoder value which does not have mapped source value in Aquila":
                    {
                        var source = await this.jsonDataSource.ReadAsync<JObject>(this.SourcePath.GetFullPath()).ConfigureAwait(false);
                        this.encoderId = UtilityMethods.GetRandomNumber(100000, 200000).ToString();
                        string sourceValue = UtilityMethods.GetRandomString();
                        source["Regions"][0]["MainSource"] = sourceValue;
                        source["id"] = this.encoderId;
                        await this.cosmosDBDataSource.CreateAsync(Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSource), source).ConfigureAwait(false);
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["EncoderId"] = this.encoderId;
                        this.ScenarioContext["encoderId"] = source["id"].ToString();
                        this.sourceFlag = "false";
                        this.templateFlag = "true";
                        this.sourceStatusMsg = string.Format(ChannelValidationStatusMessages.NoAquilaSourceMappingMsg, this.channelId, entityName, this.entityId, this.encoderId, sourceValue);
                        this.templateStatusMsg = Constants.HttpOKResponse;
                        break;
                    }

                case "resolution value which does not have mapped value in VideoPlatformTemplate":
                    {
                        string resolution = UtilityMethods.GetRandomString();
                        var schedules = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, resolution), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformTemplate)).ConfigureAwait(false);
                        Assert.True(schedules.ToList().Count == 0, $"The VideoplatformTemplate document for {resolution} is present");
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["MediaResolution"] = resolution;

                        this.sourceFlag = "true";
                        this.templateFlag = "false";
                        this.sourceStatusMsg = Constants.HttpOKResponse;
                        this.templateStatusMsg = string.Format(ChannelValidationStatusMessages.NoVideoPlaformTemplateMappingMsg, this.channelId, entityName, this.entityId, resolution);
                        break;
                    }

                case "resolution value which does not have mapped template value in Aquila":
                    {
                        var template = await this.jsonDataSource.ReadAsync<JObject>(this.TemplatePath.GetFullPath()).ConfigureAwait(false);
                        this.resolution = UtilityMethods.GetRandomNumber(100, 999).ToString();
                        string templateValue = UtilityMethods.GetRandomString();
                        template["id"] = this.resolution;
                        template["AquilaTemplate"] = templateValue;
                        template["MediaResolution"] = this.resolution;
                        await this.cosmosDBDataSource.CreateAsync(Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformTemplate), template).ConfigureAwait(false);
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["MediaResolution"] = this.resolution;
                        this.ScenarioContext["resolution"] = template["id"].ToString();
                        this.sourceFlag = "true";
                        this.templateFlag = "false";
                        this.sourceStatusMsg = Constants.HttpOKResponse;
                        this.templateStatusMsg = string.Format(ChannelValidationStatusMessages.NoAquilaTemplateMappingMsg, this.channelId, entityName, this.entityId, this.resolution, templateValue);
                        this.ScenarioContext["resolution"] = this.resolution;
                        break;
                    }

                case "encoder value with mapped source values in VideoPlatformSource and Aquila":
                    {
                        this.encoderId = "10003";
                        var sourceDoc = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, this.encoderId), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSource)).ConfigureAwait(false);
                        Assert.True(sourceDoc.ToList().Count == 1, $"The VideoplatformSource document for {this.encoderId} is not present");
                        string sourceValue = sourceDoc.First()["Regions"][0]["MainSource"].ToString();
                        bool isPresent = await this.CheckSourceOrTemplatePresentInAquila(nameof(CosmosContainers.VideoPlatformSource), sourceValue).ConfigureAwait(false);
                        Assert.True(isPresent, "The source value is not present in Aquila");
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["EncoderId"] = this.encoderId;

                        this.sourceFlag = "true";
                        this.templateFlag = "true";
                        this.sourceStatusMsg = Constants.HttpOKResponse;
                        this.templateStatusMsg = Constants.HttpOKResponse;
                        break;
                    }

                case "resolution value with mapped template values in VideoPlatformTemplate and Aquila":
                    {
                        this.resolution = "Game_null";
                        var templateDoc = await this.cosmosDBDataSource.ReadAllAsync<JObject>(string.Format(CosmosQueries.GetDocumentById, this.resolution), null, Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformTemplate)).ConfigureAwait(false);
                        Assert.True(templateDoc.ToList().Count == 1, $"The VideoplatformTemplate document for {this.resolution} is not present");
                        string templateValue = templateDoc.First()["AquilaTemplate"].ToString();
                        bool isPresent = await this.CheckSourceOrTemplatePresentInAquila(nameof(CosmosContainers.VideoPlatformTemplate), templateValue).ConfigureAwait(false);
                        Assert.True(isPresent, "The template value is not present in Aquila");
                        schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["MediaResolution"] = this.resolution;

                        this.sourceFlag = "true";
                        this.templateFlag = "true";
                        this.sourceStatusMsg = Constants.HttpOKResponse;
                        this.templateStatusMsg = Constants.HttpOKResponse;
                        break;
                    }
            }

            await this.cosmosDBDataSource.CreateAsync(Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSchedule), schedule).ConfigureAwait(false);
        }

        // <summary>
        // Validate expected event is published with expected message.
        // <param name="eventName">eventName.</param>
        // <returns>Task.</returns>
        [Then(@"I verify whether ""(.*)"" event is published with expected message")]
        public async Task ThenIVerifyWhetherEventIsPublishedWithExpectedMessage(string eventName)
        {
            bool flag = await this.ValidateEventWithExpectedMsgPublished(eventName).ConfigureAwait(false);

            this.VerifyThat(() => Assert.True(flag, $"No {eventName}Event with expected message published "));
        }

        // <summary>
        // Create schedules with game/event time more than hours provided.
        // <param name="entity">entity.</param>
        // <param name="hours">hours.</param>
        // <returns>Task.</returns>
        [Given(@"I have a schedule for a ""(.*)"" with game/event date is after (.*) hours")]
        public async Task GivenIHaveAScheduleForAWithGameEventDateIsAfterHours(string entity, int hours)
        {
            var schedule = await this.jsonDataSource.ReadAsync<JObject>(this.SchedulePath.GetFullPath()).ConfigureAwait(false);
            schedule["RequestorLiveEventId"] = this.GetUniqueGameId();
            schedule["id"] = $" {schedule["RequestorLiveEventId"].ToString()}schedule";
            this.entityId = schedule["RequestorLiveEventId"].ToString();
            this.channelId = schedule["id"].ToString();
            string entityName = entity.ReplaceIgnoreCase("Gms", string.Empty);
            schedule["RequestorEventType"] = entityName;
            schedule["WorkflowIntents"][1]["LiveEventTime"] = DateTime.UtcNow.AddHours(hours + 1).ToString(Constants.DateTimeStringFormat1, CultureInfo.InvariantCulture);
            schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["MediaResolution"] = "Game_null";
            schedule["WorkflowIntents"][1]["VideoPlatformActorSpecificDetails"][0]["Data"][0]["EncoderId"] = "10003";
            await this.cosmosDBDataSource.CreateAsync(Configs.CosmosDbName, nameof(CosmosContainers.VideoPlatformSchedule), schedule).ConfigureAwait(false);

            this.sourceFlag = "true";
            this.templateFlag = "true";
            this.sourceStatusMsg = Constants.HttpOKResponse;
            this.templateStatusMsg = Constants.HttpOKResponse;
        }

        [Then(@"I verify whether ""(.*)"" event is not published")]
        public async Task ThenIVerifyWhetherEventIsNotPublished(string eventName)
        {
            bool flag = await this.ValidateEventWithExpectedMsgPublished(eventName).ConfigureAwait(false);

            this.VerifyThat(() => Assert.False(flag, $"{eventName} Event with expected message is published "));
        }

        // <summary>
        // Get source/template value from Aquila.
        // <param name="item">item.</param>
        // <param name="value">value.</param>
        // <returns>Task.</returns>
        private async Task<bool> CheckSourceOrTemplatePresentInAquila(string item, string value)
        {
            bool flag = false;
            if (item.EqualsIgnoreCase(nameof(CosmosContainers.VideoPlatformSource)))
            {
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl, $"{KeyVaultSecrets.MKHubAccountId}/sources", $"?{this.ApimHeader}");
                var response = await this.SendGetRequestAsync(url).ConfigureAwait(false);
                var responseContent = await ((HttpResponseMessage)response).Content.ReadAsStringAsync().ConfigureAwait(false);
                JArray sources = JArray.Parse(responseContent);
                flag = sources.Children().Any(x => x["name"].ToString().Equals(value, StringComparison.Ordinal));
            }
            else if (item.EqualsIgnoreCase(nameof(CosmosContainers.VideoPlatformTemplate)))
            {
                string url = Url.Combine(Configs.MKAquilaBaseEndpointUrl, $"{KeyVaultSecrets.MKHubAccountId}/templates/channels", $"?{this.ApimHeader}");
                var response = await this.SendGetRequestAsync(url).ConfigureAwait(false);
                var responseContent = await ((HttpResponseMessage)response).Content.ReadAsStringAsync().ConfigureAwait(false);
                JArray templates = JArray.Parse(responseContent);
                flag = templates.Children().Any(x => x["name"].ToString().Equals(value, StringComparison.Ordinal));
            }

            return flag;
        }

        // <summary>
        // Check whether expected event is published.
        // <param name="eventName">eventName.</param>
        // <returns>Task.</returns>
        private async Task<bool> ValidateEventWithExpectedMsgPublished(string eventName)
        {
            int timeoutCount = 5;
            bool flag = false;
            while (timeoutCount >= 0)
            {
                var eventsJsonString = await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
                var publishedEvents = JArray.Parse(eventsJsonString);
                flag = publishedEvents.Any(y => ((JValue)y["subject"]).Value.ToString().EqualsIgnoreCase(eventName) && ((JValue)y["data"]["EventId"]).Value.ToString().EqualsIgnoreCase(this.entityId) && ((JValue)y["data"]["ChannelId"]).Value.ToString().EqualsIgnoreCase(this.channelId) && ((JValue)y["data"]["SourceProperlyConfigured"]).Value.ToString().EqualsIgnoreCase(this.sourceFlag) && ((JValue)y["data"]["TemplateProperlyConfigured"]).Value.ToString().EqualsIgnoreCase(this.templateFlag) && ((JValue)y["data"]["SourceStatusMessage"]).Value.ToString().EqualsIgnoreCase(this.sourceStatusMsg) && ((JValue)y["data"]["TemplateStatusMessage"]).Value.ToString().EqualsIgnoreCase(this.templateStatusMsg));

                if (flag)
                {
                    break;
                }

                await Task.Delay(5000).ConfigureAwait(false);
                timeoutCount--;
            }

            return flag;
        }
    }
}
