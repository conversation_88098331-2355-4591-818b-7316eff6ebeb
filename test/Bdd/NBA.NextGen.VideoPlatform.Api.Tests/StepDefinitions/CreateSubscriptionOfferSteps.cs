// "//-----------------------------------------------------------------------".
// <copyright file="CreateSubscriptionOfferSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System.IO;
    using System.Linq;
    using System.Net.Http;
    using System.Threading.Tasks;

    using Bdd.Core.DataSources;
    using Bdd.Core.Utils;

    using Flurl;

    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;

    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    [Binding]
    public class CreateSubscriptionOfferSteps : ApiStepDefinitionBase
    {
        private ServiceBusDataOperations serviceBusOps;
        private JObject jsonObject;
        private JObject jsonContent;
        private dynamic response;
        private string offerId;
        private int offerCount;

        /// <summary>
        /// Initializes a new instance of the <see cref="CreateSubscriptionOfferSteps"/> class.
        /// </summary>
        /// <param name="serviceBusOps">serviceBusOps.</param>
        public CreateSubscriptionOfferSteps(ServiceBusDataOperations serviceBusOps)
        {
            this.serviceBusOps = serviceBusOps;
        }

        /// <summary>
        /// This is send request from service bus and create subscription.
        /// </summary>
        /// <param name="key">body tags.</param>
        /// <param name="value">body tag value.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I drop a message in Infrastructure state change request TVP service bus queue with ""(.*)"" and value as ""(.*)"" in content")]
        public async Task WhenIDropAMessageInInfrastructureStateChangeRequestTVPServiceBusQueueWith(string key, string value)
        {
            this.jsonObject = JObject.Parse(File.ReadAllText(@"TestData\Input\PostBody.json".GetFullPath()));
            this.jsonContent = JObject.Parse(this.jsonObject["CreateSubscriptionOffer"].ToString());
            this.jsonContent[key][0] = value;

            await this.serviceBusOps.WriteAllAsync(Configs.TvpInfrastructureStateChangeRequestQueue, this.jsonContent).ConfigureAwait(false);
            await Task.Delay(5000).ConfigureAwait(false);
            await this.GetEventsContentFromBlobAsync().ConfigureAwait(false);
        }

        /// <summary>
        /// This is to GET Offer details.
        /// </summary>
        /// <param name="endpoint">endpoint.</param>
        /// <param name="package">package id.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I send GET request to ""(.*)"" endpoint for package Id ""(.*)""")]
        public async Task WhenISendGETRequestToEndpointForPackageId(string endpoint, string package)
        {
            Logger.Info(endpoint);
            this.response = await this.SendGetRequestAsync(Url.Combine(Configs.GetAllSubscriptionOffersUrl, package, "/", "offers"), this.ApimHeaders).ConfigureAwait(false);
            var responseContent = await ((HttpResponseMessage)this.response).Content.ReadAsStringAsync().ConfigureAwait(false);
            var contents = JObject.Parse(responseContent);
            this.offerCount = contents["offers"].Count();
            if (this.offerCount > 0)
            {
                this.offerId = contents["offers"][0]["id"].ToString();
            }
        }

        /// <summary>
        /// This step verifies number of offers with in Subscription package.
        /// </summary>
        /// <param name="number">number of subscriptions.</param>
        [Then(@"I verify ""(.*)"" offer is created")]
        public void ThenIVerifyOnlyOneOfferIsCreated(string number)
        {
            this.VerifyThat(() => Assert.AreEqual(this.offerCount, number.ToInt(), "number of offers created are different with expected offer count"));
        }

        /// <summary>
        /// This step to delete the offer associated with subscription package.
        /// </summary>
        /// <param name="endpoint">end point.</param>
        /// <param name="package">package id.</param>
        /// <param name="id">offer id.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I send DELETE request to ""(.*)"" endpoint with package Id ""(.*)"" and offer Id ""(.*)""")]
        public async Task ThenISendDELETERequestToWithPackageId(string endpoint, string package, string id)
        {
            Logger.Info(endpoint);
            id = this.offerId.ToString();
            this.response = await this.SendDeleteRequestAsync(Url.Combine(Configs.GetAllSubscriptionOffersUrl, package, "/", "offers", "/", id), this.ApimHeaders).ConfigureAwait(false);
        }
    }
}