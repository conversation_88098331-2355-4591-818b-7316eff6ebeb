// "//-----------------------------------------------------------------------".
// <copyright file="HealthCheckEventSteps.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions
{
    using System;
    using System.Collections.Generic;
    using System.Configuration;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using System.Threading.Tasks;

    using Flurl;
    using Flurl.Http;
    using global::Bdd.Core.Utils;

    using Microsoft.Azure.Cosmos;
    using NBA.NextGen.VideoPlatform.Api.Tests.DataSources;
    using NBA.NextGen.VideoPlatform.Api.Tests.Entities;
    using NBA.NextGen.VideoPlatform.Api.Tests.StepDefinitions.Common;
    using NBA.NextGen.VideoPlatform.Api.Tests.Utils;

    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    using NUnit.Framework;

    using TechTalk.SpecFlow;

    [Binding]
    public sealed class HealthCheckEventSteps : ApiStepDefinitionBase
    {
        private dynamic response;
        private Dictionary<string, object> headers;
        private string functionAppUrl;

        /// <summary>
        /// This is to trigger respective Health Check function for given actor.
        /// </summary>
        /// <param name="actor">actor.</param>
        /// <param name="function">function.</param>
        /// <returns>Task.</returns>
        [When(@"I trigger the ""(.*)"" ""(.*)"" polling function")]
        public async Task WhenITriggerThePollingFunction(string actor, string function)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            JObject body = JObject.Parse("{ \"input\" : \"\"}");

            switch (actor)
            {
                case "AquilaActor":
                    this.headers = new Dictionary<string, object>() { { "x-functions-key", KeyVaultSecrets.AquilaActorFunctionKey } };
                    this.functionAppUrl = Configs.AquilaActorHealthCheckFunctionAppUrl;
                    break;

                case "GmsWatchdog":
                    this.headers = new Dictionary<string, object>() { { "x-functions-key", KeyVaultSecrets.GmsFunctionKey } };
                    this.functionAppUrl = Configs.GmsFunctionAppUrl;
                    break;

                case "PrismaActor":
                    this.headers = new Dictionary<string, object>() { { "x-functions-key", KeyVaultSecrets.PrismaActorFunctionKey } };
                    this.functionAppUrl = Configs.PrismaActorFunctionAppUrl;
                    break;

                case "TvpActor":
                    this.headers = new Dictionary<string, object>() { { "x-functions-key", KeyVaultSecrets.TvpActorFunctionKey } };
                    this.functionAppUrl = Configs.TVPActorFunctionAppUrl;
                    break;
            }

            this.ScenarioContext["eventTimeStamp"] = DateTime.UtcNow.AddSeconds(-1).Ticks;
            this.response = await this.SendPostRequestAsync(Url.Combine(this.functionAppUrl + function), body, this.headers).ConfigureAwait(false);
            bool flag = this.response.StatusCode.ToString() == Constants.Accepted || this.response.StatusCode.ToString() == Constants.HttpOKResponse;
            this.VerifyThat(() => Assert.True(flag, "The healthcheck function is not working"));
            await Task.Delay(1000).ConfigureAwait(false);
        }

        /// <summary>
        /// This is to hit the specific actor related endpoint.
        /// </summary>
        /// <param name="endpoint">endpoint.</param>
        /// <returns>Task.</returns>
        [StepDefinition(@"I send GET request to resource ""(.*)""")]
        public async Task ThenISendGETRequestToResource(string endpoint)
        {
            switch (endpoint)
            {
                case "TvpActor":
                    this.response = await this.SendGetRequestAsync(Configs.TVPApiBaseUrl + "/v1/teams", this.ApimHeaders).ConfigureAwait(false);
                    break;

                case "PrismaActor":
                    this.response = await this.SendGetRequestAsync(Configs.PrismaWorkerApiBaseUrl + Configs.PrismaViewingPolicyManagerId, this.ApimHeaders).ConfigureAwait(false);
                    break;

                case "AquilaActor":
                    this.response = await this.SendGetRequestAsync(Configs.MKAquilaBaseEndpointUrl + KeyVaultSecrets.MKHubAccountId + "/sources", this.ApimHeaders).ConfigureAwait(false);
                    break;

                case "GmsWatchdog":
                    this.response = await this.SendGetRequestAsync(Configs.GetAllMediaUrl + $"?leagueId=00&season=2021&&dateET={DateTime.UtcNow.ToString("yyyyMMdd")}", this.ApimHeaders).ConfigureAwait(false);
                    break;
            }
        }

        /// <summary>
        /// This is to validate the event status with API response.
        /// </summary>
        /// <param name="eventName">eventName.</param>
        /// <param name="actor">actor.</param>
        /// <returns>Task.</returns>
        [Then(@"I validate ""(.*)"" event status for ""(.*)"" should match with API response")]
        public async Task ThenIValidateEventStateWithAPIResponse(string eventName, string actor)
        {
            await Task.Delay(5000).ConfigureAwait(false);
            string eventContent = await this.GetEventsContentFromBlobAsync(5).ConfigureAwait(false);
            JArray jsonContent = JArray.Parse(eventContent);
            bool apiStatus = this.response.StatusCode.ToString().Equals(Constants.HttpOKResponse);
            bool eventStatus = jsonContent.Any(y => ((JValue)y["subject"]).Value.ToString().EqualsIgnoreCase(eventName) && ((JValue)y["data"]["ServiceName"]).Value.ToString().EqualsIgnoreCase(actor) && ((JValue)y["data"]["Status"]).Value.ToString().EqualsIgnoreCase(apiStatus.ToString()));
            Assert.True(eventStatus, "API response and Event status are different");
        }
    }
}