<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <IsPackable>false</IsPackable>
    <NoWarn>RS0016;CA1707</NoWarn>
    
    <Features>IOperation</Features>
    <Features>$(Features);flow-analysis</Features>
    <DebugType>pdbonly</DebugType>
    <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
    <GenerateAssemblyTitleAttribute>true</GenerateAssemblyTitleAttribute>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
  </PropertyGroup>

  <ItemGroup>
    
    <AdditionalFiles Include="..\..\..\src\Shared\Sln Items\NBA.Core.runsettings" Link="NBA.Core.runsettings" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\..\..\src\Shared\Sln Items\AssemblyVersion.cs" Link="Properties\AssemblyVersion.cs" />
    <Compile Include="..\..\..\src\Shared\Sln Items\GlobalSuppressions.cs" Link="GlobalSuppressions.cs" />
    <Compile Include="..\..\..\src\Shared\Sln Items\SharedAssemblyInfo.cs" Link="Properties\SharedAssemblyInfo.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Ardalis.GuardClauses" Version="1.5.0" />
    <PackageReference Include="AutoBogus.Conventions" Version="2.8.1" />
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.3" />
    <PackageReference Include="NBA.NextGen.Shared.Application" Version="2.0.0" />
    <PackageReference Include="MST.Common" Version="2.3.5" /> 
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Application\NBA.NextGen.VideoPlatform.Shared.Application.csproj" />
    <ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Domain\NBA.NextGen.VideoPlatform.Shared.Domain.csproj" />
    <ProjectReference Include="..\..\..\src\Shared\NBA.NextGen.VideoPlatform.Shared.Infrastructure\NBA.NextGen.VideoPlatform.Shared.Infrastructure.csproj" />
  </ItemGroup>
</Project>
