// "//-----------------------------------------------------------------------".
// <copyright file="AquilaRequestChangeStrategyFactoryTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".
namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.Aquila.Factories
{
    using System;
    using System.Collections.Generic;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Factories;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Aquila.Strategies;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Interfaces.Aquila;
    using NBA.NextGen.VideoPlatform.Shared.Application.Aquila.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using Xunit;

    /// <summary>
    /// The AquilaRequestChangeStrategyFactoryTests.
    /// </summary>
    public class AquilaRequestChangeStrategyFactoryTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The aquila client service.
        /// </summary>
        private readonly Mock<IAquilaClientService> mockAquilaClientService;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        private readonly Mock<ILogger<RunningAquilaChannelRequestChangeStrategy>> runningLogger;

        private readonly Mock<ILogger<StoppedAquilaChannelRequestChangeStrategy>> stoppedLogger;

        private readonly Mock<ILogger<AquilaRequestChangeStrategyFactory>> logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="AquilaRequestChangeStrategyFactoryTests" /> class.
        /// </summary>
        public AquilaRequestChangeStrategyFactoryTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);
            this.mockAquilaClientService = this.mockRepository.Create<IAquilaClientService>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.runningLogger = this.mockRepository.Create<ILogger<RunningAquilaChannelRequestChangeStrategy>>();
            this.stoppedLogger = this.mockRepository.Create<ILogger<StoppedAquilaChannelRequestChangeStrategy>>();
            this.logger = this.mockRepository.Create<ILogger<AquilaRequestChangeStrategyFactory>>();
        }

        /// <summary>
        /// GetRequestChangeStrategy with AquilaChannelState returns correct IAquilaRequestChangeStrategy.
        /// </summary>
        /// <param name="strategyType">The type of strategy.</param>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        [Theory]
        [InlineData(typeof(ErrorActivatingAquilaChannelRequestChangeStrategy), AquilaChannelState.StartError)]
        [InlineData(typeof(RunningAquilaChannelRequestChangeStrategy), AquilaChannelState.Started)]
        [InlineData(typeof(StartingAquilaChannelRequestChangeStrategy), AquilaChannelState.Starting)]
        [InlineData(typeof(StoppedAquilaChannelRequestChangeStrategy), AquilaChannelState.Stopped)]
        [InlineData(typeof(StoppingAquilaChannelRequestChangeStrategy), AquilaChannelState.Stopping)]
        public void GetRequestChangeStrategy_WithAquilaChannelState_ReturnsCorrectIAquilaRequestChangeStrategy(Type strategyType, AquilaChannelState aquilaChannelState)
        {
            // Arrange
            var aquilaRequestChangeStrategies = new List<IAquilaRequestChangeStrategy>
            {
                new ErrorActivatingAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object),
                new RunningAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object, this.runningLogger.Object),
                new StartingAquilaChannelRequestChangeStrategy(),
                new StoppedAquilaChannelRequestChangeStrategy(this.mockAquilaClientService.Object, this.mockTelemetryService.Object, this.stoppedLogger.Object),
                new StoppingAquilaChannelRequestChangeStrategy(),
            };

            var aquilaRequestChangeStrategyFactory = new AquilaRequestChangeStrategyFactory(aquilaRequestChangeStrategies, this.logger.Object);

            // Act
            var aquilaRequestChangeStrategy = aquilaRequestChangeStrategyFactory.GetRequestChangeStrategy(aquilaChannelState);

            // Assert
            Assert.IsType(strategyType, aquilaRequestChangeStrategy);
        }

        /// <summary>
        /// GetRequestChangeStrategy with AquilaChannelState throws a NotSupportedException.
        /// </summary>
        [Fact]
        public void GetRequestChangeStrategy_WithInvalidAquilaChannelState_ThrowsANotSupportedException()
        {
            // Arrange
            var aquilaRequestChangeStrategies = new List<IAquilaRequestChangeStrategy>
            {
                new StoppingAquilaChannelRequestChangeStrategy(),
            };

            var aquilaRequestChangeStrategyFactory = new AquilaRequestChangeStrategyFactory(aquilaRequestChangeStrategies, this.logger.Object);

            // Act & assert
            Assert.Throws<NotSupportedException>(() => aquilaRequestChangeStrategyFactory.GetRequestChangeStrategy(AquilaChannelState.Started));
        }
    }
}
