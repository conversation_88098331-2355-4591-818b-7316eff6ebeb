// "//-----------------------------------------------------------------------".
// <copyright file="AquilaActorProfileTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.Mappers
{
    using System;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.UseCases.Channels.Commands;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Tests;
    using Xunit;

    /// <summary>
    /// Tests of AquilaActorProfile.
    /// </summary>
    /// <seealso cref="AutoMapperBaseTests{AquilaActorProfile}" />
    public class AquilaActorProfileTests : AutoMapperBaseTests<AquilaActorProfile>
    {
        /// <summary>
        /// Map with InfrastructureState maps using AquilaStateEnumConverter to AquilaChannelState.
        /// </summary>
        /// <param name="infrastructureState">State of the infrastructure.</param>
        /// <param name="aquilaChannelState">State of the aquila channel.</param>
        [Theory]
        [InlineData(InfrastructureState.CreatingDefinition, AquilaChannelState.InfraCreating)]
        [InlineData(InfrastructureState.None, AquilaChannelState.Stopped)]
        [InlineData(InfrastructureState.Deprovisioned, AquilaChannelState.Stopped)]
        [InlineData(InfrastructureState.Provisioning, AquilaChannelState.InfraCreating)]
        [InlineData(InfrastructureState.Provisioned, AquilaChannelState.Started)]
        [InlineData(InfrastructureState.Starting, AquilaChannelState.Starting)]
        [InlineData(InfrastructureState.Started, AquilaChannelState.Started)]
        [InlineData(InfrastructureState.Failed, AquilaChannelState.StartError)]
        [InlineData(InfrastructureState.Stopping, AquilaChannelState.Stopping)]
        [InlineData(InfrastructureState.Deprovisioning, AquilaChannelState.Stopping)]
        [InlineData(InfrastructureState.Deleted, AquilaChannelState.Deleted)]
        public void Map_WithInfrastructureState_MapsUsingAquilaStateEnumConverterToAquilaChannelState(InfrastructureState infrastructureState, AquilaChannelState aquilaChannelState)
        {
            // Arrange
            var source = new InfrastructureStateChangeRequest<ChannelStateChangeInfo>
            {
                DesiredState = infrastructureState,
            };

            // Act
            var target = this.Mapper.Map<ChangeChannelStateCommand>(source);

            // Assert
            Assert.Equal(aquilaChannelState, target.DesiredChannelState);
        }
    }
}
