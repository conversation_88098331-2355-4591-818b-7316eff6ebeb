// "//-----------------------------------------------------------------------".
// <copyright file="AquilaSourceConverterTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaActor.Application.Tests.Mappers
{
    using System.Collections.ObjectModel;
    using NBA.NextGen.VideoPlatform.AquilaActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Models;
    using Xunit;

    /// <summary>
    /// Tests of AquilaSourceConverter.
    /// </summary>
    public class AquilaSourceConverterTests
    {
        /// <summary>
        /// Gets the Aquila source converter.
        /// </summary>
        /// <value>
        /// The Aquila source converter.
        /// </value>
        private AquilaSourceConverter AquilaSourceConverter => new AquilaSourceConverter();

        /// <summary>
        /// Convert with full AquilaChannelSource return a <see cref="Collection{Sources}"/> with two items.
        /// </summary>
        [Fact]
        public void Convert_FullAquilaChannelSource()
        {
            var source = new AquilaChannelSource
            {
                MainSourceName = "MainSource",
                BackupSourceName = "BackupSource",
            };
            var destination = this.AquilaSourceConverter.Convert(source, null);

            Assert.NotNull(destination);
            Assert.Equal(2, destination.Count);
            Assert.Equal("MainSource", destination[0].SourceId);
            Assert.Equal("BackupSource", destination[1].SourceId);
        }

        /// <summary>
        /// Convert with AquilaChannelSource with MainSource return a <see cref="Collection{Sources}"/> with one item.
        /// </summary>
        [Fact]
        public void Convert_AquilaChannelSource_WithMainSource()
        {
            var source = new AquilaChannelSource
            {
                MainSourceName = "MainSource",
            };
            var destination = this.AquilaSourceConverter.Convert(source, null);

            Assert.NotNull(destination);
            Assert.Single(destination);
            Assert.Equal("MainSource", destination[0].SourceId);
        }

        /// <summary>
        /// Convert with AquilaChannelSource with MainSource return a <see cref="Collection{Sources}"/> with one item.
        /// </summary>
        [Fact]
        public void Convert_AquilaChannelSource_WithBackupSource()
        {
            var source = new AquilaChannelSource
            {
                BackupSourceName = "BackupSource",
            };
            var exception = Assert.Throws<System.ArgumentException>(() => this.AquilaSourceConverter.Convert(source, null));
            Assert.Equal($"The property {nameof(source.MainSourceName)} in {nameof(AquilaChannelSource)} is required", exception.Message);
        }
    }
}
