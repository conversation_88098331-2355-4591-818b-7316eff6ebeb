// "//-----------------------------------------------------------------------".
// <copyright file="UpdateNotificationStateCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.Tests.UseCases.Events.Commands.UpdateState
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.AquilaWatchdog.Application.UseCases.Events.Commands.UpdateState;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Aquila.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using Xunit;

    /// <summary>
    /// The Update Notification State command handler tests.
    /// </summary>
    public class UpdateNotificationStateCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateNotificationStateCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateNotificationStateCommandHandlerTests"/> class.
        /// </summary>
        public UpdateNotificationStateCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<UpdateNotificationStateCommandHandler>();
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
        }

        /// <summary>
        /// Gets the UpdateNotificationStateCommandHandler.
        /// </summary>
        /// <value>
        /// The UpdateNotificationStateCommandHandler.
        /// </value>
        private UpdateNotificationStateCommandHandler UpdateNotificationStateCommandHandler => new UpdateNotificationStateCommandHandler(
            this.mockRepositoryFactory,
            this.mockLogger.Object);

        /// <summary>
        /// Handle with expected command, updates the entity.
        /// </summary>
        /// <typeparam name="T">Type of the Entity to update.</typeparam>
        /// <param name="aquilaEntity">The instance of aquila entity to update.</param>
        /// <param name="aquilaEntityType">Type of the aquila entity.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [ClassData(typeof(UpdateStateData))]
        public async Task Handle_WithExpectedCommand_UpdatesEntityAsync<T>(T aquilaEntity, AquilaEntityType aquilaEntityType)
            where T : AquilaEntity
        {
            // Arrange
            var entityId = aquilaEntity?.Id;
            var updateNotificationStateCommand = new UpdateNotificationStateCommand
            {
                Id = entityId,
                State = NotificationState.WaitingForNotification,
                Type = aquilaEntityType,
                CorrelationId = "CorrelationId",
            };

            var mockAquilaRepository = this.mockRepositoryFactory.ResolveMock<T>();
            mockAquilaRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(aquilaEntity);

            // Act
            await this.UpdateNotificationStateCommandHandler.Handle(updateNotificationStateCommand, CancellationToken.None).ConfigureAwait(false);

            // Assert
            mockAquilaRepository.Verify(x => x.GetItemAsync(It.Is<string>(x => x == entityId)), Times.Once);
            mockAquilaRepository.Verify(x => x.UpdateItemAsync(It.Is<T>(x => x == aquilaEntity && x.NotificationState == NotificationState.WaitingForNotification)), Times.Once);
        }

        /// <summary>
        /// Handle with entity not found, logs critical.
        /// </summary>
        /// <typeparam name="T">Type of the Entity to update.</typeparam>
        /// <param name="aquilaEntity">The instance of aquila entity to update.</param>
        /// <param name="aquilaEntityType">Type of the aquila entity.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Theory]
        [ClassData(typeof(UpdateStateData))]
        public async Task Handle_WithEntityNotFound_LogsCriticalAsync<T>(T aquilaEntity, AquilaEntityType aquilaEntityType)
            where T : AquilaEntity
        {
            // Arrange
            var updateNotificationStateCommand = new UpdateNotificationStateCommand
            {
                Id = aquilaEntity?.Id,
                State = NotificationState.WaitingForNotification,
                Type = aquilaEntityType,
            };

            // Setting to null (i can not pass a null entity as parameter)
            aquilaEntity = null;

            var mockAquilaRepository = this.mockRepositoryFactory.ResolveMock<T>();
            mockAquilaRepository.Setup(x => x.GetItemAsync(It.IsAny<string>())).ReturnsAsync(aquilaEntity);

            // Act
            await this.UpdateNotificationStateCommandHandler.Handle(updateNotificationStateCommand, CancellationToken.None).ConfigureAwait(true);

            // Assert
            this.mockLogger.VerifyAnyLogging(LogLevel.Critical, Times.Once());
            mockAquilaRepository.Verify(x => x.UpdateItemAsync(It.IsAny<T>()), Times.Never);
        }

        /// <summary>
        /// Handle with null command throws exception.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithNullCommand_ThrowsExceptionAsync()
        {
            // Assert
            await Assert.ThrowsAsync<ArgumentNullException>(async () => await this.UpdateNotificationStateCommandHandler.Handle(null, CancellationToken.None).ConfigureAwait(true)).ConfigureAwait(true);
        }
    }
}
