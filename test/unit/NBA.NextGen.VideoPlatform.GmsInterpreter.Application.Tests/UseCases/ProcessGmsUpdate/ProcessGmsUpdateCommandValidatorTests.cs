// "//-----------------------------------------------------------------------".
// <copyright file="ProcessGmsUpdateCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.GmsInterpreter.Application.Tests.UseCases.ProcessGmsUpdate
{
    using Moq;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.GmsInterpreter.Application.UseCases.ProcessGmsUpdate;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using Xunit;

    /// <summary>
    /// The ProcessGmsUpdateCommandValidatorTests.
    /// </summary>
    public class ProcessGmsUpdateCommandValidatorTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockQueryableRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="ProcessGmsUpdateCommandValidatorTests"/> class.
        /// </summary>
        public ProcessGmsUpdateCommandValidatorTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockRepositoryFactory = new MockQueryableRepositoryFactory();
        }

        /// <summary>
        /// Gets the process GMS update command validator.
        /// </summary>
        /// <value>
        /// The process GMS update command validator.
        /// </value>
        private ProcessGmsUpdateCommandValidator ProcessGmsUpdateCommandValidator => new ProcessGmsUpdateCommandValidator(this.mockRepositoryFactory);

        /// <summary>
        /// ValidateInput with empty Id returns invalid.
        /// </summary>
        [Fact]
        public void ValidateInput_WithEmptyId_ReturnsInvalid()
        {
            var request = new ProcessGmsUpdateCommand { Id = string.Empty };

            // Act
            var result = this.ProcessGmsUpdateCommandValidator.Validate(request);

            // Assert
            Assert.Equal(1, result.Errors.Count);
            Assert.False(result.IsValid);
            Assert.Equal("Game Id cannot be null", result.Errors[0].ErrorMessage);
        }

        /// <summary>
        /// ValidateInput with not empty id returns valid.
        /// </summary>
        [Fact]
        public void ValidateInput_WithNotEmptyId_ReturnsValid()
        {
            // Arrange
            var request = new ProcessGmsUpdateCommand { Id = "1" };

            // Act
            var result = this.ProcessGmsUpdateCommandValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
