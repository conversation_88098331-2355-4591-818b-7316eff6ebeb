// "//-----------------------------------------------------------------------".
// <copyright file="SendInfrastructureStateChangedEventCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Validations
{
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands;
    using Xunit;

    /// <summary>
    /// SendInfrastructureStateChangedEventCommandValidator Tests.
    /// </summary>
    public class SendInfrastructureStateChangedEventCommandValidatorTests
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly SendInfrastructureStateChangedEventCommandValidator validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="SendInfrastructureStateChangedEventCommandValidatorTests"/> class.
        /// </summary>
        public SendInfrastructureStateChangedEventCommandValidatorTests()
        {
            this.validator = new SendInfrastructureStateChangedEventCommandValidator();
        }

        /// <summary>
        /// ValidateInput with required properties missing fails validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithRequiredPropertiesMissing_FailsValidation()
        {
            // Arrange
            var request = new SendInfrastructureStateChangedEventCommand
            {
                LongRunningOperationId = string.Empty,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(1, result.Errors.Count);
            Assert.Equal("LongRunningOperationId cannot be null", result.Errors[0].ErrorMessage);
        }

        /// <summary>
        /// ValidateInput with required properties passes validation.
        /// </summary>
        [Fact]
        public void ValidateInput_WithRequiredProperties_PassesValidation()
        {
            // Arrange
            var request = new SendInfrastructureStateChangedEventCommand
            {
                LongRunningOperationId = "LongRunningOperationId",
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
