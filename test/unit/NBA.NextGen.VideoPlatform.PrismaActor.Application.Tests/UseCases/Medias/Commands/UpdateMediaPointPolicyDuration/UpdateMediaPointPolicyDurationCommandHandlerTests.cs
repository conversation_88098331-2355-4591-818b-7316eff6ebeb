// "//-----------------------------------------------------------------------".
// <copyright file="UpdateMediaPointPolicyDurationCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.PrismaActor.Application.Tests.UseCases.Medias.Commands.UpdateMediaPointPolicyDuration
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.PrismaActor.Application.UseCases.Medias.Commands.UpdateMediaPointPolicyDuration;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Prisma.Exceptions;
    using Xunit;

    /// <summary>
    /// The <see cref="Application.UseCases.Medias.Commands.UpdateMediaPointPolicyDuration.UpdateMediaPointPolicyDurationCommandHandler"/> Tests.
    /// </summary>
    public class UpdateMediaPointPolicyDurationCommandHandlerTests : BaseUnitTest
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateMediaPointPolicyDurationCommandHandler>> mockLogger;

        /// <summary>
        /// The mock prisma client service.
        /// </summary>
        private readonly Mock<IPrismaClientService> mockPrismaWorkerClientService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateMediaPointPolicyDurationCommandHandlerTests"/> class.
        /// </summary>
        public UpdateMediaPointPolicyDurationCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockLogger = this.CreateLoggerMock<UpdateMediaPointPolicyDurationCommandHandler>();
            this.mockPrismaWorkerClientService = this.mockRepository.Create<IPrismaClientService>();
        }

        /// <summary>
        /// Gets the handler.
        /// </summary>
        /// <value>
        /// The handler.
        /// </value>
        private UpdateMediaPointPolicyDurationCommandHandler UpdateMediaPointPolicyDurationCommandHandler => new UpdateMediaPointPolicyDurationCommandHandler(
            this.mockLogger.Object,
            this.mockPrismaWorkerClientService.Object);

        /// <summary>
        /// Handle with command updates media points.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommand_UpdatesMediaPointAsync()
        {
            // Arrange
            var duration = TimeSpan.FromHours(1);
            var request = new UpdateMediaPointPolicyDurationCommand
            {
                EsniMediaId = "EsniMediaIdTest",
                MediaPointId = "MediaPointIdTest",
                PolicyId = "PolicyIdTest",
                Duration = duration,
                LongRunningOperationId = "LongRunningOperationIdTest",
            };

            // Act
            await this.UpdateMediaPointPolicyDurationCommandHandler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClientService.Verify(
                x => x.UpdateMediaPointApplyDurationAsync(
                    It.Is<string>(x => x == "EsniMediaIdTest"),
                    It.Is<string>(x => x == "MediaPointIdTest"),
                    It.Is<string>(x => x == "PolicyIdTest"),
                    It.Is<TimeSpan>(x => x == duration)),
                Times.Once);
            this.VerifyLogger(this.mockLogger, LogLevel.Information, Times.Exactly(2));
        }

        /// <summary>
        /// Handle with command log the exception when updates media points fails.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithCommand_WhenUpdateFails_LogsTheExceptionAsync()
        {
            // Arrange
            var duration = TimeSpan.FromHours(1);
            var exceptionReason = "Cannot find ESNI entity with id EsniMediaIdTest";
            var request = new UpdateMediaPointPolicyDurationCommand
            {
                EsniMediaId = "EsniMediaIdTest",
                MediaPointId = "MediaPointIdTest",
                PolicyId = "PolicyIdTest",
                Duration = duration,
                LongRunningOperationId = "LongRunningOperationIdTest",
            };
            this.mockPrismaWorkerClientService.Setup(x => x.UpdateMediaPointApplyDurationAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TimeSpan>())).Throws(new EsniResourceNotFoundException("EsniMediaIdTest"));

            // Act
            await this.UpdateMediaPointPolicyDurationCommandHandler.Handle(request, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockPrismaWorkerClientService.Verify(
                x => x.UpdateMediaPointApplyDurationAsync(
                    It.Is<string>(x => x == "EsniMediaIdTest"),
                    It.Is<string>(x => x == "MediaPointIdTest"),
                    It.Is<string>(x => x == "PolicyIdTest"),
                    It.Is<TimeSpan>(x => x == duration)),
                Times.Once);
            this.VerifyLogger(this.mockLogger, $"Unable to update media point {request.MediaPointId} for policy {request.PolicyId}. Reason: {exceptionReason}", LogLevel.Warning, Times.Once());
        }
    }
}
