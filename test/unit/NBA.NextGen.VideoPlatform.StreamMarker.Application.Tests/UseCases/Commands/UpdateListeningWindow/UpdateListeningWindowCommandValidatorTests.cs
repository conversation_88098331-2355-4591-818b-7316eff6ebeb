// "//-----------------------------------------------------------------------".
// <copyright file="UpdateListeningWindowCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.StreamMarker.Application.Tests.UseCases.Commands.UpdateListeningWindow
{
    using NBA.NextGen.VideoPlatform.StreamMarkersListener.Application.UseCases.Commands.UpdateListeningWindow;
    using Xunit;

    /// <summary>
    /// <see cref="UpdateListeningWindowCommandValidatorTests"/>.
    /// </summary>
    public class UpdateListeningWindowCommandValidatorTests
    {
        /// <summary>
        /// Gets the <see cref="UpdateListeningWindowCommandValidator"/>.
        /// </summary>
        private UpdateListeningWindowCommandValidator UpdateListeningWindowCommandValidator => new UpdateListeningWindowCommandValidator();

        /// <summary>
        /// Validate should pass.
        /// </summary>
        [Fact]
        public void ValidateShouldPass()
        {
            // Arrange
            var request = new UpdateListeningWindowCommand { ChannelId = "ChannelId" };

            // Act
            var result = this.UpdateListeningWindowCommandValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        /// <summary>
        /// Validate with empty parameters should fail.
        /// </summary>
        [Fact]
        public void Validate_WithEmptyParameters_ShouldFail()
        {
            // Arrange
            var request = new UpdateListeningWindowCommand();

            // Act
            var result = this.UpdateListeningWindowCommandValidator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Single(result.Errors);
            Assert.Contains(result.Errors, x => x.ErrorMessage == $"{nameof(UpdateListeningWindowCommand.ChannelId)} cannot be null or empty");
        }
    }
}
