namespace NBA.NextGen.VideoPlatform.ThirdPartyActor.Tests.WorkFlowsMessages;
public static class TestsWorkflows {
    
    public static string DeleteWorkFlow(){
        return @"
            {
                'ActorId': 'bcc23cac-7f8c-422e-a78f-48a23bd2f1b7',
                'InfrastructureId': 'g0022471878nyk1000397atl',
                'ExternalSystemInfrastructureId': 'g0022471878nyk1000397atl',
                'DesiredState': 2,
                'WorkflowId': 'EventInfrastructureCleanup',
                'ActorSpecificDetail': {
                    'ActorId': 'bcc23cac-7f8c-422e-a78f-48a23bd2f1b7',
                    'Data': {
                        'CustomPath': 'radio/ratl-eng-01/1080p/play',
                        'EndpointUuid': null,
                        'PoolUuid': 'pool_test_radio',
                        'EventId': '0022471878'
                    }
                },
                'RequestId': 'f71ea177-47ac-440d-be2c-2804ebd57d34-EventInfrastructureCleanup-2025-01-04T00:30:59.3343119+00:00',
                'RequestorActorId': 'd9524a54-4951-432b-b393-1691f8e70004',
                'LongRunningOperationId': '4a266204-c142-45b1-8c5c-9152f0162e86',
                'CorrelationId': '002247187801032025200800'
            }";
    }

      public static string CreateWorkFlow(){
        return @"
            {
                'ActorId': 'bcc23cac-7f8c-422e-a78f-48a23bd2f1b7',
                'InfrastructureId': '0022471733',
                'ExternalSystemInfrastructureId': '0022471733',
                'DesiredState': 4,
                'WorkflowId': 'EventInfrastructureSetup',
                'ActorSpecificDetail': {
                    'ActorId': 'bcc23cac-7f8c-422e-a78f-48a23bd2f1b7',
                    'Data': [
                        {
                            'PoolUuid': 'pool_zaljxosa',
                            'InputUuid': 'rATL-ENG-01',
                            'ProcessingUuid': '1080p',
                            'TargetUuid': 'Play',
                            'Uuid': null,
                            'CustomPath': '/radio/ratl-eng-01/1080p/play/index.m3u8',
                            'Enabled': true,
                            'StartTime': null,
                            'EventId': '0022471733',
                            'ChannelId': 'g0022471733nyk1000397atl'
                        },
                        {
                            'PoolUuid': 'pool_zaljxosa',
                            'InputUuid': 'BOS-03',
                            'ProcessingUuid': '1080p',
                            'TargetUuid': 'Fairplay',
                            'Uuid': null,
                            'CustomPath': '/3p/bos-03/1080p/fairplay/index.m3u8',
                            'Enabled': true,
                            'StartTime': null,
                            'EventId': '0022471733',
                            'ChannelId': 'g0022471733nyk1000630atl'
                        },
                        {
                            'PoolUuid': 'pool_zaljxosa',
                            'InputUuid': 'ATL-01',
                            'ProcessingUuid': '1080p',
                            'TargetUuid': 'Disaster_Recovery',
                            'Uuid': null,
                            'CustomPath': '/dr/atl-01/1080p/disaster_recovery/index.m3u8',
                            'Enabled': true,
                            'StartTime': null,
                            'EventId': '0022471733',
                            'ChannelId': 'g0022471733nyk1000631atl'
                        }
                    ]
                },
                'RequestId': 'f63f478d-e77b-44b9-9cb1-37634cfff692-EventInfrastructureSetup-2024-12-27T19:39:01.9910574+00:00',
                'RequestorActorId': 'd9524a54-4951-432b-b393-1691f8e70004',
                'LongRunningOperationId': 'e965d23b-378c-47c5-97ea-158b4cdb60af',
                'CorrelationId': '002247173312262024200800'
            }";
    }
}