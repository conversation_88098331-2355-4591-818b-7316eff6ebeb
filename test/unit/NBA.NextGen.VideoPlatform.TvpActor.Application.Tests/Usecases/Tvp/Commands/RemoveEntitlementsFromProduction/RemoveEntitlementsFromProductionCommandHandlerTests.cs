// "//-----------------------------------------------------------------------".
// <copyright file="RemoveEntitlementsFromProductionCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.UseCases.Tvp.Commands.RemoveEntitlementsFromProduction
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using MST.Common.Data;
    using NBA.NextGen.Shared.Application.Repositories;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.Vendor.Api.MKTvp;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Extensions;
    using NBA.NextGen.VideoPlatform.Shared.Application.Repositories.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Enums;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.RemoveEntitlementsFromProduction;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddEntitlementsToProduction;
    using Xunit;

    /// <summary>
    /// Tests for RemoveEntitlementsFromProductionCommandHandler.
    /// </summary>
    public class RemoveEntitlementsFromProductionCommandHandlerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock TVP client service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<RemoveEntitlementsFromProductionCommandHandler>> mockLogger;

        /// <summary>
        /// The mock repository factory.
        /// </summary>
        private readonly MockObjectRepositoryFactory mockRepositoryFactory;

        /// <summary>
        /// The mock video platform entitlement repository.
        /// </summary>
        private readonly Mock<IObjectRepository<GmsEntitlement>> mockEntitlementRepository;

        /// <summary>
        /// The Mock telemetry.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="RemoveEntitlementsFromProductionCommandHandlerTests"/> class.
        /// </summary>
        public RemoveEntitlementsFromProductionCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockLogger = this.mockRepository.Create<ILogger<RemoveEntitlementsFromProductionCommandHandler>>();
            this.mockRepositoryFactory = new MockObjectRepositoryFactory();
            this.mockEntitlementRepository = mockRepositoryFactory.ResolveMock<GmsEntitlement>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mapper = new MapperConfiguration(cfg =>
            {
                cfg.ShouldMapMethod = (m => false);
                cfg.AddProfile(new TvpActorProfile());
                cfg.AddProfile(new TvpProfile());
            }).CreateMapper();
        }

        /// <summary>
        /// Gets the RemoveEntitlementsFromProductionCommandHandler.
        /// </summary>
        private RemoveEntitlementsFromProductionCommandHandler RemoveEntitlementsFromProductionCommandHandler =>
            new RemoveEntitlementsFromProductionCommandHandler(
                this.mockTvpClientService.Object,
                this.mockLogger.Object,
                this.mapper,
                this.mockRepositoryFactory,
                this.mockTelemetryService.Object);

        /// <summary>
        /// Handle with event and service collection, removes production from subscription and deletes production.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_DeletesProductionAsync()
        {
            // Arrange
            var command = GetRemoveEntitlementsFromProductionCommand();
            var eventId = command.ActorSpecificDetail.Data.EventExternalId;
            var tvpEvent = GetTvpEvent();
            var productionId = tvpEvent.Schedules.Single().Productions.Single().ExternalId;

            this.mockTvpClientService.Setup(x => x.GetEventByIdAsync(It.IsAny<string>(), It.IsAny<bool>())).ReturnsAsync(tvpEvent);

            // Act
            await this.RemoveEntitlementsFromProductionCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(eventId, true), Times.Once);
            this.mockTvpClientService.Verify(x => x.DeleteProductionByIdAsync(productionId), Times.Once);
            this.mockTvpClientService.Verify(x => x.UpdateEventAsync(eventId, It.Is<TvpEventUpdateInfo>(x => x.EventStatus == TvpEventStatus.Completed)), Times.Once);
            this.mockEntitlementRepository.Verify(x => x.DeleteItemAsync(eventId), Times.Once);

            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    eventId,
                    EventData.RemoveServiceCollectionFromSubscriptions,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == productionId),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    eventId,
                    EventData.TvpEventCompleted,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == TvpEventStatus.Completed.ToEnumString()),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle without event, logs error.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithoutEvent_LogsErrorAsync()
        {
            // Arrange
            var command = GetRemoveEntitlementsFromProductionCommand();

            // Act
            await this.RemoveEntitlementsFromProductionCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockLogger.VerifyAnyLogging(LogLevel.Error, Times.Once());
            this.mockTvpClientService.Verify(x => x.GetEventByIdAsync(command.ActorSpecificDetail.Data.EventExternalId, true), Times.Once);
            this.mockTvpClientService.Verify(x => x.RemoveServiceCollectionFromSubscriptionAsync(It.IsAny<string>(), It.IsAny<string>()), Times.Never);
            this.mockTvpClientService.Verify(x => x.DeleteProductionByIdAsync(It.IsAny<string>()), Times.Never);
            this.mockTvpClientService.Verify(x => x.UpdateEventAsync(It.IsAny<string>(), It.IsAny<TvpEventUpdateInfo>()), Times.Never);
            this.mockEntitlementRepository.Verify(x => x.DeleteItemAsync(It.IsAny<string>()), Times.Never);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Never);
        }

        /// <summary>
        /// Gets a TvpEvent with one production.
        /// </summary>
        /// <returns>A TvpEvent with one production.</returns>
        private static TvpEvent GetTvpEvent()
        {
            return new TvpEvent
            {
                Schedules = new List<TvpEventSchedule>
                {
                    new TvpEventSchedule
                    {
                        Productions = new List<TvpProduction>
                        {
                            new TvpProduction { ExternalId = "ExternalId" },
                        },
                    },
                },
            };
        }

        /// <summary>
        /// Gets the RemoveEntitlementsFromProductionCommand.
        /// </summary>
        /// <returns>The RemoveEntitlementsFromProductionCommand.</returns>
        private static RemoveEntitlementsFromProductionCommand GetRemoveEntitlementsFromProductionCommand()
        {
            return new RemoveEntitlementsFromProductionCommand
            {
                ActorSpecificDetail = new MockCleanupActorSpecificDetail
                {
                    Data = new TvpEventCleanupInfo { EventExternalId = "eventId" },
                },
            };
        }
    }
}
