// "//-----------------------------------------------------------------------".
// <copyright file="UpdateEventActualStartTimeCommandHandlerTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateEventActualStartTime
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.Shared.Application.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Interfaces.Services;
    using NBA.NextGen.VideoPlatform.Shared.Application.Tvp.Mappers;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Exceptions;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateEventActualStartTime;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="Application.UseCases.Tvp.Commands.UpdateEventActualStartTime.UpdateEventActualStartTimeCommandHandler"/>.
    /// </summary>
    public class UpdateEventActualStartTimeCommandHandlerTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mock TVP client service.
        /// </summary>
        private readonly Mock<ITvpClientService> mockTvpClientService;

        /// <summary>
        /// The mock logger.
        /// </summary>
        private readonly Mock<ILogger<UpdateEventActualStartTimeCommandHandler>> mockLogger;

        /// <summary>
        /// The mock telemetry service.
        /// </summary>
        private readonly Mock<ITelemetryService> mockTelemetryService;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateEventActualStartTimeCommandHandlerTests"/> class.
        /// </summary>
        public UpdateEventActualStartTimeCommandHandlerTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockTvpClientService = this.mockRepository.Create<ITvpClientService>();
            this.mockLogger = this.mockRepository.Create<ILogger<UpdateEventActualStartTimeCommandHandler>>();
            this.mockTelemetryService = this.mockRepository.Create<ITelemetryService>();
            this.mapper = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new TvpActorProfile());
                cfg.AddProfile(new TvpProfile());
            }).CreateMapper();
        }

        /// <summary>
        /// Gets the <see cref="UpdateEventActualStartTimeCommandHandler"/>.
        /// </summary>
        private UpdateEventActualStartTimeCommandHandler UpdateEventActualStartTimeCommandHandler =>
            new UpdateEventActualStartTimeCommandHandler(
                this.mockTvpClientService.Object,
                this.mapper,
                this.mockLogger.Object,
                this.mockTelemetryService.Object);

        /// <summary>
        /// Handle with existing schedule, updates schedule.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithExistingSchedule_UpdatesScheduleAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateEventActualStartTimeCommand
            {
                LiveEventId = liveEventId,
            };
            var tvpSchedule = GetTvpSchedule();
            this.mockTvpClientService.Setup(x => x.GetEventScheduleByIdAsync(It.IsAny<string>())).ReturnsAsync(tvpSchedule);

            // Act
            await this.UpdateEventActualStartTimeCommandHandler.Handle(command, CancellationToken.None).ConfigureAwait(false);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventScheduleByIdAsync(liveEventId), Times.Once);
            this.mockTvpClientService.Verify(
                x => x.UpdateEventSchedulesAsync(liveEventId, liveEventId, It.Is<TvpEventScheduleUpdateInfo>(x =>
                    x.ActualStartUtc == tvpSchedule.ActualStartUtc
                    && x.EndUtc == tvpSchedule.EndUtc
                    && x.ExternalId == tvpSchedule.ExternalId
                    && x.Name == tvpSchedule.Name
                    && x.StartUtc == tvpSchedule.StartUtc
                    && x.Upid == tvpSchedule.Upid)), Times.Once);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    liveEventId,
                    EventTypes.TvpActualStartTimeUpdated,
                    It.Is<Dictionary<string, string>>(x => x[EventData.DetailTag] == command.ActualStartTime.ToString("O", CultureInfo.InvariantCulture)),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Once);
        }

        /// <summary>
        /// Handle with unexisting schedule throws TvpEntityNotFoundException.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task Handle_WithUnexistingSchedule_ThrowsTvpEntityNotFoundExceptionAsync()
        {
            // Arrange
            var liveEventId = "LiveEventId";
            var command = new UpdateEventActualStartTimeCommand
            {
                LiveEventId = liveEventId,
            };

            // Act
            await Assert.ThrowsAsync<TvpEntityNotFoundException<TvpEventSchedule>>(() => this.UpdateEventActualStartTimeCommandHandler.Handle(command, CancellationToken.None)).ConfigureAwait(true);

            // Assert
            this.mockTvpClientService.Verify(x => x.GetEventScheduleByIdAsync(liveEventId), Times.Once);
            this.mockTvpClientService.Verify(x => x.UpdateEventSchedulesAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<TvpEventScheduleUpdateInfo>()), Times.Never);
            this.mockTelemetryService.Verify(
                x => x.TrackEvent(
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<Dictionary<string, string>>(),
                    It.IsAny<Dictionary<string, double>>(),
                    It.IsAny<string>(),
                    It.IsAny<int>(),
                    It.IsAny<string>()),
                Times.Never);
        }

        /// <summary>
        /// Gets a <see cref="TvpEventSchedule"/>.
        /// </summary>
        /// <returns>The <see cref="TvpEventSchedule"/>.</returns>
        private static TvpEventSchedule GetTvpSchedule()
        {
            return new TvpEventSchedule
            {
                ActualEndUtc = DateTimeOffset.UtcNow,
                ActualStartUtc = DateTimeOffset.UtcNow,
                EndUtc = DateTimeOffset.UtcNow,
                ExternalId = "LiveEventId",
                Name = "Name",
                Productions = null,
                StartUtc = DateTimeOffset.UtcNow,
                Upid = "Upid",
            };
        }
    }
}
