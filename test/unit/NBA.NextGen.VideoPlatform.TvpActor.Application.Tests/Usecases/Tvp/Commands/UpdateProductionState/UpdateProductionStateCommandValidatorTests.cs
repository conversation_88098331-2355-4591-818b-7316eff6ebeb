// "//-----------------------------------------------------------------------".
// <copyright file="UpdateProductionStateCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpdateProductionState
{
    using NBA.NextGen.Shared.Unit;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionState;
    using Xunit;

    /// <summary>
    /// Tests for <see cref="UpdateProductionStateCommandValidator"/>.
    /// </summary>
    public class UpdateProductionStateCommandValidatorTests : BaseUnitTest
    {
        /// <summary>
        /// The validator.
        /// </summary>
        private readonly UpdateProductionStateCommandValidator validator;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpdateProductionStateCommandValidatorTests"/> class.
        /// </summary>
        public UpdateProductionStateCommandValidatorTests()
        {
            this.validator = new UpdateProductionStateCommandValidator();
        }

        /// <summary>
        /// Validates the input request parameters are correct.
        /// </summary>
        /// <param name="validPreviousState">The valid previous state.</param>
        [Theory]
        [InlineData(null)]
        [InlineData("Created")]
        [InlineData("Init")]
        public void ValidateInput_ShouldPass(string validPreviousState)
        {
            // Arrange
            var request = new UpdateProductionStateCommand
            {
                ProductionId = "ProductionIdTest",
                ValidPreviousState = validPreviousState,
                State = TvpProductionStatus.Broadcast,
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }

        /// <summary>
        /// Validates the input request parameter should not be empty.
        /// </summary>
        [Fact]
        public void ValidateInput_ShouldFail()
        {
            // Arrange
            var request = new UpdateProductionStateCommand
            {
                ValidPreviousState = "InvalidValue",
            };

            // Act
            var result = this.validator.Validate(request);

            // Assert
            Assert.Equal(4, result.Errors.Count);
            Assert.Equal("ProductionId cannot be empty", result.Errors[0].ErrorMessage);
            Assert.Equal("State cannot be empty", result.Errors[1].ErrorMessage);
            Assert.Equal($"State provided does not match any existing {nameof(TvpProductionStatus)}", result.Errors[2].ErrorMessage);
            Assert.Equal($"ValidPreviousState provided must be null or match with a {nameof(TvpProductionStatus)} value", result.Errors[3].ErrorMessage);
        }
    }
}
