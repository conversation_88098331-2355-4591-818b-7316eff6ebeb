// "//-----------------------------------------------------------------------".
// <copyright file="UpsertLocationCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpsertLocation
{
    using System.Collections.Generic;
    using Moq;
    using NBA.NextGen.VideoPlatform.Shared.Domain.GMS.Entities;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertLocation;
    using Xunit;

    /// <summary>
    /// UpsertLocationCommandValidatorTests.
    /// </summary>
    public class UpsertLocationCommandValidatorTests
    {
        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="UpsertLocationCommandValidatorTests"/> class.
        /// </summary>
        public UpsertLocationCommandValidatorTests()
        {
            this.mockRepository = new MockRepository(MockBehavior.Strict);
        }

        /// <summary>
        /// Gets the validator.
        /// </summary>
        /// <value>
        /// The validator.
        /// </value>
        private UpsertLocationCommandValidator Validator => new UpsertLocationCommandValidator();

        /// <summary>
        /// Validates the input state under test expected behavior.
        /// </summary>
        [Fact]
        public void ValidateInput_StateUnderTest_ExpectedBehavior()
        {
            TvpEventLocationInfo eventLocation = new TvpEventLocationInfo
            {
                LocationId = "123",
                LocationType = "Arena",
                Name = "Capital One Arena",
                City = "Washington",
                CountryCode = "US",
                Postal = "20004",
                Street = "601 F St NW",
                StateOrProvince = "DC",
            };

            // Arrange
            UpsertLocationCommand addLocationCommand = new UpsertLocationCommand
            {
                LongRunningOperationId = "123",
                ActorSpecificDetail = new MockCreationActorSpecificDetail
                {
                    ActorId = "6ccf0359-baaf-44c5-bcd3-d6d3d8ba9b61",
                    Data = new Shared.Domain.TVP.Models.TvpEventCreationInfo()
                    {
                        EventExternalId = "989086c988",
                        EventName = "Hometeam-test-Orchestration",
                        EventScheduleName = "Hometeam-test-Orchestration",
                        EventTeamRoles = new List<TvpEventTeamCreationInfo>
                        {
                            new TvpEventTeamCreationInfo
                            {
                                ExternalId = "Test123",
                                RoleType = "home",
                            },
                            new TvpEventTeamCreationInfo
                            {
                                ExternalId = "Test124",
                                RoleType = "away",
                            },
                        },
                        EventLocation = eventLocation,
                    },
                },
            };

            // Act
            var result = this.Validator.Validate(addLocationCommand);

            // Assert
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
