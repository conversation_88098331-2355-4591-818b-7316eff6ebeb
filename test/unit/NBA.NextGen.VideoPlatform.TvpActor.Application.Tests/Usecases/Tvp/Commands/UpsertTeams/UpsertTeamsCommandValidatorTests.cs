// "//-----------------------------------------------------------------------".
// <copyright file="UpsertTeamsCommandValidatorTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Application.Tests.Usecases.Tvp.Commands.UpsertTeams
{
    using System.Collections.Generic;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddTeams;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpsertTeams;
    using Xunit;

    /// <summary>
    /// Tests for UpsertTeamsCommandValidator.
    /// </summary>
    public class UpsertTeamsCommandValidatorTests
    {
        /// <summary>
        /// Gets the send channel state change request acknowldgement command validator.
        /// </summary>
        /// <value>
        /// The send channel state change request acknowldgement command validator.
        /// </value>
        private UpsertTeamsCommandValidator UpsertTeamsCommandValidator => new UpsertTeamsCommandValidator();

        /// <summary>
        /// Request parameter should not be empty.
        /// </summary>
        [Fact]
        public void RequestParameterShouldNotBeEmpty()
        {
            // Arrange
            var request = new UpsertTeamsCommand
            {
                TvpEventTeamCreationInfos = null,
            };

            // Act
            var result = this.UpsertTeamsCommandValidator.Validate(request);

            // Assert
            Assert.False(result.IsValid);
            Assert.Equal(1, result.Errors.Count);
            Assert.Equal($"{nameof(UpsertTeamsCommand.TvpEventTeamCreationInfos)} cannot be null", result.Errors[0].ErrorMessage);
        }

        /// <summary>
        /// All the validations should pass with valid request.
        /// </summary>
        [Fact]
        public void AllValidationsShouldPassWithValidRequest()
        {
            // Arrange
            var request = new UpsertTeamsCommand
            {
                 TvpEventTeamCreationInfos = new List<TvpEventTeamCreationInfo>(),
            };

            // Act
            var result = this.UpsertTeamsCommandValidator.Validate(request);

            // Assert
            Assert.True(result.IsValid);
            Assert.Equal(0, result.Errors.Count);
        }
    }
}
