// "//-----------------------------------------------------------------------".
// <copyright file="StateChangeNotifierTests.cs" company="Microsoft and NBA Media Ventures">
// Copyright (c) 2021 Microsoft and NBA Media Ventures. All rights reserved.
// </copyright>
// "//-----------------------------------------------------------------------".

namespace NBA.NextGen.VideoPlatform.TvpActor.Function.Tests
{
    using System;
    using System.Threading;
    using System.Threading.Tasks;
    using AutoMapper;
    using MediatR;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.ContextImplementations;
    using Microsoft.Azure.WebJobs.Extensions.DurableTask.Options;
    using Microsoft.Extensions.Logging;
    using Moq;
    using NBA.NextGen.VideoPlatform.Shared.Application.Common.Interfaces;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Common;
    using NBA.NextGen.VideoPlatform.Shared.Domain.Models;
    using NBA.NextGen.VideoPlatform.Shared.Domain.TVP.Models;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.Mappers;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.AddEntitlementsToProduction;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.NotifyState;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Commands.UpdateProductionStatus;
    using NBA.NextGen.VideoPlatform.TvpActor.Application.UseCases.Tvp.Queries;
    using NBA.NextGen.VideoPlatform.TvpActor.Domain.Common;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using Xunit;

    /// <summary>
    /// StateChangeNotifierTests.
    /// </summary>
    public class StateChangeNotifierTests
    {
        /// <summary>
        /// The mock mediator.
        /// </summary>
        private readonly Mock<IMediator> mockMediator;

        /// <summary>
        /// The logger.
        /// </summary>
        private readonly Mock<ILogger<StateChangeNotifier>> mockLogger;

        /// <summary>
        /// The mock repository.
        /// </summary>
        private readonly MockRepository mockRepository;

        /// <summary>
        /// The mapper.
        /// </summary>
        private readonly IMapper mapper;

        /// <summary>
        /// The mockVideoPlatformCorrelationProviderFactory.
        /// </summary>
        private readonly Mock<IVideoPlatformCorrelationProviderFactory> mockVideoPlatformCorrelationProviderFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="StateChangeNotifierTests"/> class.
        /// </summary>
        public StateChangeNotifierTests()
        {
            // configure automapper
            this.mockLogger = new Mock<ILogger<StateChangeNotifier>>();
            this.mockRepository = new MockRepository(MockBehavior.Loose);
            this.mockMediator = this.mockRepository.Create<IMediator>();
            this.mapper = new MapperConfiguration(cfg => cfg.AddProfile(new TvpActorProfile())).CreateMapper();
            this.mockVideoPlatformCorrelationProviderFactory = this.mockRepository.Create<IVideoPlatformCorrelationProviderFactory>();
        }

        /// <summary>
        /// Gets the state change notifier.
        /// </summary>
        /// <value>
        /// The state change notifier.
        /// </value>
        private StateChangeNotifier StateChangeNotifier => new StateChangeNotifier(this.mockMediator.Object, this.mockLogger.Object, this.mapper, this.mockVideoPlatformCorrelationProviderFactory.Object);

        /// <summary>
        /// Tests the run notify state change asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task TestRun_NotifyStateChangeAsync()
        {
            // Arrange.
            TvpEcmsNotificationInfo message = new TvpEcmsNotificationInfo
            {
                ProductionId = "newID",
            };
            this.mockMediator.Setup(m => m.Send(It.IsAny<NotifyTvpStateChangeCommand>(), It.IsAny<CancellationToken>())).Returns(Unit.Task);

            // Act.
            await this.StateChangeNotifier.NotifyStateChangeAsync(message).ConfigureAwait(false);

            // Assert.
            this.mockMediator.Verify(x => x.Send(It.Is<NotifyTvpStateChangeCommand>(x => x.ProductionId == message.ProductionId), CancellationToken.None), Times.Once);
        }

        /// <summary>
        /// Tests the run notify state change asynchronous.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous unit test.</returns>
        [Fact]
        public async Task TestRun_NotifyStateChange_WhenProductionIsNull_Async()
        {
            // Arrange.
            TvpEcmsNotificationInfo message = new TvpEcmsNotificationInfo
            {
                EventId = "newID",
            };
            this.mockMediator.Setup(m => m.Send(It.IsAny<NotifyTvpStateChangeCommand>(), It.IsAny<CancellationToken>())).Returns(Unit.Task);

            // Act.
            await this.StateChangeNotifier.NotifyStateChangeAsync(message).ConfigureAwait(false);

            // Assert.
            this.mockMediator.Verify(x => x.Send(It.Is<NotifyTvpStateChangeCommand>(x => x.EventId == message.EventId), CancellationToken.None), Times.Once);
        }
    }
}
